/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * ملف JavaScript الخاص بإدارة الطلاب للعاملين
 */

// المتغيرات العامة
let currentUser = null;       // المستخدم الحالي (العامل)
let students = [];            // قائمة الطلاب
let filteredStudents = [];    // قائمة الطلاب بعد التصفية
let currentPage = 1;          // الصفحة الحالية
const studentsPerPage = 12;   // عدد الطلاب في كل صفحة

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تحميل بيانات المستخدم الحالي
    loadCurrentUser();

    // تحميل بيانات الطلاب
    loadStudents();

    // تهيئة الأحداث
    initializeEventListeners();
});

/**
 * تحميل بيانات المستخدم الحالي
 */
function loadCurrentUser() {
    try {
        // الحصول على بيانات المستخدم من sessionStorage
        const userJson = sessionStorage.getItem('currentUser');
        if (userJson) {
            currentUser = JSON.parse(userJson);
            
            // عرض اسم المستخدم في الواجهة
            const userNameElement = document.getElementById('user-name');
            if (userNameElement && currentUser.name) {
                userNameElement.innerHTML = `مرحبًا، <strong>${currentUser.name}</strong>`;
            }
            
            // عرض التاريخ الحالي
            const currentDateElement = document.getElementById('current-date');
            if (currentDateElement) {
                const now = new Date();
                const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
            }
            
            // التحقق من صلاحيات المستخدم
            if (!currentUser.role || currentUser.role !== 'staff') {
                alert('ليس لديك صلاحية الوصول إلى هذه الصفحة');
                window.location.href = '../auth/login.html';
            }
        } else {
            // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول إذا لم يكن مسجل الدخول
            alert('يرجى تسجيل الدخول أولاً');
            window.location.href = '../auth/login.html';
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدم:', error);
        alert('حدث خطأ أثناء تحميل بيانات المستخدم');
    }
}

/**
 * تحميل بيانات الطلاب
 */
function loadStudents() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();
        
        // تحميل الطلاب للمدرسة الحالية
        if (currentUser && currentUser.schoolId) {
            students = db.users.filter(user => 
                user.role === 'student' && 
                user.schoolId === currentUser.schoolId
            );
            
            // ترتيب الطلاب حسب الاسم
            students.sort((a, b) => a.name.localeCompare(b.name));
            
            // تعيين قائمة الطلاب المصفاة
            filteredStudents = [...students];
            
            // عرض الطلاب في الواجهة
            renderStudents();
            
            // عرض أزرار الصفحات
            renderPagination();
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الطلاب:', error);
    }
}

/**
 * تهيئة مستمعي الأحداث
 */
function initializeEventListeners() {
    try {
        // حقل البحث
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterStudents();
            });
        }
        
        // قائمة التصفية حسب الصف
        const gradeFilter = document.getElementById('grade-filter');
        if (gradeFilter) {
            gradeFilter.addEventListener('change', function() {
                filterStudents();
            });
        }
        
        // أزرار إغلاق النوافذ المنبثقة
        const closeButtons = document.querySelectorAll('.close-btn');
        if (closeButtons.length > 0) {
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const modal = this.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            });
        }
        
        // إغلاق النوافذ المنبثقة عند النقر خارجها
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    } catch (error) {
        console.error('خطأ في تهيئة مستمعي الأحداث:', error);
    }
}

/**
 * تصفية الطلاب حسب معايير البحث والتصفية
 */
function filterStudents() {
    try {
        // الحصول على قيمة البحث
        const searchValue = document.getElementById('search-input').value.trim().toLowerCase();
        
        // الحصول على قيمة التصفية حسب الصف
        const gradeValue = document.getElementById('grade-filter').value;
        
        // تصفية الطلاب
        filteredStudents = students.filter(student => {
            // تصفية حسب النص المدخل
            const matchesSearch = searchValue === '' || 
                student.name.toLowerCase().includes(searchValue) || 
                (student.noorId && student.noorId.toLowerCase().includes(searchValue));
            
            // تصفية حسب الصف
            const matchesGrade = gradeValue === 'all' || student.grade === gradeValue;
            
            return matchesSearch && matchesGrade;
        });
        
        // إعادة تعيين الصفحة الحالية
        currentPage = 1;
        
        // عرض الطلاب في الواجهة
        renderStudents();
        
        // عرض أزرار الصفحات
        renderPagination();
    } catch (error) {
        console.error('خطأ في تصفية الطلاب:', error);
    }
}

/**
 * عرض الطلاب في الواجهة
 */
function renderStudents() {
    try {
        const studentsGrid = document.getElementById('students-grid');
        if (!studentsGrid) return;
        
        // حساب الطلاب للصفحة الحالية
        const startIndex = (currentPage - 1) * studentsPerPage;
        const endIndex = startIndex + studentsPerPage;
        const studentsToShow = filteredStudents.slice(startIndex, endIndex);
        
        // مسح العناصر الحالية
        studentsGrid.innerHTML = '';
        
        // إذا لم تكن هناك نتائج
        if (studentsToShow.length === 0) {
            studentsGrid.innerHTML = '<div class="no-results">لا توجد نتائج مطابقة</div>';
            return;
        }
        
        // الحصول على قاعدة البيانات
        const db = getDatabase();
        
        // إضافة بطاقات الطلاب
        studentsToShow.forEach(student => {
            // الحصول على معلومات المدرسة
            const school = db.schools.find(s => s.id === student.schoolId);
            
            // إنشاء بطاقة الطالب
            const studentCard = document.createElement('div');
            studentCard.className = 'student-card';
            studentCard.setAttribute('data-student-id', student.id);
            
            // إنشاء محتوى البطاقة
            studentCard.innerHTML = `
                <div class="student-header">
                    <div class="student-avatar">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="student-info">
                        <div class="student-name">${student.name}</div>
                        <div class="student-id">رقم نور: ${student.noorId || 'غير متوفر'}</div>
                    </div>
                </div>
                <div class="student-details">
                    <div class="student-detail-row">
                        <div class="student-detail-label">المدرسة:</div>
                        <div class="student-detail-value">${school ? school.name : 'غير معروف'}</div>
                    </div>
                    <div class="student-detail-row">
                        <div class="student-detail-label">الصف:</div>
                        <div class="student-detail-value">${getGradeText(student.grade)}</div>
                    </div>
                    <div class="student-detail-row">
                        <div class="student-detail-label">الرصيد:</div>
                        <div class="student-detail-value student-balance">${student.balance || 0} ريال</div>
                    </div>
                    ${student.allergies && student.allergies.length > 0 ? `
                    <div class="student-detail-row">
                        <div class="student-detail-label">الحساسيات:</div>
                        <div class="student-detail-value">
                            <div class="student-allergies">
                                ${student.allergies.map(allergy => `
                                    <div class="allergy-badge" title="${allergy.description || ''}">
                                        <i class="${getAllergyIcon(allergy.name)}"></i>
                                        ${allergy.name}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
                <div class="student-actions">
                    <button class="student-btn student-btn-secondary view-details-btn" data-student-id="${student.id}">
                        <i class="fas fa-eye"></i> عرض التفاصيل
                    </button>
                    <button class="student-btn student-btn-primary add-to-pos-btn" data-student-id="${student.id}">
                        <i class="fas fa-cash-register"></i> إضافة للبيع
                    </button>
                </div>
            `;
            
            // إضافة البطاقة إلى الشبكة
            studentsGrid.appendChild(studentCard);
        });
        
        // إضافة مستمعي الأحداث للأزرار
        addButtonEventListeners();
    } catch (error) {
        console.error('خطأ في عرض الطلاب:', error);
    }
}

/**
 * عرض أزرار الصفحات
 */
function renderPagination() {
    try {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;
        
        // حساب عدد الصفحات
        const totalPages = Math.ceil(filteredStudents.length / studentsPerPage);
        
        // مسح العناصر الحالية
        paginationContainer.innerHTML = '';
        
        // إذا كان هناك صفحة واحدة فقط، لا داعي لعرض أزرار الصفحات
        if (totalPages <= 1) return;
        
        // إضافة زر الصفحة السابقة
        const prevButton = document.createElement('button');
        prevButton.className = 'pagination-btn';
        prevButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
        prevButton.disabled = currentPage === 1;
        prevButton.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                renderStudents();
                renderPagination();
            }
        });
        paginationContainer.appendChild(prevButton);
        
        // إضافة أزرار الصفحات
        for (let i = 1; i <= totalPages; i++) {
            // إذا كان عدد الصفحات كبيرًا، عرض الصفحات المهمة فقط
            if (totalPages > 7) {
                if (
                    i === 1 || 
                    i === totalPages || 
                    (i >= currentPage - 1 && i <= currentPage + 1)
                ) {
                    // عرض الصفحة
                } else if (i === currentPage - 2 || i === currentPage + 2) {
                    // عرض نقاط الحذف
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'pagination-ellipsis';
                    ellipsis.textContent = '...';
                    paginationContainer.appendChild(ellipsis);
                    continue;
                } else {
                    // تخطي الصفحات الأخرى
                    continue;
                }
            }
            
            const pageButton = document.createElement('button');
            pageButton.className = 'pagination-btn' + (i === currentPage ? ' active' : '');
            pageButton.textContent = i;
            pageButton.addEventListener('click', function() {
                currentPage = i;
                renderStudents();
                renderPagination();
            });
            paginationContainer.appendChild(pageButton);
        }
        
        // إضافة زر الصفحة التالية
        const nextButton = document.createElement('button');
        nextButton.className = 'pagination-btn';
        nextButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
        nextButton.disabled = currentPage === totalPages;
        nextButton.addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                renderStudents();
                renderPagination();
            }
        });
        paginationContainer.appendChild(nextButton);
    } catch (error) {
        console.error('خطأ في عرض أزرار الصفحات:', error);
    }
}

/**
 * إضافة مستمعي الأحداث للأزرار
 */
function addButtonEventListeners() {
    try {
        // أزرار عرض التفاصيل
        const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
        if (viewDetailsButtons.length > 0) {
            viewDetailsButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const studentId = parseInt(this.getAttribute('data-student-id'));
                    showStudentDetails(studentId);
                });
            });
        }
        
        // أزرار إضافة للبيع
        const addToPosButtons = document.querySelectorAll('.add-to-pos-btn');
        if (addToPosButtons.length > 0) {
            addToPosButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const studentId = parseInt(this.getAttribute('data-student-id'));
                    addStudentToPos(studentId);
                });
            });
        }
    } catch (error) {
        console.error('خطأ في إضافة مستمعي الأحداث للأزرار:', error);
    }
}

/**
 * عرض تفاصيل الطالب
 * @param {number} studentId - معرف الطالب
 */
function showStudentDetails(studentId) {
    try {
        // الحصول على بيانات الطالب
        const db = getDatabase();
        const student = db.users.find(user => user.id === studentId);
        
        if (!student) {
            alert('لم يتم العثور على الطالب');
            return;
        }
        
        // الحصول على معلومات المدرسة
        const school = db.schools.find(s => s.id === student.schoolId);
        
        // الحصول على معلومات ولي الأمر
        const parent = db.users.find(user => user.id === student.parentId);
        
        // الحصول على طلبات الطالب
        const orders = db.orders.filter(order => order.userId === student.id);
        
        // إنشاء محتوى التفاصيل
        const detailsContainer = document.getElementById('student-details-container');
        if (!detailsContainer) return;
        
        detailsContainer.innerHTML = `
            <div class="student-details-header">
                <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="student-info">
                    <h2>${student.name}</h2>
                    <p>رقم نور: ${student.noorId || 'غير متوفر'}</p>
                </div>
            </div>
            
            <div class="student-details-section">
                <h3>المعلومات الأساسية</h3>
                <div class="details-grid">
                    <div class="details-item">
                        <div class="details-label">المدرسة:</div>
                        <div class="details-value">${school ? school.name : 'غير معروف'}</div>
                    </div>
                    <div class="details-item">
                        <div class="details-label">الصف:</div>
                        <div class="details-value">${getGradeText(student.grade)}</div>
                    </div>
                    <div class="details-item">
                        <div class="details-label">الرصيد:</div>
                        <div class="details-value student-balance">${student.balance || 0} ريال</div>
                    </div>
                    <div class="details-item">
                        <div class="details-label">ولي الأمر:</div>
                        <div class="details-value">${parent ? parent.name : 'غير معروف'}</div>
                    </div>
                </div>
            </div>
            
            ${student.allergies && student.allergies.length > 0 ? `
            <div class="student-details-section">
                <h3>الحساسيات</h3>
                <div class="allergies-list">
                    ${student.allergies.map(allergy => `
                        <div class="allergy-item">
                            <div class="allergy-header">
                                <i class="${getAllergyIcon(allergy.name)}"></i>
                                <span>${allergy.name}</span>
                                <span class="allergy-severity ${allergy.severity}">${getSeverityText(allergy.severity)}</span>
                            </div>
                            <div class="allergy-description">${allergy.description || 'لا يوجد وصف'}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}
            
            <div class="student-details-section">
                <h3>آخر الطلبات</h3>
                ${orders.length > 0 ? `
                <div class="orders-table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المنتجات</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${orders.slice(0, 5).map(order => `
                                <tr>
                                    <td>#${order.id}</td>
                                    <td>${formatDate(order.date)}</td>
                                    <td>${getOrderProductsText(order, db)}</td>
                                    <td>${order.totalPrice} ريال</td>
                                    <td><span class="order-status ${order.status}">${getStatusText(order.status)}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ` : '<div class="no-orders">لا توجد طلبات سابقة</div>'}
            </div>
            
            <div class="student-details-actions">
                <button class="student-btn student-btn-primary add-to-pos-btn" data-student-id="${student.id}">
                    <i class="fas fa-cash-register"></i> إضافة للبيع
                </button>
            </div>
        `;
        
        // إضافة مستمع حدث لزر إضافة للبيع
        const addToPosBtn = detailsContainer.querySelector('.add-to-pos-btn');
        if (addToPosBtn) {
            addToPosBtn.addEventListener('click', function() {
                addStudentToPos(studentId);
            });
        }
        
        // عرض النافذة المنبثقة
        const modal = document.getElementById('student-details-modal');
        if (modal) {
            modal.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في عرض تفاصيل الطالب:', error);
    }
}

/**
 * إضافة الطالب إلى نظام نقاط البيع
 * @param {number} studentId - معرف الطالب
 */
function addStudentToPos(studentId) {
    try {
        // تخزين معرف الطالب في sessionStorage
        sessionStorage.setItem('selectedStudentId', studentId);
        
        // الانتقال إلى صفحة نقاط البيع
        window.location.href = 'pos.html';
    } catch (error) {
        console.error('خطأ في إضافة الطالب إلى نظام نقاط البيع:', error);
    }
}

/**
 * الحصول على نص الصف
 * @param {string} grade - رقم الصف
 * @returns {string} - نص الصف
 */
function getGradeText(grade) {
    switch (grade) {
        case '1': return 'الصف الأول';
        case '2': return 'الصف الثاني';
        case '3': return 'الصف الثالث';
        case '4': return 'الصف الرابع';
        case '5': return 'الصف الخامس';
        case '6': return 'الصف السادس';
        default: return grade || 'غير معروف';
    }
}

/**
 * الحصول على نص شدة الحساسية
 * @param {string} severity - شدة الحساسية
 * @returns {string} - نص شدة الحساسية
 */
function getSeverityText(severity) {
    switch (severity) {
        case 'high': return 'شديدة';
        case 'medium': return 'متوسطة';
        case 'low': return 'خفيفة';
        default: return severity || 'غير معروف';
    }
}

/**
 * الحصول على نص حالة الطلب
 * @param {string} status - حالة الطلب
 * @returns {string} - نص حالة الطلب
 */
function getStatusText(status) {
    switch (status) {
        case 'completed': return 'مكتمل';
        case 'pending': return 'معلق';
        case 'cancelled': return 'ملغي';
        default: return status || 'غير معروف';
    }
}

/**
 * تنسيق التاريخ
 * @param {string} dateString - سلسلة التاريخ
 * @returns {string} - التاريخ المنسق
 */
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error) {
        console.error('خطأ في تنسيق التاريخ:', error);
        return dateString;
    }
}

/**
 * الحصول على نص منتجات الطلب
 * @param {Object} order - الطلب
 * @param {Object} db - قاعدة البيانات
 * @returns {string} - نص المنتجات
 */
function getOrderProductsText(order, db) {
    try {
        if (!order.items || order.items.length === 0) {
            return 'لا توجد منتجات';
        }
        
        // إذا كان هناك أكثر من 2 منتجات، نعرض أول منتجين فقط
        if (order.items.length > 2) {
            const firstTwoItems = order.items.slice(0, 2);
            const remainingCount = order.items.length - 2;
            
            const itemsText = firstTwoItems.map(item => {
                const product = db.products.find(p => p.id === item.productId);
                return `${product ? product.name : 'منتج غير معروف'} (${item.quantity})`;
            }).join('، ');
            
            return `${itemsText} و${remainingCount} أخرى`;
        } else {
            // عرض جميع المنتجات
            return order.items.map(item => {
                const product = db.products.find(p => p.id === item.productId);
                return `${product ? product.name : 'منتج غير معروف'} (${item.quantity})`;
            }).join('، ');
        }
    } catch (error) {
        console.error('خطأ في الحصول على نص منتجات الطلب:', error);
        return 'خطأ في عرض المنتجات';
    }
}

/**
 * الحصول على أيقونة الحساسية المناسبة
 * @param {string} allergyName - اسم الحساسية
 * @returns {string} - اسم الأيقونة
 */
function getAllergyIcon(allergyName) {
    try {
        const allergyName_lower = allergyName.toLowerCase();
        
        if (allergyName_lower.includes('حليب') || allergyName_lower.includes('لبن') || allergyName_lower.includes('جبن')) {
            return 'fas fa-cheese';
        } else if (allergyName_lower.includes('فول') || allergyName_lower.includes('مكسرات')) {
            return 'fas fa-seedling';
        } else if (allergyName_lower.includes('بيض')) {
            return 'fas fa-egg';
        } else if (allergyName_lower.includes('سمك') || allergyName_lower.includes('بحر')) {
            return 'fas fa-fish';
        } else if (allergyName_lower.includes('قمح') || allergyName_lower.includes('خبز')) {
            return 'fas fa-bread-slice';
        } else if (allergyName_lower.includes('فراولة') || allergyName_lower.includes('توت')) {
            return 'fas fa-apple-alt';
        } else {
            return 'fas fa-allergies';
        }
    } catch (error) {
        console.error('خطأ في الحصول على أيقونة الحساسية:', error);
        return 'fas fa-allergies';
    }
}
