<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلباتي - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 6px 0;
            min-height: 45px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* لوجو الشوكة والسكينة */
        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 10px;
            color: white;
            font-size: 1.4rem;
            box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .compact-header .logo h1 {
            font-size: 1.1rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .compact-header .user-menu span {
            font-size: 0.9rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        .dashboard {
            display: flex;
            min-height: calc(100vh - 50px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group label {
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .status-pending {
            background-color: #ffeaa7;
            color: #d35400;
        }

        .status-processing {
            background-color: #81ecec;
            color: #00b894;
        }

        .status-completed {
            background-color: #55efc4;
            color: #00b894;
        }

        .status-cancelled {
            background-color: #fab1a0;
            color: #d63031;
        }

        .type-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .preorder-badge {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }

        .regular-badge {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .cancel-btn {
            background-color: #f44336;
            color: white;
        }

        .order-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }

        .order-details h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .order-products {
            margin-bottom: 15px;
        }

        .order-product {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #ddd;
        }

        .order-product:last-child {
            border-bottom: none;
        }

        .order-total {
            font-weight: bold;
            text-align: left;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 0;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #666;
        }

        .empty-state p {
            color: #888;
            margin-bottom: 20px;
        }

        .empty-state .btn {
            display: inline-block;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .filter-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header compact-header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>أحمد محمد</strong></span>
                <button id="header-logout-btn" class="header-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>الطالب</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> المنتجات</a></li>
                <li><a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="profile.html"><i class="fas fa-user"></i> الملف الشخصي</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>طلباتي</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <div class="filter-bar">
                    <div class="filter-group">
                        <label for="status-filter">تصفية حسب الحالة:</label>
                        <select id="status-filter">
                            <option value="all">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="processing">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date-filter">تصفية حسب التاريخ:</label>
                        <select id="date-filter">
                            <option value="all">جميع الأوقات</option>
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="orders-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>النوع</th>
                                <th>المنتجات</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-tbody">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>

                <div id="empty-state" class="empty-state" style="display: none;">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>لا توجد طلبات</h3>
                    <p>لم تقم بإنشاء أي طلبات بعد.</p>
                    <a href="products.html" class="btn btn-primary">تصفح المنتجات</a>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is student
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'student') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Get student orders from database
            let orders = db.orders.filter(order => order.userId === currentUser.id);

            // Get preorders from localStorage
            let preorders = JSON.parse(localStorage.getItem('preorders')) || [];
            let studentPreorders = preorders.filter(order => order.studentId === currentUser.id);

            // Convert preorders to match orders format
            const convertedPreorders = studentPreorders.map(preorder => ({
                id: preorder.id,
                userId: preorder.studentId,
                schoolId: preorder.schoolId,
                products: [{
                    productId: preorder.product.id,
                    quantity: preorder.quantity,
                    name: preorder.product.name,
                    price: preorder.product.price
                }],
                totalPrice: preorder.totalPrice,
                status: preorder.status,
                date: preorder.date,
                type: 'preorder',
                timestamp: preorder.timestamp
            }));

            // Combine orders and preorders
            orders = [...orders, ...convertedPreorders];

            // Sort by timestamp (newest first)
            orders.sort((a, b) => {
                const timeA = new Date(a.timestamp || a.date).getTime();
                const timeB = new Date(b.timestamp || b.date).getTime();
                return timeB - timeA;
            });

            // Render orders
            function renderOrders(orders) {
                const ordersTable = document.getElementById('orders-table');
                const ordersTbody = document.getElementById('orders-tbody');
                const emptyState = document.getElementById('empty-state');

                ordersTbody.innerHTML = '';

                if (orders.length === 0) {
                    ordersTable.style.display = 'none';
                    emptyState.style.display = 'block';
                    return;
                }

                ordersTable.style.display = 'table';
                emptyState.style.display = 'none';

                orders.forEach(order => {
                    // Get product names
                    let productNames;
                    if (order.type === 'preorder') {
                        // For preorders, use the stored product name
                        productNames = `${order.products[0].name} (${order.products[0].quantity})`;
                    } else {
                        // For regular orders, look up product names
                        productNames = order.products.map(p => {
                            const product = db.products.find(prod => prod.id === p.productId);
                            return `${product ? product.name : 'منتج غير معروف'} (${p.quantity})`;
                        }).join(', ');
                    }

                    // Determine order type
                    const orderType = order.type === 'preorder' ? 'طلب مسبق' : 'طلب عادي';
                    const orderTypeClass = order.type === 'preorder' ? 'preorder-badge' : 'regular-badge';

                    // Format date
                    let displayDate;
                    if (order.timestamp) {
                        displayDate = new Date(order.timestamp).toLocaleDateString('ar-SA');
                    } else {
                        displayDate = order.date;
                    }

                    const row = document.createElement('tr');
                    row.setAttribute('data-order-id', order.id);
                    row.innerHTML = `
                        <td>${order.id}</td>
                        <td><span class="type-badge ${orderTypeClass}">${orderType}</span></td>
                        <td>${productNames}</td>
                        <td>${order.totalPrice} ريال</td>
                        <td>${displayDate}</td>
                        <td><span class="status-badge status-${order.status}">${getOrderStatusText(order.status)}</span></td>
                        <td>
                            <button class="action-btn view-btn" data-id="${order.id}">عرض</button>
                            ${order.status === 'pending' ? `<button class="action-btn cancel-btn" data-id="${order.id}">إلغاء</button>` : ''}
                        </td>
                    `;

                    // Create order details row
                    const detailsRow = document.createElement('tr');
                    detailsRow.className = 'order-details-row';
                    detailsRow.style.display = 'none';

                    // Get detailed product information
                    let productDetails;
                    if (order.type === 'preorder') {
                        // For preorders, use stored product information
                        productDetails = [{
                            name: order.products[0].name,
                            price: order.products[0].price,
                            quantity: order.products[0].quantity,
                            total: order.totalPrice
                        }];
                    } else {
                        // For regular orders, look up product information
                        productDetails = order.products.map(p => {
                            const product = db.products.find(prod => prod.id === p.productId);
                            return {
                                name: product ? product.name : 'منتج غير معروف',
                                price: product ? product.price : 0,
                                quantity: p.quantity,
                                total: product ? product.price * p.quantity : 0
                            };
                        });
                    }

                    const productDetailsHTML = productDetails.map(p => `
                        <div class="order-product">
                            <div>${p.name} x ${p.quantity}</div>
                            <div>${p.total} ريال</div>
                        </div>
                    `).join('');

                    detailsRow.innerHTML = `
                        <td colspan="6">
                            <div class="order-details">
                                <h3>تفاصيل الطلب #${order.id}</h3>
                                <div class="order-products">
                                    ${productDetailsHTML}
                                </div>
                                <div class="order-total">
                                    المجموع: ${order.totalPrice} ريال
                                </div>
                            </div>
                        </td>
                    `;

                    ordersTbody.appendChild(row);
                    ordersTbody.appendChild(detailsRow);
                });

                // Add event listeners to view buttons
                document.querySelectorAll('.view-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        const detailsRow = this.closest('tr').nextElementSibling;
                        const detailsDiv = detailsRow.querySelector('.order-details');

                        if (detailsRow.style.display === 'none') {
                            detailsRow.style.display = 'table-row';
                            detailsDiv.style.display = 'block';
                        } else {
                            detailsRow.style.display = 'none';
                            detailsDiv.style.display = 'none';
                        }
                    });
                });

                // Add event listeners to cancel buttons
                document.querySelectorAll('.cancel-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const orderId = parseInt(this.getAttribute('data-id'));

                        if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                            // Find order in combined orders array
                            const order = orders.find(o => o.id === orderId);

                            if (order) {
                                if (order.type === 'preorder') {
                                    // Handle preorder cancellation
                                    let preorders = JSON.parse(localStorage.getItem('preorders')) || [];
                                    const preorderIndex = preorders.findIndex(p => p.id === orderId);

                                    if (preorderIndex !== -1) {
                                        // Update preorder status
                                        preorders[preorderIndex].status = 'cancelled';
                                        localStorage.setItem('preorders', JSON.stringify(preorders));

                                        // Refund amount to user balance
                                        currentUser.balance = (currentUser.balance || 0) + order.totalPrice;

                                        // Update user in localStorage
                                        let users = JSON.parse(localStorage.getItem('users')) || [];
                                        const userIndex = users.findIndex(u => u.id === currentUser.id);
                                        if (userIndex !== -1) {
                                            users[userIndex].balance = currentUser.balance;
                                            localStorage.setItem('users', JSON.stringify(users));
                                            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                                        }
                                    }
                                } else {
                                    // Handle regular order cancellation
                                    const orderIndex = db.orders.findIndex(o => o.id === orderId);

                                    if (orderIndex !== -1) {
                                        // Update order status
                                        db.orders[orderIndex].status = 'cancelled';

                                        // Refund amount to user balance
                                        const orderAmount = db.orders[orderIndex].totalPrice;
                                        currentUser.balance = (currentUser.balance || 0) + orderAmount;

                                        // Update user in database
                                        const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                                        if (userIndex !== -1) {
                                            db.users[userIndex].balance = currentUser.balance;
                                        }

                                        // Save changes
                                        saveDatabase(db);
                                        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                                    }
                                }

                                alert('تم إلغاء الطلب وإعادة المبلغ إلى رصيدك.');

                                // Refresh page to show updated orders
                                location.reload();
                            }
                        }
                    });
                });
            }

            // Initial render
            renderOrders(orders);

            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');

            function applyFilters() {
                const status = statusFilter.value;
                const dateRange = dateFilter.value;

                // Start with all orders (regular + preorders)
                let filteredOrders = [...orders];

                // Apply status filter
                if (status !== 'all') {
                    filteredOrders = filteredOrders.filter(order => order.status === status);
                }

                // Apply date filter
                if (dateRange !== 'all') {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    const weekStart = new Date(today);
                    weekStart.setDate(today.getDate() - today.getDay());

                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);

                    filteredOrders = filteredOrders.filter(order => {
                        let orderDate;
                        if (order.timestamp) {
                            orderDate = new Date(order.timestamp);
                        } else {
                            orderDate = new Date(order.date);
                        }
                        orderDate.setHours(0, 0, 0, 0);

                        if (dateRange === 'today') {
                            return orderDate.getTime() === today.getTime();
                        } else if (dateRange === 'week') {
                            return orderDate >= weekStart;
                        } else if (dateRange === 'month') {
                            return orderDate >= monthStart;
                        }

                        return true;
                    });
                }

                renderOrders(filteredOrders);
            }

            statusFilter.addEventListener('change', applyFilters);
            dateFilter.addEventListener('change', applyFilters);

            // Logout functionality - Sidebar button
            document.getElementById('logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });

            // Logout functionality - Header button
            document.getElementById('header-logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });
        });

        // Helper function to get order status text
        function getOrderStatusText(status) {
            switch(status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }
    </script>
</body>
</html>
