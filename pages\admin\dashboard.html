<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="../../index.html">
                    <img src="../../assets/images/logo-enhanced.svg" alt="شعار فواصل النجاح" class="logo-img">
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                </a>
            </div>
            <div class="user-menu">
                <div class="notifications-dropdown">
                    <div class="notifications-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notifications-count">3</span>
                    </div>
                    <div class="notifications-menu" id="notifications-menu">
                        <div class="notifications-header">
                            <h3>الإشعارات</h3>
                            <span class="mark-all-read">تعيين الكل كمقروء</span>
                        </div>
                        <div class="notifications-list" id="notifications-list">
                            <!-- سيتم ملء هذه القائمة ديناميكيًا -->
                        </div>
                        <div class="notification-footer">
                            <a href="notifications.html">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>
                <div class="dark-mode-toggle" id="dark-mode-toggle">
                    <i class="fas fa-moon"></i>
                </div>
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> <span>الرئيسية</span></a></li>
                <li><a href="schools.html"><i class="fas fa-school"></i> <span>إدارة المدارس</span></a></li>
                <li><a href="users.html"><i class="fas fa-users"></i> <span>إدارة المستخدمين</span></a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> <span>إدارة العاملين</span></a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> <span>إدارة المنتجات</span></a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> <span>إدارة الطلبات</span></a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> <span>التقارير</span></a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> <span>الإعدادات</span></a></li>
                <li><a href="logs.html"><i class="fas fa-history"></i> <span>سجل الأحداث</span></a></li>
                <li><a href="backup.html"><i class="fas fa-database"></i> <span>النسخ الاحتياطي</span></a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>لوحة التحكم</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon schools">
                        <i class="fas fa-school"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="schools-count">1</h3>
                        <p>المدارس</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon students">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="students-count">1</h3>
                        <p>الطلاب</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="products-count">3</h3>
                        <p>المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="orders-count">1</h3>
                        <p>الطلبات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon staff">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="staff-count">3</h3>
                        <p>العاملين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon parents">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-value" id="parents-count">1</h3>
                        <p>أولياء الأمور</p>
                    </div>
                </div>
            </div>

            <div class="content-row">
                <div class="content-card chart-card">
                    <h2>إحصائيات المبيعات <div class="card-actions"><button class="refresh-btn"><i class="fas fa-sync-alt"></i></button></div></h2>
                    <div class="chart-container">
                        <canvas id="sales-chart"></canvas>
                    </div>
                </div>

                <div class="content-card">
                    <h2>آخر الأنشطة <div class="card-actions"><a href="logs.html" class="view-all">عرض الكل <i class="fas fa-arrow-left"></i></a></div></h2>
                    <div class="activities-list" id="activities-list">
                        <!-- سيتم ملء هذه القائمة ديناميكيًا -->
                    </div>
                </div>
            </div>

            <div class="content-card">
                <h2>آخر المدارس المسجلة <div class="card-actions"><a href="schools.html" class="view-all">عرض الكل <i class="fas fa-arrow-left"></i></a></div></h2>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المدرسة</th>
                                <th>العنوان</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="schools-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="content-card">
                <h2>آخر الطلبات <div class="card-actions"><a href="orders.html" class="view-all">عرض الكل <i class="fas fa-arrow-left"></i></a></div></h2>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>المدرسة</th>
                                <th>الطالب</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- الزر العائم للإجراءات السريعة -->
    <div class="floating-btn" id="quick-actions-btn">
        <i class="fas fa-plus"></i>
    </div>

    <!-- قائمة الإجراءات السريعة -->
    <div class="quick-actions-menu" id="quick-actions-menu">
        <div class="quick-action" data-action="add-school">
            <i class="fas fa-school"></i>
            <span>إضافة مدرسة</span>
        </div>
        <div class="quick-action" data-action="add-user">
            <i class="fas fa-user-plus"></i>
            <span>إضافة مستخدم</span>
        </div>
        <div class="quick-action" data-action="add-staff">
            <i class="fas fa-user-tie"></i>
            <span>إضافة عامل</span>
        </div>
        <div class="quick-action" data-action="add-product">
            <i class="fas fa-box-open"></i>
            <span>إضافة منتج</span>
        </div>
        <div class="quick-action" data-action="view-reports">
            <i class="fas fa-chart-bar"></i>
            <span>عرض التقارير</span>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/admin-dashboard.js"></script>
    <script>
        // تهيئة الإشعارات
        function initializeNotifications() {
            try {
                const notificationsIcon = document.querySelector('.notifications-icon');
                const notificationsMenu = document.getElementById('notifications-menu');

                // إضافة مستمع حدث للنقر على أيقونة الإشعارات
                notificationsIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationsMenu.classList.toggle('show');
                });

                // إغلاق قائمة الإشعارات عند النقر في أي مكان آخر
                document.addEventListener('click', function() {
                    notificationsMenu.classList.remove('show');
                });

                // منع إغلاق القائمة عند النقر داخلها
                notificationsMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });

                // تعيين الكل كمقروء
                const markAllRead = document.querySelector('.mark-all-read');
                markAllRead.addEventListener('click', function() {
                    const db = getDatabase();

                    // تحديث حالة الإشعارات في قاعدة البيانات
                    if (db.notifications) {
                        db.notifications.forEach(notification => {
                            notification.read = true;
                        });

                        // حفظ التغييرات
                        saveDatabase(db);

                        // تحديث عرض الإشعارات
                        loadNotifications(db);

                        // إخفاء عدد الإشعارات
                        document.querySelector('.notifications-count').style.display = 'none';
                    }
                });
            } catch (error) {
                console.error('خطأ في تهيئة الإشعارات:', error);
            }
        }

        // تهيئة الوضع الليلي
        function initializeDarkMode() {
            try {
                const darkModeToggle = document.getElementById('dark-mode-toggle');
                const body = document.body;

                // التحقق من حالة الوضع الليلي المخزنة
                const isDarkMode = localStorage.getItem('darkMode') === 'true';

                // تطبيق الوضع الليلي إذا كان مفعلاً
                if (isDarkMode) {
                    body.classList.add('dark-mode');
                    darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                }

                // إضافة مستمع حدث للنقر على زر تبديل الوضع
                darkModeToggle.addEventListener('click', function() {
                    body.classList.toggle('dark-mode');

                    // تحديث الأيقونة
                    if (body.classList.contains('dark-mode')) {
                        darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                        localStorage.setItem('darkMode', 'true');
                    } else {
                        darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                        localStorage.setItem('darkMode', 'false');
                    }
                });
            } catch (error) {
                console.error('خطأ في تهيئة الوضع الليلي:', error);
            }
        }

        // تهيئة الإجراءات السريعة
        function initializeQuickActions() {
            try {
                const quickActionsBtn = document.getElementById('quick-actions-btn');
                const quickActionsMenu = document.getElementById('quick-actions-menu');

                // إضافة مستمع حدث للنقر على زر الإجراءات السريعة
                quickActionsBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    quickActionsMenu.classList.toggle('show');
                });

                // إغلاق قائمة الإجراءات السريعة عند النقر في أي مكان آخر
                document.addEventListener('click', function() {
                    quickActionsMenu.classList.remove('show');
                });

                // منع إغلاق القائمة عند النقر داخلها
                quickActionsMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });

                // إضافة مستمعي الأحداث للإجراءات
                const quickActions = document.querySelectorAll('.quick-action');
                quickActions.forEach(action => {
                    action.addEventListener('click', function() {
                        const actionType = this.getAttribute('data-action');

                        switch (actionType) {
                            case 'add-school':
                                window.location.href = 'school-add.html';
                                break;
                            case 'add-user':
                                window.location.href = 'user-add.html';
                                break;
                            case 'add-staff':
                                window.location.href = 'staff.html';
                                break;
                            case 'add-product':
                                window.location.href = 'product-add.html';
                                break;
                            case 'view-reports':
                                window.location.href = 'reports.html';
                                break;
                        }
                    });
                });
            } catch (error) {
                console.error('خطأ في تهيئة الإجراءات السريعة:', error);
            }
        }

        // إنشاء الرسوم البيانية
        function createCharts(db) {
            try {
                // إنشاء رسم بياني للمبيعات
                const salesChartCtx = document.getElementById('sales-chart').getContext('2d');

                // الحصول على بيانات المبيعات للأشهر الستة الماضية
                const salesData = getLastMonthsSales(db, 6);

                // إنشاء الرسم البياني
                const salesChart = new Chart(salesChartCtx, {
                    type: 'bar',
                    data: {
                        labels: salesData.labels,
                        datasets: [{
                            label: 'المبيعات (ريال)',
                            data: salesData.values,
                            backgroundColor: 'rgba(46, 125, 50, 0.7)',
                            borderColor: 'rgba(46, 125, 50, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'المبيعات (ريال)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'الشهر'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'المبيعات الشهرية',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('خطأ في إنشاء الرسوم البيانية:', error);
            }
        }

        // الحصول على بيانات المبيعات للأشهر الماضية
        function getLastMonthsSales(db, months) {
            try {
                const labels = [];
                const values = [];

                // الحصول على التاريخ الحالي
                const now = new Date();

                // إنشاء مصفوفة للأشهر الماضية
                for (let i = months - 1; i >= 0; i--) {
                    const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
                    const monthName = month.toLocaleDateString('ar-SA', { month: 'long' });
                    labels.push(monthName);

                    // حساب المبيعات لهذا الشهر
                    const monthSales = db.orders.reduce((sum, order) => {
                        const orderDate = new Date(order.date);
                        if (orderDate.getMonth() === month.getMonth() && orderDate.getFullYear() === month.getFullYear() && order.status === 'completed') {
                            return sum + order.totalPrice;
                        }
                        return sum;
                    }, 0);

                    values.push(monthSales);
                }

                return { labels, values };
            } catch (error) {
                console.error('خطأ في الحصول على بيانات المبيعات:', error);
                return { labels: [], values: [] };
            }
        }

        // تحميل الإشعارات
        function loadNotifications(db) {
            try {
                const notificationsList = document.getElementById('notifications-list');
                if (!notificationsList) return;

                // مسح المحتوى الحالي
                notificationsList.innerHTML = '';

                // إذا لم تكن هناك إشعارات، نعرض رسالة
                if (!db.notifications || db.notifications.length === 0) {
                    notificationsList.innerHTML = '<div class="notification-item">لا توجد إشعارات جديدة</div>';
                    return;
                }

                // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
                const sortedNotifications = [...db.notifications].sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);

                // تحديث عدد الإشعارات غير المقروءة
                const unreadCount = sortedNotifications.filter(notification => !notification.read).length;
                const notificationsCount = document.querySelector('.notifications-count');

                if (unreadCount > 0) {
                    notificationsCount.textContent = unreadCount;
                    notificationsCount.style.display = 'flex';
                } else {
                    notificationsCount.style.display = 'none';
                }

                // إضافة الإشعارات إلى القائمة
                sortedNotifications.forEach(notification => {
                    const notificationItem = document.createElement('div');
                    notificationItem.className = 'notification-item';

                    if (!notification.read) {
                        notificationItem.classList.add('unread');
                    }

                    notificationItem.innerHTML = `
                        <div class="notification-content">${notification.content}</div>
                        <div class="notification-time">${formatDateTime(notification.date)}</div>
                    `;

                    notificationsList.appendChild(notificationItem);
                });
            } catch (error) {
                console.error('خطأ في تحميل الإشعارات:', error);
            }
        }

        // إضافة مستمعي الأحداث لأزرار الإجراءات في جدول المدارس
        function addSchoolActionListeners() {
            try {
                // أزرار العرض
                document.querySelectorAll('#schools-table .view-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const schoolId = this.getAttribute('data-id');
                        window.location.href = `school-view.html?id=${schoolId}`;
                    });
                });

                // أزرار التعديل
                document.querySelectorAll('#schools-table .edit-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const schoolId = this.getAttribute('data-id');
                        window.location.href = `school-edit.html?id=${schoolId}`;
                    });
                });

                // أزرار الحذف
                document.querySelectorAll('#schools-table .delete-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const schoolId = this.getAttribute('data-id');
                        if (confirm('هل أنت متأكد من حذف هذه المدرسة؟')) {
                            deleteSchool(schoolId);
                        }
                    });
                });
            } catch (error) {
                console.error('خطأ في إضافة مستمعي الأحداث لأزرار الإجراءات في جدول المدارس:', error);
            }
        }

        // إضافة مستمعي الأحداث لأزرار الإجراءات في جدول الطلبات
        function addOrderActionListeners() {
            try {
                // أزرار العرض
                document.querySelectorAll('#orders-table .view-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        window.location.href = `order-view.html?id=${orderId}`;
                    });
                });
            } catch (error) {
                console.error('خطأ في إضافة مستمعي الأحداث لأزرار الإجراءات في جدول الطلبات:', error);
            }
        }

        // تهيئة مستمعي الأحداث
        function initializeEventListeners() {
            try {
                // تهيئة الإشعارات
                initializeNotifications();

                // تهيئة الوضع الليلي
                initializeDarkMode();

                // تهيئة الإجراءات السريعة
                initializeQuickActions();

                // زر تسجيل الخروج
                document.getElementById('logout-btn').addEventListener('click', function() {
                    try {
                        // حذف بيانات المستخدم من جميع وسائل التخزين
                        sessionStorage.clear();
                        localStorage.removeItem('currentUserRole');
                        localStorage.removeItem('currentUserId');

                        // حذف الكوكيز المتعلقة بالمستخدم
                        document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                        console.log('تم تسجيل الخروج بنجاح');

                        // التوجيه إلى صفحة تسجيل الدخول
                        window.location.href = '../auth/login.html';
                    } catch (error) {
                        console.error('خطأ في تسجيل الخروج:', error);
                        alert('حدث خطأ في تسجيل الخروج. سيتم إعادة تحميل الصفحة.');
                        window.location.reload();
                    }
                });
            } catch (error) {
                console.error('خطأ في تهيئة مستمعي الأحداث:', error);
            }
        }
    </script>
</body>
</html>
