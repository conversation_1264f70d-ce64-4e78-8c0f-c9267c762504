<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الاختبارات - فواصل النجاح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        .test-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
        }
        .test-card h3 i {
            margin-left: 10px;
            color: #667eea;
        }
        .test-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .test-card .features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .test-card .features li {
            padding: 5px 0;
            color: #555;
        }
        .test-card .features li:before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-left: 10px;
        }
        .test-btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        .test-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .quick-actions {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        .quick-actions h2 {
            color: white;
            margin-bottom: 20px;
            text-align: center;
        }
        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .quick-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        .quick-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            margin-top: 40px;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            .tests-grid {
                grid-template-columns: 1fr;
            }
            .quick-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-flask"></i> مركز الاختبارات</h1>
            <p>أدوات شاملة لاختبار وتشخيص نظام فواصل النجاح</p>
        </div>

        <div class="quick-actions">
            <h2><i class="fas fa-rocket"></i> إجراءات سريعة</h2>
            <div class="quick-buttons">
                <a href="../pages/auth/login.html" class="quick-btn">
                    <i class="fas fa-sign-in-alt"></i><br>
                    تسجيل الدخول
                </a>
                <a href="../pages/admin/dashboard.html" class="quick-btn">
                    <i class="fas fa-tachometer-alt"></i><br>
                    لوحة المدير
                </a>
                <a href="../pages/admin/staff.html" class="quick-btn">
                    <i class="fas fa-user-tie"></i><br>
                    إدارة العاملين
                </a>
                <a href="admin-login-quick.html" class="quick-btn">
                    <i class="fas fa-key"></i><br>
                    دخول سريع للمدير
                </a>
                <a href="../pages/staff/pos-simple.html" class="quick-btn">
                    <i class="fas fa-cash-register"></i><br>
                    نقطة البيع
                </a>
                <a href="pos-test.html" class="quick-btn">
                    <i class="fas fa-flask"></i><br>
                    اختبار نقطة البيع
                </a>
                <a href="staff-login-flow-test.html" class="quick-btn">
                    <i class="fas fa-user-check"></i><br>
                    اختبار تدفق دخول العاملين
                </a>
                <a href="password-reset-tool.html" class="quick-btn" style="background: #f44336;">
                    <i class="fas fa-key"></i><br>
                    إعادة تعيين كلمات المرور
                </a>
            </div>
        </div>

        <div class="tests-grid">
            <div class="test-card">
                <h3><i class="fas fa-user-shield"></i> اختبار تسجيل دخول المدير</h3>
                <p>أداة شاملة لتشخيص وإصلاح مشاكل تسجيل دخول المدير مع دعم جميع المتصفحات</p>
                <ul class="features">
                    <li>فحص قاعدة البيانات</li>
                    <li>اختبار التوافق مع المتصفح</li>
                    <li>اختبار التخزين</li>
                    <li>إصلاح تلقائي</li>
                    <li>اختبار التوجيه</li>
                </ul>
                <a href="admin-login-diagnostic.html" class="test-btn">
                    <i class="fas fa-play"></i> بدء الاختبار الشامل
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-bolt"></i> اختبار سريع للمدير</h3>
                <p>أداة سريعة لاختبار تسجيل دخول المدير وإصلاح المشاكل الشائعة بنقرة واحدة</p>
                <ul class="features">
                    <li>فحص سريع للنظام</li>
                    <li>تسجيل دخول بنقرة واحدة</li>
                    <li>إصلاح سريع للمشاكل</li>
                    <li>انتقال مباشر للوحات التحكم</li>
                </ul>
                <a href="admin-login-quick.html" class="test-btn">
                    <i class="fas fa-zap"></i> اختبار سريع
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-users-cog"></i> اختبار إدارة العاملين</h3>
                <p>اختبار شامل لنظام إنشاء وإدارة حسابات العاملين مع جميع الوظائف</p>
                <ul class="features">
                    <li>إنشاء حسابات العاملين</li>
                    <li>اختبار تسجيل دخول العاملين</li>
                    <li>تفعيل/إلغاء تفعيل الحسابات</li>
                    <li>اختبار التدفق الكامل</li>
                </ul>
                <a href="staff-management.html" class="test-btn">
                    <i class="fas fa-play"></i> اختبار العاملين
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
                <p>أدوات لفحص وإصلاح قاعدة البيانات وإدارة البيانات المخزنة</p>
                <ul class="features">
                    <li>فحص سلامة قاعدة البيانات</li>
                    <li>إعادة تهيئة البيانات</li>
                    <li>نسخ احتياطي واستعادة</li>
                    <li>تنظيف البيانات التالفة</li>
                </ul>
                <a href="database-test.html" class="test-btn">
                    <i class="fas fa-play"></i> اختبار قاعدة البيانات
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-globe"></i> اختبار التوافق</h3>
                <p>فحص توافق النظام مع مختلف المتصفحات والأجهزة</p>
                <ul class="features">
                    <li>فحص دعم المتصفح</li>
                    <li>اختبار الميزات المطلوبة</li>
                    <li>فحص الأداء</li>
                    <li>تقرير التوافق</li>
                </ul>
                <a href="compatibility-test.html" class="test-btn">
                    <i class="fas fa-play"></i> اختبار التوافق
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-exclamation-triangle"></i> أداة إعادة تعيين كلمات المرور</h3>
                <p>أداة طوارئ لإصلاح مشاكل المصادقة وإعادة كلمات المرور إلى حالتها الأصلية</p>
                <ul class="features">
                    <li>تشخيص مشاكل كلمات المرور</li>
                    <li>إعادة تعيين كلمات المرور</li>
                    <li>تعطيل نظام التشفير</li>
                    <li>استعادة قاعدة البيانات الأصلية</li>
                    <li>اختبار تسجيل الدخول</li>
                </ul>
                <a href="password-reset-tool.html" class="test-btn" style="background: #f44336;">
                    <i class="fas fa-tools"></i> أداة الإصلاح
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-user-check"></i> اختبار تدفق دخول العاملين</h3>
                <p>اختبار شامل لعملية تسجيل دخول العاملين والتوجيه إلى نقطة البيع</p>
                <ul class="features">
                    <li>فحص آلية تسجيل الدخول</li>
                    <li>اختبار التوجيه التلقائي</li>
                    <li>إنشاء حسابات تجريبية</li>
                    <li>فحص التخزين والجلسات</li>
                    <li>تشخيص المشاكل</li>
                </ul>
                <a href="staff-login-flow-test.html" class="test-btn">
                    <i class="fas fa-play"></i> اختبار تدفق الدخول
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-cash-register"></i> اختبار نقطة البيع</h3>
                <p>اختبار شامل لنظام نقطة البيع للعاملين مع جميع الوظائف والميزات</p>
                <ul class="features">
                    <li>عرض وإدارة المنتجات</li>
                    <li>نظام السلة والدفع</li>
                    <li>الطلبات مسبقة الدفع</li>
                    <li>طباعة الفواتير</li>
                    <li>تتبع المخزون</li>
                </ul>
                <a href="pos-test.html" class="test-btn">
                    <i class="fas fa-play"></i> اختبار نقطة البيع
                </a>
            </div>

            <div class="test-card">
                <h3><i class="fas fa-tools"></i> أدوات الصيانة</h3>
                <p>مجموعة أدوات لصيانة النظام وحل المشاكل الشائعة</p>
                <ul class="features">
                    <li>مسح البيانات المؤقتة</li>
                    <li>إعادة تعيين النظام</li>
                    <li>إصلاح الأخطاء الشائعة</li>
                    <li>تحسين الأداء</li>
                </ul>
                <a href="maintenance-tools.html" class="test-btn">
                    <i class="fas fa-play"></i> أدوات الصيانة
                </a>
            </div>
        </div>

        <div class="footer">
            <p><i class="fas fa-info-circle"></i> جميع الاختبارات آمنة ولا تؤثر على البيانات الأصلية</p>
            <p>تم تطوير هذه الأدوات لضمان عمل النظام بأفضل أداء ممكن</p>
        </div>
    </div>
</body>
</html>
