<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدارس - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .add-btn i {
            margin-left: 5px;
        }

        .add-btn:hover {
            background-color: var(--dark-color);
        }

        .search-filter {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px 10px;
            flex: 1;
            max-width: 300px;
        }

        .search-box input {
            border: none;
            outline: none;
            padding: 5px;
            width: 100%;
            font-family: 'Tajawal', sans-serif;
        }

        .search-box i {
            color: #999;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            border: none;
            margin-left: 5px;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .edit-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow: auto;
        }

        .modal-content {
            background-color: white;
            margin: 50px auto;
            padding: 20px;
            border-radius: 10px;
            max-width: 600px;
            width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h3 {
            font-size: 1.3rem;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination-btn {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 3px;
        }

        .pagination-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .search-filter {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-box {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="schools.html" class="active"><i class="fas fa-school"></i> إدارة المدارس</a></li>
                <li><a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> إدارة العاملين</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>إدارة المدارس</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>
                    قائمة المدارس
                    <button id="add-school-btn" class="add-btn"><i class="fas fa-plus"></i> إضافة مدرسة</button>
                </h2>

                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="بحث عن مدرسة...">
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المدرسة</th>
                                <th>العنوان</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="schools-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="pagination">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>
        </div>
    </section>

    <!-- Add/Edit School Modal -->
    <div id="school-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة مدرسة جديدة</h3>
                <button class="close-btn">&times;</button>
            </div>
            <form id="school-form">
                <input type="hidden" id="school-id">
                <div class="form-group">
                    <label for="school-name">اسم المدرسة</label>
                    <input type="text" id="school-name" required>
                </div>
                <div class="form-group">
                    <label for="school-address">العنوان</label>
                    <input type="text" id="school-address" required>
                </div>
                <div class="form-group">
                    <label for="school-phone">رقم الهاتف</label>
                    <input type="tel" id="school-phone" required>
                </div>
                <div class="form-group">
                    <label for="school-email">البريد الإلكتروني</label>
                    <input type="email" id="school-email" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="action-btn" id="cancel-btn">إلغاء</button>
                    <button type="submit" class="action-btn add-btn" id="save-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is admin
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Pagination variables
            const itemsPerPage = 5;
            let currentPage = 1;
            let filteredSchools = [...db.schools];

            // Function to render schools table
            function renderSchoolsTable() {
                const schoolsTable = document.getElementById('schools-table');
                schoolsTable.innerHTML = '';

                // Calculate pagination
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;
                const paginatedSchools = filteredSchools.slice(startIndex, endIndex);

                if (paginatedSchools.length === 0) {
                    schoolsTable.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد مدارس.</td></tr>';
                    return;
                }

                paginatedSchools.forEach(school => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${school.id}</td>
                        <td>${school.name}</td>
                        <td>${school.address}</td>
                        <td>${school.phone}</td>
                        <td>${school.email}</td>
                        <td>
                            <button class="action-btn view-btn" data-id="${school.id}">عرض</button>
                            <button class="action-btn edit-btn" data-id="${school.id}">تعديل</button>
                            <button class="action-btn delete-btn" data-id="${school.id}">حذف</button>
                        </td>
                    `;
                    schoolsTable.appendChild(row);
                });

                // Add event listeners to buttons
                document.querySelectorAll('.view-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const schoolId = parseInt(this.getAttribute('data-id'));
                        viewSchool(schoolId);
                    });
                });

                document.querySelectorAll('.edit-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const schoolId = parseInt(this.getAttribute('data-id'));
                        editSchool(schoolId);
                    });
                });

                document.querySelectorAll('.delete-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const schoolId = parseInt(this.getAttribute('data-id'));
                        deleteSchool(schoolId);
                    });
                });

                // Render pagination
                renderPagination();
            }

            // Function to render pagination
            function renderPagination() {
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';

                const totalPages = Math.ceil(filteredSchools.length / itemsPerPage);

                if (totalPages <= 1) {
                    return;
                }

                // Previous button
                const prevBtn = document.createElement('button');
                prevBtn.className = 'pagination-btn';
                prevBtn.innerHTML = '&laquo;';
                prevBtn.disabled = currentPage === 1;
                prevBtn.addEventListener('click', function() {
                    if (currentPage > 1) {
                        currentPage--;
                        renderSchoolsTable();
                    }
                });
                pagination.appendChild(prevBtn);

                // Page buttons
                for (let i = 1; i <= totalPages; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = 'pagination-btn' + (i === currentPage ? ' active' : '');
                    pageBtn.textContent = i;
                    pageBtn.addEventListener('click', function() {
                        currentPage = i;
                        renderSchoolsTable();
                    });
                    pagination.appendChild(pageBtn);
                }

                // Next button
                const nextBtn = document.createElement('button');
                nextBtn.className = 'pagination-btn';
                nextBtn.innerHTML = '&raquo;';
                nextBtn.disabled = currentPage === totalPages;
                nextBtn.addEventListener('click', function() {
                    if (currentPage < totalPages) {
                        currentPage++;
                        renderSchoolsTable();
                    }
                });
                pagination.appendChild(nextBtn);
            }

            // Function to view school details
            function viewSchool(schoolId) {
                const school = db.schools.find(s => s.id === schoolId);
                if (school) {
                    alert(`
                        اسم المدرسة: ${school.name}
                        العنوان: ${school.address}
                        رقم الهاتف: ${school.phone}
                        البريد الإلكتروني: ${school.email}
                    `);
                }
            }

            // Function to open edit school modal
            function editSchool(schoolId) {
                const school = db.schools.find(s => s.id === schoolId);
                if (school) {
                    document.getElementById('modal-title').textContent = 'تعديل بيانات المدرسة';
                    document.getElementById('school-id').value = school.id;
                    document.getElementById('school-name').value = school.name;
                    document.getElementById('school-address').value = school.address;
                    document.getElementById('school-phone').value = school.phone;
                    document.getElementById('school-email').value = school.email;

                    document.getElementById('school-modal').style.display = 'block';
                }
            }

            // Function to delete school
            function deleteSchool(schoolId) {
                if (confirm('هل أنت متأكد من حذف هذه المدرسة؟')) {
                    // Check if school has associated users
                    const hasUsers = db.users.some(user => user.schoolId === schoolId);
                    if (hasUsers) {
                        alert('لا يمكن حذف هذه المدرسة لأنها مرتبطة بمستخدمين!');
                        return;
                    }

                    // Remove school from database
                    const schoolIndex = db.schools.findIndex(s => s.id === schoolId);
                    if (schoolIndex !== -1) {
                        db.schools.splice(schoolIndex, 1);
                        saveDatabase(db);

                        // Update filtered schools
                        filteredSchools = [...db.schools];

                        // Reset to first page if current page is now empty
                        if (currentPage > Math.ceil(filteredSchools.length / itemsPerPage)) {
                            currentPage = 1;
                        }

                        renderSchoolsTable();
                        alert('تم حذف المدرسة بنجاح!');
                    }
                }
            }

            // Add school button click event
            document.getElementById('add-school-btn').addEventListener('click', function() {
                document.getElementById('modal-title').textContent = 'إضافة مدرسة جديدة';
                document.getElementById('school-id').value = '';
                document.getElementById('school-form').reset();
                document.getElementById('school-modal').style.display = 'block';
            });

            // Close modal buttons
            document.querySelector('.close-btn').addEventListener('click', function() {
                document.getElementById('school-modal').style.display = 'none';
            });

            document.getElementById('cancel-btn').addEventListener('click', function() {
                document.getElementById('school-modal').style.display = 'none';
            });

            // School form submission
            document.getElementById('school-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const schoolId = document.getElementById('school-id').value;
                const name = document.getElementById('school-name').value;
                const address = document.getElementById('school-address').value;
                const phone = document.getElementById('school-phone').value;
                const email = document.getElementById('school-email').value;

                if (schoolId) {
                    // Update existing school
                    const schoolIndex = db.schools.findIndex(s => s.id === parseInt(schoolId));
                    if (schoolIndex !== -1) {
                        db.schools[schoolIndex].name = name;
                        db.schools[schoolIndex].address = address;
                        db.schools[schoolIndex].phone = phone;
                        db.schools[schoolIndex].email = email;

                        saveDatabase(db);
                        alert('تم تحديث بيانات المدرسة بنجاح!');
                    }
                } else {
                    // Add new school
                    const newSchool = {
                        id: db.schools.length > 0 ? Math.max(...db.schools.map(s => s.id)) + 1 : 1,
                        name,
                        address,
                        phone,
                        email
                    };

                    db.schools.push(newSchool);
                    saveDatabase(db);
                    alert('تمت إضافة المدرسة بنجاح!');
                }

                // Update filtered schools
                filteredSchools = [...db.schools];
                renderSchoolsTable();

                // Close modal
                document.getElementById('school-modal').style.display = 'none';
            });

            // Search input event
            document.getElementById('search-input').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                filteredSchools = db.schools.filter(school =>
                    school.name.toLowerCase().includes(searchTerm) ||
                    school.address.toLowerCase().includes(searchTerm) ||
                    school.phone.toLowerCase().includes(searchTerm) ||
                    school.email.toLowerCase().includes(searchTerm)
                );

                currentPage = 1;
                renderSchoolsTable();
            });

            // Initial render
            renderSchoolsTable();

            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
    </script>
</body>
</html>
