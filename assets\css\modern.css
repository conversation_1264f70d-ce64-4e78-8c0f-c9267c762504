/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * ملف CSS للتصميم العصري
 */

/* ===== تحسينات عامة ===== */
:root {
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 15px 30px rgba(0, 0, 0, 0.2);
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --border-radius-sm: 5px;
    --border-radius-md: 10px;
    --border-radius-lg: 15px;
    --border-radius-xl: 20px;
    --border-radius-full: 9999px;
}

body {
    background-color: #f8f9fa;
}

/* ===== تحسينات الأزرار ===== */
.btn {
    border-radius: var(--border-radius-md);
    padding: 10px 20px;
    font-weight: 500;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-normal);
    z-index: -1;
}

.btn:hover::before {
    width: 300%;
    height: 300%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    border: none;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #f57f17);
    color: white;
    border: none;
    box-shadow: 0 4px 10px rgba(249, 168, 37, 0.3);
}

.btn-secondary:hover {
    box-shadow: 0 6px 15px rgba(249, 168, 37, 0.4);
    transform: translateY(-2px);
}

/* ===== تحسينات البطاقات ===== */
.content-card {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: none;
    overflow: hidden;
}

.content-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.content-card h2 {
    position: relative;
    padding-bottom: 15px;
}

.content-card h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-full);
}

/* ===== تحسينات الجداول ===== */
table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

table th {
    background: linear-gradient(to left, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.05));
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

table tr:hover {
    background-color: rgba(46, 125, 50, 0.05);
}

/* ===== تحسينات النماذج ===== */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--primary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    font-family: 'Tajawal', sans-serif;
    background-color: #f9f9f9;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
    outline: none;
    background-color: white;
}

/* ===== تحسينات القائمة الجانبية ===== */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    box-shadow: var(--shadow-lg);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.sidebar-menu li a {
    border-radius: var(--border-radius-md);
    margin: 0 10px;
    padding: 12px 15px;
    transition: var(--transition-normal);
}

.sidebar-menu li a:hover,
.sidebar-menu li a.active {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
    border-right: none;
}

.sidebar-menu li a.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

.sidebar-menu li a i {
    transition: var(--transition-normal);
}

.sidebar-menu li a:hover i,
.sidebar-menu li a.active i {
    transform: scale(1.2);
}

/* ===== تحسينات الرأس ===== */
.header {
    background-color: white;
    box-shadow: var(--shadow-md);
}

.logo h1 {
    background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

/* ===== تحسينات البطاقات الإحصائية ===== */
.stat-card {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 20px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(46, 125, 50, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* ===== تحسينات الرسوم البيانية ===== */
.chart-container {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: 20px;
    box-shadow: var(--shadow-md);
    margin-bottom: 20px;
}

/* ===== تأثيرات حركية ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== تحسينات الشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .sidebar {
        border-radius: 0;
    }
    
    .content-card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 8px 15px;
    }
}

/* ===== تحسينات الإشعارات ===== */
.notification-item {
    transition: var(--transition-normal);
    border-radius: var(--border-radius-sm);
    margin: 5px;
}

.notification-item:hover {
    transform: translateX(-5px);
}

.notification-icon {
    transition: var(--transition-normal);
}

.notification-item:hover .notification-icon {
    transform: scale(1.1);
}

/* ===== تحسينات بطاقات المنتجات ===== */
.product-card {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    background-color: white;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    height: 150px;
    background: linear-gradient(135deg, rgba(46, 125, 50, 0.8), rgba(27, 94, 32, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    transition: var(--transition-normal);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 15px;
}

.product-info h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.category {
    display: inline-block;
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    margin-bottom: 10px;
}

/* ===== تحسينات الزر العائم ===== */
.floating-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    z-index: 99;
}

.floating-btn:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: var(--shadow-xl);
}

/* ===== تحسينات الشريط العلوي ===== */
.user-menu {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 10px;
    box-shadow: var(--shadow-sm);
}

/* ===== تحسينات الصفحة الرئيسية ===== */
.hero {
    background: linear-gradient(135deg, rgba(46, 125, 50, 0.8), rgba(27, 94, 32, 0.9)), url('../../assets/images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../assets/images/pattern.png');
    opacity: 0.1;
}

.hero-content h2 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: white;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

/* ===== تحسينات الأقسام ===== */
.section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 4px;
    background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    border-radius: var(--border-radius-full);
}

.section-header p {
    color: #666;
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
}
