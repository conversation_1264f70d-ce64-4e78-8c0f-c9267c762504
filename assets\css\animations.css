/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * أنماط التأثيرات الحركية والانتقالات
 */

/* متغيرات التأثيرات الحركية */
:root {
    --transition-speed: 0.3s;
    --transition-function: ease;
    --animation-speed: 1;
}

/* تعطيل جميع التأثيرات الحركية */
.no-animations * {
    transition: none !important;
    animation: none !important;
}

/* تأثيرات الانتقال الأساسية */
.transition-fade {
    transition: opacity var(--transition-speed) var(--transition-function);
}

.transition-transform {
    transition: transform var(--transition-speed) var(--transition-function);
}

.transition-all {
    transition: all var(--transition-speed) var(--transition-function);
}

/* تأثيرات حركية للشعار */
@keyframes logoGlow {
    0% { filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.25)); }
    50% { filter: drop-shadow(0 8px 15px rgba(46, 125, 50, 0.5)); }
    100% { filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.25)); }
}

@keyframes logoPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.logo-img {
    animation: logoGlow 3s infinite ease-in-out;
}

.logo:hover .logo-img {
    animation: logoPulse 1s infinite ease-in-out;
}

/* تأثيرات الظهور */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.animate-fade-in {
    animation: fadeIn calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-down {
    animation: fadeInDown calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-right {
    animation: fadeInRight calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-left {
    animation: fadeInLeft calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

/* تأثيرات الاختفاء */
@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.animate-fade-out {
    animation: fadeOut calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.animate-fade-out-up {
    animation: fadeOutUp calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.animate-fade-out-down {
    animation: fadeOutDown calc(var(--transition-speed) * var(--animation-speed)) var(--transition-function);
}

/* تأثيرات النبض والتكبير */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-pulse {
    animation: pulse calc(2s * var(--animation-speed)) infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.animate-bounce {
    animation: bounce calc(2s * var(--animation-speed)) infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
    animation: shake calc(0.8s * var(--animation-speed));
}

/* تأثيرات الدوران */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin calc(1.5s * var(--animation-speed)) linear infinite;
}

@keyframes flip {
    0% { transform: perspective(400px) rotateY(0); }
    100% { transform: perspective(400px) rotateY(360deg); }
}

.animate-flip {
    animation: flip calc(1.5s * var(--animation-speed)) ease-in-out;
}

/* تأثيرات الانتقال بين الصفحات */
.page-transition-enter {
    opacity: 0;
    transform: translateY(20px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity var(--transition-speed) var(--transition-function),
                transform var(--transition-speed) var(--transition-function);
}

.page-transition-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity var(--transition-speed) var(--transition-function),
                transform var(--transition-speed) var(--transition-function);
}

/* تأثيرات للعناصر التفاعلية */
.hover-scale {
    transition: transform var(--transition-speed) var(--transition-function);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-lift {
    transition: transform var(--transition-speed) var(--transition-function),
                box-shadow var(--transition-speed) var(--transition-function);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hover-glow {
    transition: box-shadow var(--transition-speed) var(--transition-function);
}

.hover-glow:hover {
    box-shadow: 0 0 15px var(--primary-color);
}

/* تأثيرات للأزرار */
.btn {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-speed) var(--transition-function);
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width var(--transition-speed) var(--transition-function),
                height var(--transition-speed) var(--transition-function);
    z-index: 1;
}

.btn:hover::after {
    width: 300%;
    height: 300%;
}

.btn:active {
    transform: scale(0.98);
}

/* تأثيرات للبطاقات */
.card-hover {
    transition: transform var(--transition-speed) var(--transition-function),
                box-shadow var(--transition-speed) var(--transition-function);
}

.card-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* تأثيرات للقوائم */
.menu-item {
    position: relative;
    transition: color var(--transition-speed) var(--transition-function);
}

.menu-item::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-speed) var(--transition-function);
}

.menu-item:hover::after {
    width: 100%;
}

/* تأثيرات للنماذج */
.input-focus {
    transition: border-color var(--transition-speed) var(--transition-function),
                box-shadow var(--transition-speed) var(--transition-function);
}

.input-focus:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
}

/* تأثيرات للشاشات الصغيرة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
