<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - تسجيل دخول المدير</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .admin-info {
            background: linear-gradient(45deg, #fff3e0, #ffecb3);
            color: #f57c00;
            border: 2px solid #ff9800;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .quick-login {
            background: linear-gradient(45deg, #e8f5e9, #c8e6c9);
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #4caf50;
        }
        .quick-login input {
            padding: 12px;
            margin: 8px;
            border: 2px solid #ddd;
            border-radius: 8px;
            width: 200px;
            font-size: 16px;
            text-align: center;
        }
        .quick-login input:focus {
            border-color: #2e7d32;
            outline: none;
            box-shadow: 0 0 10px rgba(46, 125, 50, 0.3);
        }
        .big-button {
            font-size: 18px;
            padding: 15px 30px;
            background: linear-gradient(45deg, #2e7d32, #4caf50);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }
        .big-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 اختبار سريع - تسجيل دخول المدير</h1>
            <p>أداة سريعة لاختبار وإصلاح مشاكل تسجيل دخول المدير</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="admin-login-diagnostic.html">🔍 تشخيص شامل</a>
            <a href="../pages/auth/login.html">🔐 تسجيل الدخول</a>
            <a href="../pages/admin/dashboard.html">📊 لوحة المدير</a>
        </div>
        
        <div class="admin-info">
            <h3>📋 بيانات المدير الافتراضية:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
            <p><strong>الدور:</strong> admin</p>
        </div>

        <div class="quick-login">
            <h3>🔐 تسجيل دخول سريع</h3>
            <input type="text" id="username" value="admin" placeholder="اسم المستخدم">
            <input type="password" id="password" value="admin123" placeholder="كلمة المرور">
            <br><br>
            <button onclick="quickLogin()" class="big-button">⚡ تسجيل الدخول كمدير</button>
        </div>

        <div class="test-section">
            <h3>1. فحص سريع للنظام</h3>
            <button onclick="quickSystemCheck()">🔍 فحص سريع</button>
            <div id="quick-check-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. إصلاح سريع</h3>
            <button onclick="quickFix()">🔧 إصلاح سريع</button>
            <div id="quick-fix-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. الانتقال المباشر</h3>
            <button onclick="goToLoginPage()">🔐 صفحة تسجيل الدخول</button>
            <button onclick="goToAdminDashboard()">📊 لوحة تحكم المدير</button>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // فحص سريع للنظام
        function quickSystemCheck() {
            const resultDiv = document.getElementById('quick-check-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري الفحص السريع...</div>';
                
                let html = '';
                let allGood = true;

                // فحص قاعدة البيانات
                if (typeof window.getDatabase === 'function') {
                    const db = window.getDatabase();
                    if (db && db.users) {
                        const admin = db.users.find(u => u.role === 'admin');
                        if (admin) {
                            html += '<div class="success">✓ حساب المدير موجود</div>';
                            html += `<div class="info">اسم المستخدم: ${admin.username}</div>`;
                            html += `<div class="info">كلمة المرور: ${admin.password}</div>`;
                        } else {
                            html += '<div class="error">✗ حساب المدير غير موجود</div>';
                            allGood = false;
                        }
                    } else {
                        html += '<div class="error">✗ قاعدة البيانات تالفة</div>';
                        allGood = false;
                    }
                } else {
                    html += '<div class="error">✗ دالة getDatabase غير متوفرة</div>';
                    allGood = false;
                }

                // فحص التخزين
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    html += '<div class="success">✓ localStorage يعمل</div>';
                } catch (e) {
                    html += '<div class="error">✗ localStorage لا يعمل</div>';
                    allGood = false;
                }

                // فحص المتصفح
                html += `<div class="info">المتصفح: ${navigator.userAgent.split(' ')[0]}</div>`;

                if (allGood) {
                    html += '<div class="success"><strong>🎉 النظام جاهز!</strong></div>';
                } else {
                    html += '<div class="error"><strong>⚠️ يحتاج إصلاح</strong></div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في الفحص: ${error.message}</div>`;
            }
        }

        // إصلاح سريع
        function quickFix() {
            const resultDiv = document.getElementById('quick-fix-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري الإصلاح السريع...</div>';

                // إنشاء قاعدة بيانات افتراضية
                const defaultDB = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            name: 'مدير النظام'
                        }
                    ],
                    schools: [],
                    products: [],
                    orders: []
                };

                localStorage.setItem('cafeteriaDB', JSON.stringify(defaultDB));
                sessionStorage.clear();

                resultDiv.innerHTML = '<div class="success">✓ تم الإصلاح بنجاح! يمكنك الآن تسجيل الدخول.</div>';

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في الإصلاح: ${error.message}</div>`;
            }
        }

        // تسجيل دخول سريع
        function quickLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            console.log('محاولة تسجيل دخول سريع:', { username, password });

            try {
                // التأكد من وجود قاعدة البيانات
                let db = null;
                if (window.getDatabase) {
                    db = window.getDatabase();
                } else {
                    const stored = localStorage.getItem('cafeteriaDB');
                    if (stored) {
                        db = JSON.parse(stored);
                    }
                }

                if (!db || !db.users) {
                    alert('قاعدة البيانات غير متوفرة. سيتم إنشاؤها الآن...');
                    quickFix();
                    setTimeout(() => quickLogin(), 1000);
                    return;
                }

                // البحث عن المدير
                const admin = db.users.find(u => u.username === username && u.role === 'admin');
                
                if (!admin) {
                    alert('المدير غير موجود!');
                    return;
                }

                if (admin.password !== password) {
                    alert('كلمة المرور غير صحيحة!');
                    return;
                }

                // تخزين بيانات المدير
                try {
                    sessionStorage.setItem('currentUser', JSON.stringify(admin));
                    localStorage.setItem('currentUserRole', admin.role);
                    localStorage.setItem('currentUserId', admin.id.toString());
                } catch (storageError) {
                    console.warn('خطأ في التخزين:', storageError);
                }

                alert('تم تسجيل الدخول بنجاح! سيتم توجيهك إلى لوحة التحكم...');

                // التوجيه إلى لوحة التحكم
                setTimeout(() => {
                    window.location.href = '../pages/admin/dashboard.html';
                }, 1000);

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                alert('حدث خطأ: ' + error.message);
            }
        }

        // الانتقال لصفحة تسجيل الدخول
        function goToLoginPage() {
            window.location.href = '../pages/auth/login.html';
        }

        // الانتقال للوحة تحكم المدير
        function goToAdminDashboard() {
            // تخزين بيانات مؤقتة للمدير
            const admin = {
                id: 1,
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                name: 'مدير النظام'
            };

            try {
                sessionStorage.setItem('currentUser', JSON.stringify(admin));
                localStorage.setItem('currentUserRole', admin.role);
                localStorage.setItem('currentUserId', admin.id.toString());
            } catch (e) {
                console.warn('خطأ في التخزين:', e);
            }

            window.location.href = '../pages/admin/dashboard.html';
        }

        // تشغيل فحص سريع عند تحميل الصفحة
        setTimeout(() => {
            quickSystemCheck();
        }, 1000);
    </script>
</body>
</html>
