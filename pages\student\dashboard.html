<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الطالب - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 8px 0;
            min-height: 50px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* لوجو الشوكة والسكينة */
        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 10px;
            color: white;
            font-size: 1.4rem;
            box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .compact-header .logo h1 {
            font-size: 1.1rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .compact-header .user-menu span {
            font-size: 0.9rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        /* زر العودة إلى الأعلى */
        .back-to-top-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px) scale(0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .back-to-top-btn.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .back-to-top-btn:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 8px 30px rgba(76, 175, 80, 0.5);
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
        }

        .back-to-top-btn:active {
            transform: translateY(-2px) scale(1.05);
        }

        /* تأثير الإضاءة خلف الزر */
        .btn-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0);
            transition: all 0.4s ease;
            z-index: -1;
        }

        .back-to-top-btn:hover .btn-glow {
            transform: translate(-50%, -50%) scale(1.5);
        }

        /* تأثير النبضة */
        .back-to-top-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            opacity: 0.7;
            transform: scale(1);
            animation: pulse 2s infinite;
            z-index: -2;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.3;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* تأثير الدوران عند الضغط */
        .back-to-top-btn i {
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .back-to-top-btn:hover i {
            transform: translateY(-2px);
        }

        .back-to-top-btn:active i {
            transform: translateY(0) rotateX(180deg);
        }

        /* استجابة للأجهزة المحمولة */
        @media (max-width: 768px) {
            .back-to-top-btn {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .back-to-top-btn {
                bottom: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
                font-size: 1rem;
            }
        }

        /* تأثير الاهتزاز عند الضغط */
        .back-to-top-btn.clicked {
            animation: shake 0.3s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px) rotateZ(-2deg); }
            75% { transform: translateX(2px) rotateZ(2deg); }
        }

        /* تأثير التموج عند الضغط */
        .back-to-top-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
            z-index: 1;
        }

        .back-to-top-btn:active::after {
            width: 120%;
            height: 120%;
            opacity: 0;
        }

        .dashboard {
            display: flex;
            min-height: calc(100vh - 50px);
        }





        /* نافذة إضافة الرصيد */
        .add-balance-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            position: relative;
            z-index: 1001;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 30px;
        }

        .amount-input-section {
            margin-bottom: 25px;
        }

        .amount-input-section label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .amount-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .amount-input-wrapper input {
            width: 100%;
            padding: 15px 60px 15px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .amount-input-wrapper input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .amount-input-wrapper .currency {
            position: absolute;
            right: 15px;
            color: var(--text-muted);
            font-weight: 600;
        }

        .quick-amounts h4 {
            margin-bottom: 15px;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .quick-amounts-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .quick-amount-btn {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #e0e0e0;
            padding: 12px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: var(--primary-color);
        }

        .quick-amount-btn:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8f9fa;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .modal-footer button {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cancel-btn {
            background: #6c757d;
            color: white;
        }

        .cancel-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .confirm-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
        }

        .confirm-btn:hover {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        /* إشعار النجاح */
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(40, 167, 69, 0.3);
            z-index: 1002;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInRight 0.3s ease-out, slideOutRight 0.3s ease-in 2.7s forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .success-notification i {
            font-size: 1.2rem;
        }

        /* نافذة إضافة بطاقة جديدة */
        .add-card-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-card-modal .modal-content {
            max-width: 600px;
            width: 90%;
        }

        .card-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .card-type-option {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #e0e0e0;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .card-type-option:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .card-type-option.selected {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 5px 20px rgba(76, 175, 80, 0.4);
        }

        .card-type-option i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .card-type-option h4 {
            margin: 0 0 5px 0;
            font-size: 1rem;
        }

        .card-type-option p {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }

        /* إشعار الرسوم */
        .fee-notice {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .fee-notice-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ffc107, #ff9800);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .fee-notice-content h4 {
            margin: 0 0 5px 0;
            color: #f57c00;
            font-size: 1rem;
        }

        .fee-notice-content p {
            margin: 0;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .fee-notice-content strong {
            color: #f57c00;
            font-weight: 700;
        }

        /* شارة الرسوم على البطاقات */
        .card-type-option {
            position: relative;
        }

        .card-fee-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(231, 76, 60, 0.3);
        }

        .card-type-option:hover .card-fee-badge {
            background: linear-gradient(135deg, #c0392b, #a93226);
        }

        /* نافذة خيارات البطاقة */
        .card-options-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-options-modal .modal-content {
            max-width: 400px;
            width: 90%;
        }

        .card-options-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
        }

        .card-option-btn {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #e0e0e0;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
            color: var(--text-color);
        }

        .card-option-btn:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border-color: var(--primary-color);
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .card-option-btn.danger:hover {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-color: #e74c3c;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .card-option-btn i {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        /* أنواع البطاقات المختلفة */
        .balance-card.savings {
            background: linear-gradient(135deg, #8e24aa 0%, #7b1fa2 50%, #6a1b9a 100%);
        }

        .balance-card.premium {
            background: linear-gradient(135deg, #ff6f00 0%, #ff8f00 50%, #ffa000 100%);
        }

        .balance-card.family {
            background: linear-gradient(135deg, #00695c 0%, #00796b 50%, #00897b 100%);
        }

        .balance-card.gift {
            background: linear-gradient(135deg, #c2185b 0%, #d81b60 50%, #e91e63 100%);
        }

        /* نوافذ رسوم البطاقة */
        .card-fee-modal, .insufficient-balance-modal, .success-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-fee-modal .modal-content,
        .insufficient-balance-modal .modal-content,
        .success-modal .modal-content {
            max-width: 500px;
            width: 90%;
        }

        /* تفاصيل الرسوم */
        .fee-breakdown {
            margin: 20px 0;
        }

        .fee-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .fee-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .fee-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
        }

        .fee-details {
            flex: 1;
        }

        .fee-details h4 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .fee-details p {
            margin: 0;
            color: var(--text-muted);
            font-size: 0.85rem;
        }

        .fee-amount {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .fee-amount.total {
            font-size: 1.3rem;
            color: var(--primary-color);
        }

        .fee-separator {
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            margin: 15px 0;
            border-radius: 1px;
        }

        .fee-total {
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.05));
            border: 2px solid var(--primary-color);
        }

        /* معلومات الرصيد */
        .balance-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .current-balance, .remaining-balance {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .current-balance {
            color: var(--primary-color);
        }

        .remaining-balance {
            color: var(--secondary-color);
        }

        /* نافذة الرصيد غير الكافي */
        .modal-header.error {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .balance-comparison {
            margin: 20px 0;
        }

        .balance-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .balance-item.required {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
            border: 2px solid #e74c3c;
        }

        .balance-item.current {
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.05));
            border: 2px solid var(--primary-color);
        }

        .balance-item.shortage {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
            border: 2px solid #ff9800;
        }

        .balance-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
        }

        .balance-item.required .balance-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .balance-item.current .balance-icon {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
        }

        .balance-item.shortage .balance-icon {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .balance-details {
            flex: 1;
        }

        .balance-details h4 {
            margin: 0 0 5px 0;
            font-size: 1rem;
        }

        .balance-details p {
            margin: 0;
            color: var(--text-muted);
            font-size: 0.85rem;
        }

        .balance-amount {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .suggestion-box {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .suggestion-box i {
            color: #ffc107;
            font-size: 1.5rem;
            margin-top: 5px;
        }

        .suggestion-box h4 {
            margin: 0 0 5px 0;
            color: #f57c00;
        }

        .suggestion-box p {
            margin: 0;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .add-balance-btn {
            background: linear-gradient(135deg, #ffc107, #ff9800) !important;
            color: white !important;
            border: none !important;
        }

        .add-balance-btn:hover {
            background: linear-gradient(135deg, #ff9800, #f57c00) !important;
        }

        /* نافذة النجاح */
        .modal-header.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .success-animation {
            text-align: center;
            margin: 20px 0;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            animation: checkmarkPulse 0.6s ease-out;
        }

        @keyframes checkmarkPulse {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .card-summary {
            text-align: center;
            margin: 20px 0;
        }

        .card-summary h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .transaction-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .summary-item.total {
            border-top: 2px solid var(--primary-color);
            padding-top: 8px;
            margin-top: 10px;
            font-weight: 700;
            font-size: 1rem;
            color: var(--primary-color);
        }

        /* نافذة حذف البطاقة */
        .delete-card-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .delete-card-modal .modal-content {
            max-width: 500px;
            width: 90%;
        }

        .modal-header.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .warning-message {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
            border: 2px solid #e74c3c;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .warning-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            animation: warningPulse 2s ease-in-out infinite;
        }

        @keyframes warningPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .warning-content h4 {
            margin: 0 0 5px 0;
            color: #c0392b;
            font-size: 1.1rem;
        }

        .warning-content p {
            margin: 0;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .card-info {
            margin: 20px 0;
        }

        .card-preview {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            padding: 15px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-details {
            flex: 1;
        }

        .card-details h4 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
        }

        .card-details p {
            margin: 0;
            color: var(--text-muted);
            font-size: 0.85rem;
        }

        .card-balance {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .balance-transfer-info {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .transfer-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ffc107, #ff9800);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .transfer-details h4 {
            margin: 0 0 5px 0;
            color: #f57c00;
        }

        .transfer-details p {
            margin: 0;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .confirmation-input {
            margin: 20px 0;
        }

        .confirmation-input label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }

        .confirmation-input input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .confirmation-input input:focus {
            border-color: #e74c3c;
            outline: none;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.2);
        }

        .delete-btn {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d) !important;
            color: white !important;
            border: none !important;
            cursor: not-allowed !important;
            transition: all 0.3s ease !important;
        }

        .delete-btn.enabled {
            background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
            cursor: pointer !important;
        }

        .delete-btn.enabled:hover {
            background: linear-gradient(135deg, #c0392b, #a93226) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4) !important;
        }

        .deletion-summary {
            text-align: center;
            margin: 20px 0;
        }

        .deletion-summary h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .balance-transfer-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .student-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e3f2fd;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .info-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .info-item h3 {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .info-item p {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .info-item i {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: var(--primary-color);
            opacity: 0.3;
        }

        .balance-card {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            padding: 0;
            border-radius: 20px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            aspect-ratio: 1.586; /* نسبة بطاقة الائتمان القياسية */
            max-width: 400px;
            min-height: 250px;
        }

        /* تأثير الهولوجرام */
        .balance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 70%
            );
            transform: translateX(-100%);
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }

        /* نمط الدوائر الخلفية */
        .balance-card::after {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        .balance-content {
            padding: 25px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }

        /* رأس البطاقة */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .card-logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1rem;
            font-weight: 700;
            opacity: 0.9;
        }

        .card-type {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            backdrop-filter: blur(10px);
        }

        /* رقم البطاقة */
        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 3px;
            margin: 15px 0;
            opacity: 0.8;
        }

        /* معلومات الرصيد */
        .balance-info {
            margin: 20px 0;
        }

        .balance-label {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .balance-amount {
            font-size: 2.2rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* معلومات الطالب */
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }

        .card-holder {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .card-holder-label {
            font-size: 0.7rem;
            opacity: 0.6;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-holder-name {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .card-chip {
            width: 35px;
            height: 25px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 4px;
            position: relative;
            margin-bottom: 10px;
        }

        .card-chip::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            right: 3px;
            bottom: 3px;
            background: linear-gradient(135deg, #ffed4e, #ffd700);
            border-radius: 2px;
        }

        /* أزرار الإجراءات */
        .balance-actions {
            position: absolute;
            bottom: -70px;
            left: 0;
            right: 0;
            display: flex;
            gap: 12px;
            padding: 0 25px;
            transition: all 0.3s ease;
            justify-content: center;
            align-items: center;
        }

        .balance-card:hover .balance-actions {
            bottom: -55px;
        }

        .balance-actions button {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            border: none;
            padding: 14px 22px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-weight: 600;
            font-size: 0.95rem;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            position: relative;
            overflow: hidden;
            min-width: 140px;
        }

        .balance-actions button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .balance-actions button:hover::before {
            left: 100%;
        }

        .balance-actions button:hover {
            background: linear-gradient(135deg, #45a049, #4caf50);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .balance-actions button:active {
            transform: translateY(0);
        }

        /* أزرار الإضافة السريعة */
        .quick-add-amounts {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .quick-add-btn {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            color: var(--primary-color);
            border: 2px solid #e0e0e0;
            padding: 15px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            font-weight: 700;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            position: relative;
            overflow: hidden;
        }

        .quick-add-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
            transition: left 0.5s;
        }

        .quick-add-btn:hover::before {
            left: 100%;
        }

        .quick-add-btn:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .quick-add-btn:active {
            transform: translateY(-1px);
        }

        .quick-add-btn i {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* تأثير الإمالة ثلاثية الأبعاد */
        .balance-card {
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .balance-card:hover {
            transform: rotateY(5deg) rotateX(5deg) translateZ(10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        /* تأثير النقر على البطاقة */
        .balance-card:active {
            transform: rotateY(2deg) rotateX(2deg) translateZ(5px);
            transition: transform 0.1s ease;
        }

        /* تأثير الضوء المتحرك - تم إزالة اللون الأبيض */
        .balance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: none;
            z-index: 1;
            pointer-events: none;
        }

        /* تحسين شريحة البطاقة */
        .card-chip {
            width: 35px;
            height: 25px;
            background: linear-gradient(135deg, #ffd700, #ffed4e, #ffd700);
            border-radius: 4px;
            position: relative;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .card-chip::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            right: 3px;
            bottom: 3px;
            background: linear-gradient(135deg, #ffed4e, #ffd700, #ffed4e);
            border-radius: 2px;
        }

        .card-chip::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            right: 6px;
            bottom: 6px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 1px;
        }

        /* تحسين نمط الخلفية - تم إزالة اللون الأبيض */
        .balance-card::after {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            background: none;
            border-radius: 50%;
            z-index: 1;
            pointer-events: none;
        }

        /* تأثير الانعكاس */
        .balance-content {
            position: relative;
            z-index: 3;
        }

        /* إدارة البطاقات */
        .cards-management {
            margin-bottom: 30px;
        }

        .cards-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 10px;
        }

        .cards-header h3 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .add-card-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .add-card-btn:hover {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        /* شريط التنقل بين البطاقات */
        .cards-navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e0e0e0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .cards-indicator {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e0e0e0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: var(--primary-color);
            transform: scale(1.2);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .indicator:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
        }

        /* حاوي البطاقات */
        .cards-container {
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            margin-bottom: 20px;
        }

        .balance-card {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .balance-card.active {
            position: relative;
            opacity: 1;
            transform: translateX(0);
        }

        .balance-card.prev {
            transform: translateX(-100%);
        }

        .balance-card.next {
            transform: translateX(100%);
        }

        /* زر خيارات البطاقة */
        .card-options-btn {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2)) !important;
            border: 2px solid rgba(255, 255, 255, 0.5) !important;
            width: 45px !important;
            height: 45px !important;
            padding: 0 !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 1.1rem !important;
            color: white !important;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2) !important;
            transition: all 0.3s ease !important;
        }

        .card-options-btn:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3)) !important;
            border-color: rgba(255, 255, 255, 0.7) !important;
            transform: rotate(90deg) scale(1.1) !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
        }

        .card-options-btn:active {
            transform: rotate(90deg) scale(0.95) !important;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 150px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image i {
            font-size: 3rem;
            color: #ccc;
        }

        .product-info {
            padding: 15px;
        }

        .product-info h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .product-info .price {
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        .product-info .category {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 10px;
        }

        .product-actions button {
            width: 100%;
            padding: 8px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .product-actions button:hover {
            background-color: var(--dark-color);
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .status-pending {
            background-color: #ffeaa7;
            color: #d35400;
        }

        .status-processing {
            background-color: #81ecec;
            color: #00b894;
        }

        .status-completed {
            background-color: #55efc4;
            color: #00b894;
        }

        .status-cancelled {
            background-color: #fab1a0;
            color: #d63031;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .student-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header compact-header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>أحمد محمد</strong></span>
                <button id="header-logout-btn" class="header-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>الطالب</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>لوحة تحكم الطالب</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2><i class="fas fa-user-graduate"></i> معلومات الطالب</h2>
                <div class="student-info">
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <h3>الاسم الكامل</h3>
                        <p id="student-name">أحمد محمد العلي</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-id-card"></i>
                        <h3>رقم الطالب</h3>
                        <p id="student-noor-id">1234567890</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-school"></i>
                        <h3>المدرسة</h3>
                        <p id="student-school">مدرسة النموذجية الابتدائية</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-graduation-cap"></i>
                        <h3>الصف الدراسي</h3>
                        <p id="student-grade">الصف الثالث الابتدائي</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>تاريخ التسجيل</h3>
                        <p id="student-registration">15/09/2023</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-chart-line"></i>
                        <h3>إجمالي المشتريات</h3>
                        <p id="student-total-purchases">25 طلب</p>
                    </div>
                </div>

                <!-- إدارة البطاقات -->
                <div class="cards-management">
                    <div class="cards-header">
                        <h3><i class="fas fa-credit-card"></i> بطاقاتي المالية</h3>
                        <button class="add-card-btn" onclick="showAddCardModal()">
                            <i class="fas fa-plus"></i> إضافة بطاقة جديدة
                        </button>
                    </div>

                    <!-- شريط التنقل بين البطاقات -->
                    <div class="cards-navigation">
                        <button class="nav-btn prev-btn" onclick="previousCard()">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="cards-indicator" id="cards-indicator">
                            <span class="indicator active"></span>
                            <span class="indicator"></span>
                            <span class="indicator"></span>
                        </div>
                        <button class="nav-btn next-btn" onclick="nextCard()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <!-- حاوي البطاقات -->
                    <div class="cards-container" id="cards-container">
                        <!-- البطاقة الرئيسية -->
                        <div class="balance-card active" data-card-id="main">
                            <div class="balance-content">
                                <!-- رأس البطاقة -->
                                <div class="card-header">
                                    <div class="card-logo">
                                        <i class="fas fa-university"></i>
                                        <span>Smart Canteen</span>
                                    </div>
                                    <div class="card-type">Student</div>
                                </div>

                                <!-- شريحة البطاقة -->
                                <div class="card-chip"></div>

                                <!-- رقم البطاقة -->
                                <div class="card-number" id="card-number">
                                    **** **** **** 1234
                                </div>

                                <!-- معلومات الرصيد -->
                                <div class="balance-info">
                                    <div class="balance-label">الرصيد المتاح</div>
                                    <div class="balance-amount" id="student-balance">
                                        <i class="fas fa-coins"></i>
                                        <span>100.00</span> ريال
                                    </div>
                                </div>

                                <!-- معلومات الطالب -->
                                <div class="card-footer">
                                    <div class="card-holder">
                                        <div class="card-holder-label">اسم الطالب</div>
                                        <div class="card-holder-name" id="card-holder-name">أحمد محمد العلي</div>
                                    </div>
                                    <div class="card-expiry">
                                        <div class="card-holder-label">صالحة حتى</div>
                                        <div class="card-holder-name">12/25</div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="balance-actions">
                                <button onclick="showAddBalanceModal('main')">
                                    <i class="fas fa-plus-circle"></i> إضافة رصيد
                                </button>
                                <button onclick="showCardOptions('main')" class="card-options-btn">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإضافة السريعة -->
                    <div class="quick-add-amounts">
                        <button class="quick-add-btn" onclick="quickAddBalance(10)">
                            <i class="fas fa-plus"></i> 10 ر.س
                        </button>
                        <button class="quick-add-btn" onclick="quickAddBalance(20)">
                            <i class="fas fa-plus"></i> 20 ر.س
                        </button>
                        <button class="quick-add-btn" onclick="quickAddBalance(50)">
                            <i class="fas fa-plus"></i> 50 ر.س
                        </button>
                        <button class="quick-add-btn" onclick="quickAddBalance(100)">
                            <i class="fas fa-plus"></i> 100 ر.س
                        </button>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <h2>المنتجات المتاحة</h2>
                <div class="products-grid" id="products-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>


        </div>
    </section>

    <!-- زر العودة إلى الأعلى -->
    <button id="back-to-top" class="back-to-top-btn" title="العودة إلى الأعلى">
        <i class="fas fa-chevron-up"></i>
        <div class="btn-glow"></div>
    </button>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة للتحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم من sessionStorage
                let currentUser = null;
                try {
                    const userDataString = sessionStorage.getItem('currentUser');
                    if (userDataString) {
                        currentUser = JSON.parse(userDataString);
                    }
                } catch (sessionError) {
                    console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
                }

                // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
                if (!currentUser || !currentUser.role) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'student' && userId) {
                        // إنشاء كائن مستخدم بسيط
                        currentUser = {
                            id: parseInt(userId),
                            role: userRole,
                            name: 'الطالب',
                            balance: 0
                        };

                        // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                        try {
                            const db = getDatabase();
                            const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                            if (fullUserData) {
                                currentUser = fullUserData;
                                // تخزين البيانات الكاملة في sessionStorage
                                sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                            }
                        } catch (dbError) {
                            console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                        }
                    }
                }

                // التحقق من صلاحيات المستخدم
                if (!currentUser || currentUser.role !== 'student') {
                    alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                    window.location.href = '../auth/login.html';
                    return null;
                }

                return currentUser;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
                window.location.href = '../auth/login.html';
                return null;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صلاحيات المستخدم
            const currentUser = checkUserAuthorization();
            if (!currentUser) {
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // تحديث بيانات المستخدم من قاعدة البيانات إذا لزم الأمر
            const dbUser = db.users.find(u => u.id === currentUser.id);
            if (dbUser) {
                // دمج البيانات من قاعدة البيانات مع البيانات المحفوظة
                currentUser = {
                    ...currentUser,
                    ...dbUser,
                    // الاحتفاظ بالبيانات المهمة من sessionStorage
                    balance: dbUser.balance || currentUser.balance || 0
                };
                // تحديث sessionStorage
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            }

            // Get student info
            const school = db.schools.find(s => s.id === currentUser.schoolId);

            // Update student info
            updateStudentInfo(currentUser, school);
            updateBalanceDisplay(currentUser.balance || 0);

            // تحميل البطاقات
            loadStudentCards();

            // Populate products grid
            const productsGrid = document.getElementById('products-grid');
            db.products.slice(0, 4).forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <div class="product-image">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <div class="price">${product.price} ريال</div>
                        <div class="category">${product.category}</div>
                        <div class="product-actions">
                            <button class="order-btn" data-id="${product.id}">طلب</button>
                        </div>
                    </div>
                `;
                productsGrid.appendChild(productCard);
            });



            // Add Balance Button - تم إزالة هذا لأنه يتم التعامل معه في نظام البطاقات الجديد

            // Order Buttons
            document.querySelectorAll('.order-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = parseInt(this.getAttribute('data-id'));
                    const product = db.products.find(p => p.id === productId);

                    if (product) {
                        const quantity = prompt(`كم عدد ${product.name} تريد طلبه؟`, '1');

                        if (quantity && !isNaN(quantity) && quantity > 0) {
                            const totalPrice = product.price * parseInt(quantity);

                            // Check if student has enough balance
                            if ((currentUser.balance || 0) < totalPrice) {
                                alert('رصيدك غير كافي لإتمام هذا الطلب!');
                                return;
                            }

                            // Create new order
                            const newOrder = {
                                id: db.orders.length + 1,
                                userId: currentUser.id,
                                schoolId: currentUser.schoolId,
                                products: [
                                    { productId: productId, quantity: parseInt(quantity) }
                                ],
                                totalPrice: totalPrice,
                                status: 'pending',
                                date: new Date().toISOString().split('T')[0]
                            };

                            // Update database
                            db.orders.push(newOrder);

                            // Update user balance
                            currentUser.balance -= totalPrice;
                            const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                            if (userIndex !== -1) {
                                db.users[userIndex].balance = currentUser.balance;
                            }

                            // Save changes
                            saveDatabase(db);
                            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                            // Update display
                            updateBalanceDisplay(currentUser.balance);

                            alert('تم إنشاء الطلب بنجاح!');
                            window.location.reload();
                        } else if (quantity) {
                            alert('الرجاء إدخال كمية صحيحة!');
                        }
                    }
                });
            });

            // Logout functionality - Sidebar button
            document.getElementById('logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    try {
                        // حذف بيانات المستخدم من جميع وسائل التخزين
                        sessionStorage.clear();
                        localStorage.removeItem('currentUserRole');
                        localStorage.removeItem('currentUserId');

                        // حذف الكوكيز المتعلقة بالمستخدم
                        document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                        console.log('تم تسجيل الخروج بنجاح');

                        // التوجيه إلى صفحة تسجيل الدخول
                        window.location.href = '../auth/login.html';
                    } catch (error) {
                        console.error('خطأ في تسجيل الخروج:', error);
                        alert('حدث خطأ في تسجيل الخروج. سيتم إعادة تحميل الصفحة.');
                        window.location.reload();
                    }
                }
            });

            // Logout functionality - Header button
            document.getElementById('header-logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    try {
                        // حذف بيانات المستخدم من جميع وسائل التخزين
                        sessionStorage.clear();
                        localStorage.removeItem('currentUserRole');
                        localStorage.removeItem('currentUserId');

                        // حذف الكوكيز المتعلقة بالمستخدم
                        document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                        console.log('تم تسجيل الخروج بنجاح');

                        // التوجيه إلى صفحة تسجيل الدخول
                        window.location.href = '../auth/login.html';
                    } catch (error) {
                        console.error('خطأ في تسجيل الخروج:', error);
                        alert('حدث خطأ في تسجيل الخروج. سيتم إعادة تحميل الصفحة.');
                        window.location.reload();
                    }
                }
            });

            // زر العودة إلى الأعلى
            initBackToTopButton();
        });

        // وظيفة زر العودة إلى الأعلى
        function initBackToTopButton() {
            const backToTopBtn = document.getElementById('back-to-top');

            // إظهار/إخفاء الزر حسب موضع التمرير
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });

            // وظيفة العودة إلى الأعلى مع تأثير سلس
            backToTopBtn.addEventListener('click', function() {
                // تأثير بصري عند الضغط
                this.style.transform = 'translateY(-2px) scale(1.05) rotateZ(360deg)';

                // العودة إلى الأعلى بسلاسة
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });

                // إعادة تعيين التأثير البصري
                setTimeout(() => {
                    this.style.transform = '';
                }, 600);

                // تأثير اهتزاز خفيف
                this.classList.add('clicked');
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 300);
            });

            // تأثير إضافي عند hover
            backToTopBtn.addEventListener('mouseenter', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = '';
                }, 100);
            });
        }

        // Helper function to get grade text
        function getGradeText(grade) {
            switch(grade) {
                case '1':
                    return 'الصف الأول';
                case '2':
                    return 'الصف الثاني';
                case '3':
                    return 'الصف الثالث';
                case '4':
                    return 'الصف الرابع';
                case '5':
                    return 'الصف الخامس';
                case '6':
                    return 'الصف السادس';
                default:
                    return 'غير معروف';
            }
        }

        // Helper function to get order status text
        function getOrderStatusText(status) {
            switch(status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }

        // دالة تحديث معلومات الطالب
        function updateStudentInfo(currentUser, school) {
            // تحديث الاسم
            document.getElementById('student-name').textContent = currentUser.name || 'غير متوفر';

            // تحديث رقم نور مع التحقق من وجوده
            const noorId = currentUser.noorId || currentUser.studentId || currentUser.id || 'غير متوفر';
            document.getElementById('student-noor-id').textContent = noorId;

            // تحديث المدرسة
            document.getElementById('student-school').textContent = school ? school.name : 'غير معروف';

            // تحديث الصف
            document.getElementById('student-grade').textContent = getGradeText(currentUser.grade);

            // تاريخ التسجيل
            const registrationDate = currentUser.registrationDate || currentUser.createdAt?.split('T')[0] || '15/09/2023';
            document.getElementById('student-registration').textContent = registrationDate;

            // إجمالي المشتريات - حساب من قاعدة البيانات
            const db = getDatabase();
            const orders = db.orders ? db.orders.filter(order => order.userId === currentUser.id) : [];
            const totalPurchases = orders.length;
            document.getElementById('student-total-purchases').textContent = totalPurchases + ' طلب';

            // تحديث اسم حامل البطاقة
            const cardHolderElement = document.getElementById('card-holder-name');
            if (cardHolderElement) {
                cardHolderElement.textContent = (currentUser.name || 'غير متوفر').toUpperCase();
            }

            // تحديث رقم البطاقة (آخر 4 أرقام من رقم الطالب)
            const studentId = noorId !== 'غير متوفر' ? noorId : currentUser.id || '1234';
            const lastFourDigits = studentId.toString().slice(-4).padStart(4, '0');
            const cardNumberElement = document.getElementById('card-number');
            if (cardNumberElement) {
                cardNumberElement.textContent = `**** **** **** ${lastFourDigits}`;
            }

            // طباعة معلومات للتشخيص
            console.log('معلومات الطالب:', {
                name: currentUser.name,
                noorId: currentUser.noorId,
                studentId: currentUser.studentId,
                id: currentUser.id,
                finalNoorId: noorId
            });
        }

        // دالة تحديث عرض الرصيد
        function updateBalanceDisplay(balance) {
            // هذه الدالة لم تعد مستخدمة مع نظام البطاقات الجديد
            // يتم تحديث الرصيد من خلال updateCardsDisplay()
        }

        // تم نقل دالة عرض نافذة إضافة الرصيد إلى نظام البطاقات الجديد

        // تم نقل جميع دوال إدارة الرصيد إلى نظام البطاقات الجديد

        // دالة الإضافة السريعة للرصيد
        function quickAddBalance(amount) {
            // الحصول على البطاقة الحالية
            const currentCard = studentCards[currentCardIndex];
            if (!currentCard) return;

            // تحديث رصيد البطاقة
            currentCard.balance += amount;

            // إذا كانت البطاقة الرئيسية، تحديث رصيد المستخدم
            if (currentCard.isMain) {
                const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
                currentUser.balance = currentCard.balance;

                // تحديث في قاعدة البيانات
                const db = getDatabase();
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].balance = currentUser.balance;
                    saveDatabase(db);
                }
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            } else {
                // حفظ البطاقات الفرعية
                saveCards();
            }

            // تحديث العرض
            updateCardsDisplay();

            // إظهار رسالة نجاح
            showSuccessMessage(`تم إضافة ${amount} ريال إلى ${currentCard.name} بنجاح!`);
        }

        // دالة إظهار رسالة النجاح
        function showSuccessMessage(message) {
            const notification = document.createElement('div');
            notification.className = 'success-notification';
            notification.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // متغيرات إدارة البطاقات
        let currentCardIndex = 0;
        let studentCards = [];

        // تحميل بطاقات الطالب
        function loadStudentCards() {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            const savedCards = JSON.parse(localStorage.getItem(`cards_${currentUser.id}`)) || [];

            // البطاقة الرئيسية
            const mainCard = {
                id: 'main',
                name: 'البطاقة الرئيسية',
                type: 'student',
                balance: currentUser.balance || 0,
                isMain: true,
                createdAt: new Date().toISOString()
            };

            studentCards = [mainCard, ...savedCards];
            updateCardsDisplay();
        }

        // تحديث عرض البطاقات
        function updateCardsDisplay() {
            const container = document.getElementById('cards-container');
            const indicator = document.getElementById('cards-indicator');

            // تحديث المؤشرات
            indicator.innerHTML = studentCards.map((_, index) =>
                `<span class="indicator ${index === currentCardIndex ? 'active' : ''}" onclick="goToCard(${index})"></span>`
            ).join('');

            // تحديث البطاقات
            container.innerHTML = studentCards.map((card, index) => {
                const cardClass = index === currentCardIndex ? 'active' :
                                 index < currentCardIndex ? 'prev' : 'next';

                return createCardHTML(card, cardClass);
            }).join('');

            // تحديث أزرار التنقل
            document.querySelector('.prev-btn').disabled = currentCardIndex === 0;
            document.querySelector('.next-btn').disabled = currentCardIndex === studentCards.length - 1;
        }

        // إنشاء HTML للبطاقة
        function createCardHTML(card, cardClass) {
            const cardTypeClass = card.type !== 'student' ? card.type : '';
            const cardTypeName = getCardTypeName(card.type);

            return `
                <div class="balance-card ${cardClass} ${cardTypeClass}" data-card-id="${card.id}">
                    <div class="balance-content">
                        <div class="card-header">
                            <div class="card-logo">
                                <i class="fas fa-university"></i>
                                <span>Smart Canteen</span>
                            </div>
                            <div class="card-type">${cardTypeName}</div>
                        </div>

                        <div class="card-chip"></div>

                        <div class="card-number">
                            **** **** **** ${generateCardNumber(card.id)}
                        </div>

                        <div class="balance-info">
                            <div class="balance-label">الرصيد المتاح</div>
                            <div class="balance-amount">
                                <i class="fas fa-coins"></i>
                                <span>${card.balance.toFixed(2)}</span> ريال
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="card-holder">
                                <div class="card-holder-label">اسم الطالب</div>
                                <div class="card-holder-name">${card.holderName || getCurrentUserName()}</div>
                            </div>
                            <div class="card-expiry">
                                <div class="card-holder-label">رقم نور</div>
                                <div class="card-holder-name">${getCurrentUserNoorId()}</div>
                            </div>
                        </div>
                    </div>

                    <div class="balance-actions">
                        <button onclick="showAddBalanceModal('${card.id}')">
                            <i class="fas fa-plus-circle"></i> إضافة رصيد
                        </button>
                        ${!card.isMain ? `
                            <button onclick="showCardOptions('${card.id}')" class="card-options-btn">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // دوال مساعدة للحصول على بيانات المستخدم الحالي
        function getCurrentUserName() {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            return currentUser?.name || 'غير متوفر';
        }

        function getCurrentUserNoorId() {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            return currentUser?.noorId || currentUser?.studentId || currentUser?.id || 'غير متوفر';
        }

        // الحصول على اسم نوع البطاقة
        function getCardTypeName(type) {
            const types = {
                'student': 'Student',
                'savings': 'Savings',
                'premium': 'Premium',
                'family': 'Family',
                'gift': 'Gift Card'
            };
            return types[type] || 'Student';
        }

        // توليد رقم البطاقة
        function generateCardNumber(cardId) {
            if (cardId === 'main') {
                const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
                const noorId = currentUser.noorId || currentUser.studentId || currentUser.id || '1234';
                return noorId.toString().slice(-4).padStart(4, '0');
            }
            return Math.random().toString().slice(-4).padStart(4, '0');
        }

        // التنقل بين البطاقات
        function previousCard() {
            if (currentCardIndex > 0) {
                currentCardIndex--;
                updateCardsDisplay();
            }
        }

        function nextCard() {
            if (currentCardIndex < studentCards.length - 1) {
                currentCardIndex++;
                updateCardsDisplay();
            }
        }

        function goToCard(index) {
            currentCardIndex = index;
            updateCardsDisplay();
        }

        // عرض نافذة إضافة بطاقة جديدة
        function showAddCardModal() {
            const modal = document.createElement('div');
            modal.className = 'add-card-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeAddCardModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus-circle"></i> إضافة بطاقة جديدة</h3>
                        <button class="close-btn" onclick="closeAddCardModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="fee-notice">
                            <div class="fee-notice-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="fee-notice-content">
                                <h4>رسوم إنشاء البطاقة</h4>
                                <p>تطبق رسوم قدرها <strong>25 ريال</strong> على جميع البطاقات الجديدة</p>
                            </div>
                        </div>

                        <div class="card-types-grid">
                            <div class="card-type-option" data-type="savings" onclick="selectCardType('savings')">
                                <i class="fas fa-piggy-bank"></i>
                                <h4>بطاقة توفير</h4>
                                <p>للادخار والتوفير</p>
                                <div class="card-fee-badge">25 ر.س</div>
                            </div>
                            <div class="card-type-option" data-type="premium" onclick="selectCardType('premium')">
                                <i class="fas fa-crown"></i>
                                <h4>بطاقة مميزة</h4>
                                <p>مزايا إضافية</p>
                                <div class="card-fee-badge">25 ر.س</div>
                            </div>
                            <div class="card-type-option" data-type="family" onclick="selectCardType('family')">
                                <i class="fas fa-users"></i>
                                <h4>بطاقة عائلية</h4>
                                <p>للاستخدام العائلي</p>
                                <div class="card-fee-badge">25 ر.س</div>
                            </div>
                            <div class="card-type-option" data-type="gift" onclick="selectCardType('gift')">
                                <i class="fas fa-gift"></i>
                                <h4>بطاقة هدية</h4>
                                <p>للهدايا والمناسبات</p>
                                <div class="card-fee-badge">25 ر.س</div>
                            </div>
                        </div>
                        <div class="amount-input-section">
                            <label for="initial-balance">الرصيد الأولي (اختياري):</label>
                            <div class="amount-input-wrapper">
                                <input type="number" id="initial-balance" placeholder="0.00" min="0" max="1000" step="0.01">
                                <span class="currency">ريال</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="cancel-btn" onclick="closeAddCardModal()">إلغاء</button>
                        <button class="confirm-btn" onclick="createNewCard()">
                            <i class="fas fa-check"></i> إنشاء البطاقة
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // إغلاق نافذة إضافة البطاقة
        function closeAddCardModal() {
            const modal = document.querySelector('.add-card-modal');
            if (modal) {
                modal.remove();
            }
        }

        // اختيار نوع البطاقة
        let selectedCardType = null;
        function selectCardType(type) {
            selectedCardType = type;
            document.querySelectorAll('.card-type-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
        }

        // إنشاء بطاقة جديدة
        function createNewCard() {
            if (!selectedCardType) {
                alert('الرجاء اختيار نوع البطاقة');
                return;
            }

            const initialBalance = parseFloat(document.getElementById('initial-balance').value) || 0;
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            const cardFee = 25; // رسوم البطاقة الجديدة
            const totalCost = cardFee + initialBalance;

            // فحص الرصيد الكافي
            if (currentUser.balance < totalCost) {
                showInsufficientBalanceModal(totalCost, currentUser.balance);
                return;
            }

            // عرض نافذة تأكيد الرسوم
            showCardFeeConfirmation(selectedCardType, initialBalance, cardFee, totalCost);
        }

        // عرض نافذة تأكيد رسوم البطاقة
        function showCardFeeConfirmation(cardType, initialBalance, cardFee, totalCost) {
            const modal = document.createElement('div');
            modal.className = 'card-fee-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeCardFeeModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-receipt"></i> تأكيد إنشاء البطاقة</h3>
                        <button class="close-btn" onclick="closeCardFeeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="fee-breakdown">
                            <div class="fee-item">
                                <div class="fee-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="fee-details">
                                    <h4>${getCardTypeName(cardType)}</h4>
                                    <p>بطاقة جديدة عالية الجودة</p>
                                </div>
                                <div class="fee-amount">
                                    ${cardFee.toFixed(2)} ريال
                                </div>
                            </div>

                            ${initialBalance > 0 ? `
                                <div class="fee-item">
                                    <div class="fee-icon">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                    <div class="fee-details">
                                        <h4>الرصيد الأولي</h4>
                                        <p>مبلغ يضاف للبطاقة الجديدة</p>
                                    </div>
                                    <div class="fee-amount">
                                        ${initialBalance.toFixed(2)} ريال
                                    </div>
                                </div>
                            ` : ''}

                            <div class="fee-separator"></div>

                            <div class="fee-total">
                                <div class="fee-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="fee-details">
                                    <h4>المجموع الكلي</h4>
                                    <p>سيتم خصمه من رصيدك الرئيسي</p>
                                </div>
                                <div class="fee-amount total">
                                    ${totalCost.toFixed(2)} ريال
                                </div>
                            </div>
                        </div>

                        <div class="balance-info">
                            <div class="current-balance">
                                <i class="fas fa-wallet"></i>
                                <span>رصيدك الحالي: ${JSON.parse(sessionStorage.getItem('currentUser')).balance.toFixed(2)} ريال</span>
                            </div>
                            <div class="remaining-balance">
                                <i class="fas fa-arrow-down"></i>
                                <span>الرصيد المتبقي: ${(JSON.parse(sessionStorage.getItem('currentUser')).balance - totalCost).toFixed(2)} ريال</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="cancel-btn" onclick="closeCardFeeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button class="confirm-btn" onclick="confirmCardCreation('${cardType}', ${initialBalance}, ${cardFee})">
                            <i class="fas fa-check"></i> تأكيد وإنشاء البطاقة
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // عرض نافذة الرصيد غير الكافي
        function showInsufficientBalanceModal(requiredAmount, currentBalance) {
            const shortfall = requiredAmount - currentBalance;

            const modal = document.createElement('div');
            modal.className = 'insufficient-balance-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeInsufficientBalanceModal()"></div>
                <div class="modal-content">
                    <div class="modal-header error">
                        <h3><i class="fas fa-exclamation-triangle"></i> رصيد غير كافي</h3>
                        <button class="close-btn" onclick="closeInsufficientBalanceModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="balance-comparison">
                            <div class="balance-item required">
                                <div class="balance-icon">
                                    <i class="fas fa-hand-holding-usd"></i>
                                </div>
                                <div class="balance-details">
                                    <h4>المبلغ المطلوب</h4>
                                    <p>لإنشاء البطاقة</p>
                                </div>
                                <div class="balance-amount">
                                    ${requiredAmount.toFixed(2)} ريال
                                </div>
                            </div>

                            <div class="balance-item current">
                                <div class="balance-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="balance-details">
                                    <h4>رصيدك الحالي</h4>
                                    <p>في البطاقة الرئيسية</p>
                                </div>
                                <div class="balance-amount">
                                    ${currentBalance.toFixed(2)} ريال
                                </div>
                            </div>

                            <div class="balance-item shortage">
                                <div class="balance-icon">
                                    <i class="fas fa-minus-circle"></i>
                                </div>
                                <div class="balance-details">
                                    <h4>النقص في الرصيد</h4>
                                    <p>تحتاج لإضافة</p>
                                </div>
                                <div class="balance-amount">
                                    ${shortfall.toFixed(2)} ريال
                                </div>
                            </div>
                        </div>

                        <div class="suggestion-box">
                            <i class="fas fa-lightbulb"></i>
                            <div>
                                <h4>اقتراح:</h4>
                                <p>يمكنك إضافة رصيد إلى بطاقتك الرئيسية أولاً، ثم إنشاء البطاقة الجديدة.</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="cancel-btn" onclick="closeInsufficientBalanceModal()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                        <button class="add-balance-btn" onclick="addBalanceForCard(${shortfall})">
                            <i class="fas fa-plus-circle"></i> إضافة ${shortfall.toFixed(2)} ريال
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // تأكيد إنشاء البطاقة مع الرسوم
        function confirmCardCreation(cardType, initialBalance, cardFee) {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            const totalCost = cardFee + initialBalance;

            // إنشاء البطاقة الجديدة
            const newCard = {
                id: Date.now().toString(),
                name: getCardTypeName(cardType),
                type: cardType,
                balance: initialBalance,
                isMain: false,
                createdAt: new Date().toISOString(),
                holderName: currentUser.name,
                cardFee: cardFee
            };

            // خصم التكلفة الإجمالية من الرصيد الرئيسي
            currentUser.balance -= totalCost;
            studentCards[0].balance = currentUser.balance;

            // تحديث في قاعدة البيانات
            const db = getDatabase();
            const userIndex = db.users.findIndex(u => u.id === currentUser.id);
            if (userIndex !== -1) {
                db.users[userIndex].balance = currentUser.balance;
                saveDatabase(db);
            }
            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

            // إضافة البطاقة الجديدة
            studentCards.push(newCard);

            // حفظ البطاقات
            const cardsToSave = studentCards.filter(card => !card.isMain);
            localStorage.setItem(`cards_${currentUser.id}`, JSON.stringify(cardsToSave));

            // الانتقال للبطاقة الجديدة
            currentCardIndex = studentCards.length - 1;
            updateCardsDisplay();

            // إغلاق النوافذ
            closeCardFeeModal();
            closeAddCardModal();

            // إظهار رسالة نجاح مفصلة
            showCardCreationSuccess(newCard, cardFee, totalCost);
        }

        // إظهار رسالة نجاح إنشاء البطاقة
        function showCardCreationSuccess(card, cardFee, totalCost) {
            const modal = document.createElement('div');
            modal.className = 'success-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeSuccessModal()"></div>
                <div class="modal-content">
                    <div class="modal-header success">
                        <h3><i class="fas fa-check-circle"></i> تم إنشاء البطاقة بنجاح!</h3>
                        <button class="close-btn" onclick="closeSuccessModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="success-animation">
                            <div class="checkmark">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>

                        <div class="card-summary">
                            <h4>${card.name}</h4>
                            <p>تم إنشاؤها وإضافتها إلى محفظتك</p>

                            <div class="transaction-summary">
                                <div class="summary-item">
                                    <span>رسوم البطاقة:</span>
                                    <span>${cardFee.toFixed(2)} ريال</span>
                                </div>
                                ${card.balance > 0 ? `
                                    <div class="summary-item">
                                        <span>الرصيد الأولي:</span>
                                        <span>${card.balance.toFixed(2)} ريال</span>
                                    </div>
                                ` : ''}
                                <div class="summary-item total">
                                    <span>إجمالي المخصوم:</span>
                                    <span>${totalCost.toFixed(2)} ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="confirm-btn" onclick="closeSuccessModal()">
                            <i class="fas fa-thumbs-up"></i> ممتاز!
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إغلاق تلقائي بعد 5 ثوان
            setTimeout(() => {
                closeSuccessModal();
            }, 5000);
        }

        // دوال إغلاق النوافذ
        function closeCardFeeModal() {
            const modal = document.querySelector('.card-fee-modal');
            if (modal) modal.remove();
        }

        function closeInsufficientBalanceModal() {
            const modal = document.querySelector('.insufficient-balance-modal');
            if (modal) modal.remove();
        }

        function closeSuccessModal() {
            const modal = document.querySelector('.success-modal');
            if (modal) modal.remove();
        }

        // إضافة رصيد سريع للبطاقة
        function addBalanceForCard(amount) {
            closeInsufficientBalanceModal();
            showAddBalanceModal('main');

            // تعبئة المبلغ المطلوب تلقائياً
            setTimeout(() => {
                const amountInput = document.getElementById('custom-amount');
                if (amountInput) {
                    amountInput.value = Math.ceil(amount);
                    amountInput.focus();
                }
            }, 200);
        }

        // عرض خيارات البطاقة
        function showCardOptions(cardId) {
            const card = studentCards.find(c => c.id === cardId);
            if (!card) return;

            const modal = document.createElement('div');
            modal.className = 'card-options-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeCardOptionsModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-cog"></i> خيارات البطاقة</h3>
                        <button class="close-btn" onclick="closeCardOptionsModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="card-options-list">
                            <button class="card-option-btn" onclick="transferBalance('${cardId}')">
                                <i class="fas fa-exchange-alt"></i>
                                <span>تحويل رصيد</span>
                            </button>
                            <button class="card-option-btn" onclick="freezeCard('${cardId}')">
                                <i class="fas fa-pause"></i>
                                <span>تجميد البطاقة</span>
                            </button>
                            <button class="card-option-btn" onclick="renameCard('${cardId}')">
                                <i class="fas fa-edit"></i>
                                <span>إعادة تسمية</span>
                            </button>
                            <button class="card-option-btn danger" onclick="deleteCard('${cardId}')">
                                <i class="fas fa-trash"></i>
                                <span>حذف البطاقة</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // إغلاق نافذة خيارات البطاقة
        function closeCardOptionsModal() {
            const modal = document.querySelector('.card-options-modal');
            if (modal) {
                modal.remove();
            }
        }

        // تحويل رصيد بين البطاقات
        function transferBalance(fromCardId) {
            const fromCard = studentCards.find(c => c.id === fromCardId);
            if (!fromCard || fromCard.balance <= 0) {
                alert('لا يوجد رصيد كافي في هذه البطاقة');
                return;
            }

            const amount = prompt(`كم تريد تحويل من ${fromCard.name}؟\nالرصيد المتاح: ${fromCard.balance.toFixed(2)} ريال`);
            if (!amount || isNaN(amount) || amount <= 0) return;

            const transferAmount = parseFloat(amount);
            if (transferAmount > fromCard.balance) {
                alert('المبلغ أكبر من الرصيد المتاح');
                return;
            }

            // اختيار البطاقة المستهدفة
            const targetOptions = studentCards
                .filter(c => c.id !== fromCardId)
                .map((c, index) => `${index + 1}. ${c.name} (${c.balance.toFixed(2)} ريال)`)
                .join('\n');

            const targetChoice = prompt(`اختر البطاقة المستهدفة:\n${targetOptions}`);
            if (!targetChoice || isNaN(targetChoice)) return;

            const targetIndex = parseInt(targetChoice) - 1;
            const availableCards = studentCards.filter(c => c.id !== fromCardId);

            if (targetIndex < 0 || targetIndex >= availableCards.length) {
                alert('اختيار غير صحيح');
                return;
            }

            const targetCard = availableCards[targetIndex];

            // تنفيذ التحويل
            fromCard.balance -= transferAmount;
            targetCard.balance += transferAmount;

            // حفظ التغييرات
            saveCards();
            updateCardsDisplay();
            closeCardOptionsModal();

            showSuccessMessage(`تم تحويل ${transferAmount.toFixed(2)} ريال من ${fromCard.name} إلى ${targetCard.name}`);
        }

        // حذف البطاقة
        function deleteCard(cardId) {
            const card = studentCards.find(c => c.id === cardId);
            if (!card || card.isMain) return;

            // عرض نافذة تأكيد الحذف المتقدمة
            showDeleteCardConfirmation(card);
        }

        // عرض نافذة تأكيد حذف البطاقة
        function showDeleteCardConfirmation(card) {
            const modal = document.createElement('div');
            modal.className = 'delete-card-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeDeleteCardModal()"></div>
                <div class="modal-content">
                    <div class="modal-header danger">
                        <h3><i class="fas fa-exclamation-triangle"></i> تأكيد حذف البطاقة</h3>
                        <button class="close-btn" onclick="closeDeleteCardModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="warning-message">
                            <div class="warning-icon">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <div class="warning-content">
                                <h4>هل أنت متأكد من حذف هذه البطاقة؟</h4>
                                <p>هذا الإجراء لا يمكن التراجع عنه</p>
                            </div>
                        </div>

                        <div class="card-info">
                            <div class="card-preview">
                                <div class="card-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="card-details">
                                    <h4>${card.name}</h4>
                                    <p>تم إنشاؤها في: ${new Date(card.createdAt).toLocaleDateString('ar-SA')}</p>
                                </div>
                                <div class="card-balance">
                                    ${card.balance.toFixed(2)} ريال
                                </div>
                            </div>
                        </div>

                        ${card.balance > 0 ? `
                            <div class="balance-transfer-info">
                                <div class="transfer-icon">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="transfer-details">
                                    <h4>تحويل الرصيد</h4>
                                    <p>سيتم تحويل الرصيد المتبقي (${card.balance.toFixed(2)} ريال) إلى البطاقة الرئيسية تلقائياً</p>
                                </div>
                            </div>
                        ` : ''}

                        <div class="confirmation-input">
                            <label for="delete-confirmation">اكتب "حذف" للتأكيد:</label>
                            <input type="text" id="delete-confirmation" placeholder="حذف" maxlength="3">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="cancel-btn" onclick="closeDeleteCardModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button class="delete-btn" onclick="confirmDeleteCard('${card.id}')" disabled>
                            <i class="fas fa-trash"></i> حذف البطاقة نهائياً
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // تفعيل زر الحذف عند كتابة "حذف"
            const confirmationInput = document.getElementById('delete-confirmation');
            const deleteBtn = document.querySelector('.delete-btn');

            confirmationInput.addEventListener('input', function() {
                if (this.value.trim() === 'حذف') {
                    deleteBtn.disabled = false;
                    deleteBtn.classList.add('enabled');
                } else {
                    deleteBtn.disabled = true;
                    deleteBtn.classList.remove('enabled');
                }
            });

            // التركيز على حقل الإدخال
            setTimeout(() => {
                confirmationInput.focus();
            }, 100);
        }

        // تأكيد حذف البطاقة
        function confirmDeleteCard(cardId) {
            const card = studentCards.find(c => c.id === cardId);
            if (!card) return;

            // تحويل الرصيد إلى البطاقة الرئيسية
            if (card.balance > 0) {
                studentCards[0].balance += card.balance;

                // تحديث رصيد المستخدم الرئيسي
                const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
                currentUser.balance = studentCards[0].balance;

                // تحديث في قاعدة البيانات
                const db = getDatabase();
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].balance = currentUser.balance;
                    saveDatabase(db);
                }
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            }

            // حذف البطاقة
            studentCards = studentCards.filter(c => c.id !== cardId);

            // تعديل الفهرس الحالي إذا لزم الأمر
            if (currentCardIndex >= studentCards.length) {
                currentCardIndex = studentCards.length - 1;
            }

            saveCards();
            updateCardsDisplay();
            closeDeleteCardModal();
            closeCardOptionsModal();

            // إظهار رسالة نجاح الحذف
            showCardDeletionSuccess(card);
        }

        // إظهار رسالة نجاح حذف البطاقة
        function showCardDeletionSuccess(card) {
            const modal = document.createElement('div');
            modal.className = 'success-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeSuccessModal()"></div>
                <div class="modal-content">
                    <div class="modal-header success">
                        <h3><i class="fas fa-check-circle"></i> تم حذف البطاقة بنجاح</h3>
                        <button class="close-btn" onclick="closeSuccessModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="success-animation">
                            <div class="checkmark">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>

                        <div class="deletion-summary">
                            <h4>تم حذف ${card.name}</h4>
                            <p>البطاقة لم تعد متاحة في محفظتك</p>

                            ${card.balance > 0 ? `
                                <div class="balance-transfer-summary">
                                    <div class="summary-item">
                                        <span>الرصيد المحول:</span>
                                        <span>${card.balance.toFixed(2)} ريال</span>
                                    </div>
                                    <div class="summary-item">
                                        <span>إلى:</span>
                                        <span>البطاقة الرئيسية</span>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="confirm-btn" onclick="closeSuccessModal()">
                            <i class="fas fa-thumbs-up"></i> تم
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إغلاق تلقائي بعد 3 ثوان
            setTimeout(() => {
                closeSuccessModal();
            }, 3000);
        }

        // إغلاق نافذة حذف البطاقة
        function closeDeleteCardModal() {
            const modal = document.querySelector('.delete-card-modal');
            if (modal) modal.remove();
        }

        // حفظ البطاقات
        function saveCards() {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            const cardsToSave = studentCards.filter(card => !card.isMain);
            localStorage.setItem(`cards_${currentUser.id}`, JSON.stringify(cardsToSave));
        }

        // تحديث دالة إضافة الرصيد للعمل مع البطاقات المتعددة
        function showAddBalanceModal(cardId = 'main') {
            const card = studentCards.find(c => c.id === cardId);
            if (!card) return;

            const modal = document.createElement('div');
            modal.className = 'add-balance-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeAddBalanceModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-plus-circle"></i> إضافة رصيد - ${card.name}</h3>
                        <button class="close-btn" onclick="closeAddBalanceModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="amount-input-section">
                            <label for="custom-amount">أدخل المبلغ المطلوب:</label>
                            <div class="amount-input-wrapper">
                                <input type="number" id="custom-amount" placeholder="0.00" min="1" max="1000" step="0.01">
                                <span class="currency">ريال</span>
                            </div>
                        </div>
                        <div class="quick-amounts">
                            <h4>مبالغ سريعة:</h4>
                            <div class="quick-amounts-grid">
                                <button class="quick-amount-btn" onclick="setAmount(10)">10 ريال</button>
                                <button class="quick-amount-btn" onclick="setAmount(20)">20 ريال</button>
                                <button class="quick-amount-btn" onclick="setAmount(50)">50 ريال</button>
                                <button class="quick-amount-btn" onclick="setAmount(100)">100 ريال</button>
                                <button class="quick-amount-btn" onclick="setAmount(200)">200 ريال</button>
                                <button class="quick-amount-btn" onclick="setAmount(500)">500 ريال</button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="cancel-btn" onclick="closeAddBalanceModal()">إلغاء</button>
                        <button class="confirm-btn" onclick="confirmAddBalance('${cardId}')">
                            <i class="fas fa-check"></i> إضافة الرصيد
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Focus on input
            setTimeout(() => {
                document.getElementById('custom-amount').focus();
            }, 100);
        }

        // تأكيد إضافة الرصيد للبطاقة المحددة
        function confirmAddBalance(cardId = 'main') {
            const amountInput = document.getElementById('custom-amount');
            const amount = parseFloat(amountInput.value);

            if (!amount || amount <= 0) {
                alert('الرجاء إدخال مبلغ صحيح!');
                amountInput.focus();
                return;
            }

            if (amount > 1000) {
                alert('الحد الأقصى للإضافة هو 1000 ريال!');
                amountInput.focus();
                return;
            }

            const card = studentCards.find(c => c.id === cardId);
            if (!card) return;

            // تحديث رصيد البطاقة
            card.balance += amount;

            // إذا كانت البطاقة الرئيسية، تحديث رصيد المستخدم
            if (card.isMain) {
                const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
                currentUser.balance = card.balance;

                // تحديث في قاعدة البيانات
                const db = getDatabase();
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].balance = currentUser.balance;
                    saveDatabase(db);
                }
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            } else {
                // حفظ البطاقات الفرعية
                saveCards();
            }

            // تحديث العرض
            updateCardsDisplay();

            // إغلاق النافذة
            closeAddBalanceModal();

            // إظهار رسالة نجاح
            showSuccessMessage(`تم إضافة ${amount.toFixed(2)} ريال إلى ${card.name} بنجاح!`);
        }

        // دالة تعيين المبلغ في نافذة إضافة الرصيد
        function setAmount(amount) {
            const amountInput = document.getElementById('custom-amount');
            if (amountInput) {
                amountInput.value = amount;
            }
        }

        // دالة إغلاق نافذة إضافة الرصيد
        function closeAddBalanceModal() {
            const modal = document.querySelector('.add-balance-modal');
            if (modal) {
                modal.remove();
            }
        }
    </script>
</body>
</html>
