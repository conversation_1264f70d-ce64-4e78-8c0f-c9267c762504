/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * ملف JavaScript الخاص بلوحة تحكم المدير
 */

/**
 * تهيئة لوحة التحكم
 */
function initializeDashboard() {
    try {
        // التحقق من صلاحيات المستخدم
        const currentUser = checkUserAuthorization();
        if (!currentUser) return;

        // تعيين اسم المستخدم
        document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

        // تعيين التاريخ الحالي
        updateCurrentDate();

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحديث الإحصائيات
        updateStats(db);

        // تحميل آخر المدارس
        loadLatestSchools(db);

        // تحميل آخر الطلبات
        loadLatestOrders(db);

        // تحميل آخر الأنشطة
        loadLatestActivities(db);

        // تحميل الإشعارات
        loadNotifications(db);

        // إنشاء الرسوم البيانية
        createCharts(db);

        // تهيئة الإجراءات السريعة
        initializeQuickActions();

        // تهيئة مستمعي الأحداث
        initializeEventListeners();
    } catch (error) {
        console.error('خطأ في تهيئة لوحة التحكم:', error);
    }
}

/**
 * تحديث التاريخ الحالي
 */
function updateCurrentDate() {
    try {
        const now = new Date();
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
    } catch (error) {
        console.error('خطأ في تحديث التاريخ الحالي:', error);
    }
}

/**
 * تحديث الإحصائيات
 * @param {Object} db - قاعدة البيانات
 */
function updateStats(db) {
    try {
        // إحصائيات المدارس
        const schoolsCount = db.schools ? db.schools.length : 0;
        document.getElementById('schools-count').textContent = schoolsCount;

        // إحصائيات الطلاب
        const studentsCount = db.users ? db.users.filter(user => user.role === 'student').length : 0;
        document.getElementById('students-count').textContent = studentsCount;

        // إحصائيات المنتجات
        const productsCount = db.products ? db.products.length : 0;
        document.getElementById('products-count').textContent = productsCount;

        // إحصائيات الطلبات
        const ordersCount = db.orders ? db.orders.length : 0;
        document.getElementById('orders-count').textContent = ordersCount;

        // إحصائيات العاملين
        const staffCount = db.users ? db.users.filter(user => user.role === 'staff').length : 0;
        document.getElementById('staff-count').textContent = staffCount;

        // إحصائيات أولياء الأمور
        const parentsCount = db.users ? db.users.filter(user => user.role === 'parent').length : 0;
        document.getElementById('parents-count').textContent = parentsCount;

        // إجمالي المبيعات
        const totalSales = db.orders ? db.orders.reduce((sum, order) => sum + order.totalPrice, 0) : 0;
        document.getElementById('total-sales').textContent = totalSales + ' ريال';

        // إضافة تأثيرات حركية للإحصائيات
        animateStats();
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

/**
 * إضافة تأثيرات حركية للإحصائيات
 */
function animateStats() {
    try {
        const statValues = document.querySelectorAll('.stat-value');

        statValues.forEach(statValue => {
            const finalValue = parseInt(statValue.textContent.replace(/[^\d]/g, ''));

            // إعادة تعيين القيمة إلى صفر
            statValue.textContent = '0';

            // تحريك القيمة من صفر إلى القيمة النهائية
            let currentValue = 0;
            const duration = 1500; // مدة التحريك بالمللي ثانية
            const interval = 16; // فترة التحديث بالمللي ثانية
            const steps = duration / interval;
            const increment = finalValue / steps;

            const animation = setInterval(() => {
                currentValue += increment;

                if (currentValue >= finalValue) {
                    clearInterval(animation);
                    if (statValue.id === 'total-sales') {
                        statValue.textContent = finalValue + ' ريال';
                    } else {
                        statValue.textContent = finalValue;
                    }
                } else {
                    if (statValue.id === 'total-sales') {
                        statValue.textContent = Math.floor(currentValue) + ' ريال';
                    } else {
                        statValue.textContent = Math.floor(currentValue);
                    }
                }
            }, interval);
        });
    } catch (error) {
        console.error('خطأ في تحريك الإحصائيات:', error);
    }
}

/**
 * تحميل آخر المدارس
 * @param {Object} db - قاعدة البيانات
 */
function loadLatestSchools(db) {
    try {
        const schoolsTable = document.getElementById('schools-table');
        if (!schoolsTable) return;

        // مسح المحتوى الحالي
        schoolsTable.innerHTML = '';

        // ترتيب المدارس حسب الأحدث (في هذه الحالة نفترض أن المدارس ذات المعرفات الأكبر هي الأحدث)
        const sortedSchools = [...db.schools].sort((a, b) => b.id - a.id).slice(0, 5);

        // إضافة المدارس إلى الجدول
        sortedSchools.forEach(school => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${school.id}</td>
                <td>${school.name}</td>
                <td>${school.address || '-'}</td>
                <td>${school.phone || '-'}</td>
                <td>${school.email || '-'}</td>
                <td>
                    <button class="action-btn view-btn" data-id="${school.id}" title="عرض"><i class="fas fa-eye"></i></button>
                    <button class="action-btn edit-btn" data-id="${school.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete-btn" data-id="${school.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
                </td>
            `;
            schoolsTable.appendChild(row);
        });

        // إضافة مستمعي الأحداث لأزرار الإجراءات
        addSchoolActionListeners();
    } catch (error) {
        console.error('خطأ في تحميل آخر المدارس:', error);
    }
}

/**
 * تحميل آخر الطلبات
 * @param {Object} db - قاعدة البيانات
 */
function loadLatestOrders(db) {
    try {
        const ordersTable = document.getElementById('orders-table');
        if (!ordersTable) return;

        // مسح المحتوى الحالي
        ordersTable.innerHTML = '';

        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
        const sortedOrders = [...db.orders].sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);

        // إضافة الطلبات إلى الجدول
        sortedOrders.forEach(order => {
            const school = db.schools.find(s => s.id === order.schoolId);
            const user = db.users.find(u => u.id === order.userId);

            // تحديد لون حالة الطلب
            let statusClass = '';
            switch (order.status) {
                case 'completed':
                    statusClass = 'status-success';
                    break;
                case 'pending':
                    statusClass = 'status-warning';
                    break;
                case 'cancelled':
                    statusClass = 'status-danger';
                    break;
                default:
                    statusClass = 'status-info';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${order.id}</td>
                <td>${school ? school.name : 'غير معروف'}</td>
                <td>${user ? user.name : 'غير معروف'}</td>
                <td>${order.totalPrice} ريال</td>
                <td>${formatDate(order.date)}</td>
                <td><span class="status-badge ${statusClass}">${getOrderStatusText(order.status)}</span></td>
                <td>
                    <button class="action-btn view-btn" data-id="${order.id}" title="عرض"><i class="fas fa-eye"></i></button>
                </td>
            `;
            ordersTable.appendChild(row);
        });

        // إضافة مستمعي الأحداث لأزرار الإجراءات
        addOrderActionListeners();
    } catch (error) {
        console.error('خطأ في تحميل آخر الطلبات:', error);
    }
}

/**
 * تحميل آخر الأنشطة
 * @param {Object} db - قاعدة البيانات
 */
function loadLatestActivities(db) {
    try {
        const activitiesList = document.getElementById('activities-list');
        if (!activitiesList) return;

        // مسح المحتوى الحالي
        activitiesList.innerHTML = '';

        // إذا لم تكن هناك أنشطة، نعرض رسالة
        if (!db.activities || db.activities.length === 0) {
            activitiesList.innerHTML = '<div class="no-data">لا توجد أنشطة حديثة</div>';
            return;
        }

        // ترتيب الأنشطة حسب التاريخ (الأحدث أولاً)
        const sortedActivities = [...db.activities].sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);

        // إضافة الأنشطة إلى القائمة
        sortedActivities.forEach(activity => {
            const user = db.users.find(u => u.id === activity.userId);

            // تحديد أيقونة النشاط
            let icon = '';
            switch (activity.type) {
                case 'login':
                    icon = 'fa-sign-in-alt';
                    break;
                case 'order':
                    icon = 'fa-shopping-cart';
                    break;
                case 'payment':
                    icon = 'fa-money-bill-wave';
                    break;
                case 'user':
                    icon = 'fa-user';
                    break;
                case 'product':
                    icon = 'fa-box';
                    break;
                default:
                    icon = 'fa-info-circle';
            }

            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="activity-details">
                    <div class="activity-text">${activity.description}</div>
                    <div class="activity-meta">
                        <span class="activity-user">${user ? user.name : 'مستخدم غير معروف'}</span>
                        <span class="activity-time">${formatDateTime(activity.date)}</span>
                    </div>
                </div>
            `;
            activitiesList.appendChild(activityItem);
        });
    } catch (error) {
        console.error('خطأ في تحميل آخر الأنشطة:', error);
    }
}

/**
 * تنسيق التاريخ
 * @param {string} dateString - سلسلة التاريخ
 * @returns {string} - التاريخ المنسق
 */
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    } catch (error) {
        console.error('خطأ في تنسيق التاريخ:', error);
        return dateString;
    }
}

/**
 * تنسيق التاريخ والوقت
 * @param {string} dateString - سلسلة التاريخ
 * @returns {string} - التاريخ والوقت المنسقين
 */
function formatDateTime(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
    } catch (error) {
        console.error('خطأ في تنسيق التاريخ والوقت:', error);
        return dateString;
    }
}

/**
 * الحصول على نص حالة الطلب
 * @param {string} status - حالة الطلب
 * @returns {string} - نص حالة الطلب
 */
function getOrderStatusText(status) {
    switch(status) {
        case 'pending':
            return 'قيد الانتظار';
        case 'processing':
            return 'قيد التنفيذ';
        case 'completed':
            return 'مكتمل';
        case 'cancelled':
            return 'ملغي';
        default:
            return status;
    }
}

/**
 * التحقق من صلاحيات المستخدم
 * @returns {Object|null} - بيانات المستخدم الحالي أو null إذا لم يكن مصرحًا له
 */
function checkUserAuthorization() {
    try {
        // محاولة الحصول على بيانات المستخدم من sessionStorage
        let currentUser = null;
        try {
            const userDataString = sessionStorage.getItem('currentUser');
            if (userDataString) {
                currentUser = JSON.parse(userDataString);
            }
        } catch (sessionError) {
            console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
        }

        // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
        if (!currentUser || !currentUser.role) {
            const userRole = localStorage.getItem('currentUserRole');
            const userId = localStorage.getItem('currentUserId');

            if (userRole === 'admin' && userId) {
                // إنشاء كائن مستخدم بسيط
                currentUser = {
                    id: parseInt(userId),
                    role: userRole,
                    name: 'مدير النظام'
                };

                // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                try {
                    const db = getDatabase();
                    const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                    if (fullUserData) {
                        currentUser = fullUserData;
                        // تخزين البيانات الكاملة في sessionStorage
                        sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                    }
                } catch (dbError) {
                    console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                }
            }
        }

        // التحقق من صلاحيات المستخدم
        if (!currentUser || currentUser.role !== 'admin') {
            alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
            window.location.href = '../auth/login.html';
            return null;
        }

        // تسجيل نشاط تسجيل الدخول إذا لم يكن مسجلاً بالفعل
        const lastLoginTime = sessionStorage.getItem('lastLoginTime');
        const currentTime = new Date().getTime();

        // إذا لم يكن هناك تسجيل دخول مسجل أو مر أكثر من ساعة على آخر تسجيل
        if (!lastLoginTime || (currentTime - parseInt(lastLoginTime)) > 3600000) {
            logActivity('login', currentUser.id, 'تسجيل دخول إلى لوحة التحكم');
            sessionStorage.setItem('lastLoginTime', currentTime.toString());
        }

        return currentUser;
    } catch (error) {
        console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
        alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
        window.location.href = '../auth/login.html';
        return null;
    }
}

/**
 * تسجيل نشاط في النظام
 * @param {string} type - نوع النشاط (login, order, payment, user, product, etc.)
 * @param {number} userId - معرف المستخدم
 * @param {string} description - وصف النشاط
 * @param {Object} [metadata] - بيانات إضافية للنشاط
 */
function logActivity(type, userId, description, metadata = null) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // إنشاء معرف جديد للنشاط
        const newActivityId = db.activities && db.activities.length > 0
            ? Math.max(...db.activities.map(a => a.id)) + 1
            : 1;

        // إنشاء كائن النشاط الجديد
        const newActivity = {
            id: newActivityId,
            type: type,
            userId: userId,
            description: description,
            date: new Date().toISOString(),
            metadata: metadata
        };

        // إضافة النشاط إلى قاعدة البيانات
        if (!db.activities) {
            db.activities = [];
        }

        db.activities.push(newActivity);

        // حفظ التغييرات في قاعدة البيانات
        saveDatabase(db);

        // إنشاء إشعار للنشاط إذا كان مهمًا
        if (['user_add', 'user_delete', 'order_add', 'payment', 'product_add', 'product_delete', 'school_add', 'school_delete'].includes(type)) {
            createNotification(description, userId);
        }

        return newActivityId;
    } catch (error) {
        console.error('خطأ في تسجيل النشاط:', error);
        return null;
    }
}

/**
 * إنشاء إشعار جديد
 * @param {string} content - محتوى الإشعار
 * @param {number} [userId] - معرف المستخدم المرتبط بالإشعار (اختياري)
 * @param {string} [type] - نوع الإشعار (اختياري)
 * @param {Object} [metadata] - بيانات إضافية للإشعار (اختياري)
 */
function createNotification(content, userId = null, type = 'system', metadata = null) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // إنشاء معرف جديد للإشعار
        const newNotificationId = db.notifications && db.notifications.length > 0
            ? Math.max(...db.notifications.map(n => n.id)) + 1
            : 1;

        // إنشاء كائن الإشعار الجديد
        const newNotification = {
            id: newNotificationId,
            content: content,
            userId: userId,
            type: type,
            date: new Date().toISOString(),
            read: false,
            metadata: metadata
        };

        // إضافة الإشعار إلى قاعدة البيانات
        if (!db.notifications) {
            db.notifications = [];
        }

        db.notifications.push(newNotification);

        // حفظ التغييرات في قاعدة البيانات
        saveDatabase(db);

        return newNotificationId;
    } catch (error) {
        console.error('خطأ في إنشاء إشعار:', error);
        return null;
    }
}

/**
 * تحميل الإشعارات
 * @param {Object} db - قاعدة البيانات
 */
function loadNotifications(db) {
    try {
        const notificationsList = document.getElementById('notifications-list');
        if (!notificationsList) return;

        // مسح المحتوى الحالي
        notificationsList.innerHTML = '';

        // إذا لم تكن هناك إشعارات، نعرض رسالة
        if (!db.notifications || db.notifications.length === 0) {
            notificationsList.innerHTML = '<div class="notification-item">لا توجد إشعارات جديدة</div>';
            return;
        }

        // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
        const sortedNotifications = [...db.notifications].sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);

        // تحديث عدد الإشعارات غير المقروءة
        const unreadCount = sortedNotifications.filter(notification => !notification.read).length;
        const notificationsCount = document.querySelector('.notifications-count');

        if (unreadCount > 0) {
            notificationsCount.textContent = unreadCount;
            notificationsCount.style.display = 'flex';
        } else {
            notificationsCount.style.display = 'none';
        }

        // إضافة الإشعارات إلى القائمة
        sortedNotifications.forEach(notification => {
            const notificationItem = document.createElement('div');
            notificationItem.className = 'notification-item';

            if (!notification.read) {
                notificationItem.classList.add('unread');
            }

            notificationItem.innerHTML = `
                <div class="notification-content">${notification.content}</div>
                <div class="notification-time">${formatDateTime(notification.date)}</div>
            `;

            // إضافة مستمع حدث للنقر على الإشعار
            notificationItem.addEventListener('click', function() {
                markNotificationAsRead(notification.id);

                // إذا كان هناك بيانات إضافية تحتوي على رابط، ننتقل إليه
                if (notification.metadata && notification.metadata.url) {
                    window.location.href = notification.metadata.url;
                }
            });

            notificationsList.appendChild(notificationItem);
        });
    } catch (error) {
        console.error('خطأ في تحميل الإشعارات:', error);
    }
}

/**
 * تعيين إشعار كمقروء
 * @param {number} notificationId - معرف الإشعار
 */
function markNotificationAsRead(notificationId) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // البحث عن الإشعار
        const notificationIndex = db.notifications.findIndex(n => n.id === notificationId);

        if (notificationIndex !== -1) {
            // تعيين الإشعار كمقروء
            db.notifications[notificationIndex].read = true;

            // حفظ التغييرات في قاعدة البيانات
            saveDatabase(db);

            // تحديث عرض الإشعارات
            loadNotifications(db);
        }
    } catch (error) {
        console.error('خطأ في تعيين إشعار كمقروء:', error);
    }
}

/**
 * حذف مدرسة
 * @param {number} schoolId - معرف المدرسة
 */
function deleteSchool(schoolId) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // البحث عن المدرسة
        const school = db.schools.find(s => s.id === parseInt(schoolId));

        if (!school) {
            alert('لم يتم العثور على المدرسة!');
            return;
        }

        // التحقق من وجود طلاب مرتبطين بالمدرسة
        const studentsCount = db.users.filter(u => u.role === 'student' && u.schoolId === parseInt(schoolId)).length;

        if (studentsCount > 0) {
            alert(`لا يمكن حذف المدرسة لأنها تحتوي على ${studentsCount} طالب. يرجى نقل الطلاب إلى مدرسة أخرى أولاً.`);
            return;
        }

        // حذف المدرسة
        const schoolIndex = db.schools.findIndex(s => s.id === parseInt(schoolId));

        if (schoolIndex !== -1) {
            // الحصول على المستخدم الحالي
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));

            // حفظ اسم المدرسة للاستخدام في تسجيل النشاط
            const schoolName = db.schools[schoolIndex].name;

            // حذف المدرسة من المصفوفة
            db.schools.splice(schoolIndex, 1);

            // حفظ التغييرات في قاعدة البيانات
            saveDatabase(db);

            // تسجيل النشاط
            logActivity('school_delete', currentUser.id, `تم حذف المدرسة: ${schoolName}`);

            // تحديث عرض المدارس
            loadLatestSchools(db);

            // تحديث الإحصائيات
            updateStats(db);

            alert('تم حذف المدرسة بنجاح!');
        } else {
            alert('لم يتم العثور على المدرسة!');
        }
    } catch (error) {
        console.error('خطأ في حذف المدرسة:', error);
        alert('حدث خطأ أثناء حذف المدرسة!');
    }
}

/**
 * إضافة مستمعي الأحداث لأزرار الإجراءات في جدول المدارس
 */
function addSchoolActionListeners() {
    try {
        // أزرار العرض
        document.querySelectorAll('#schools-table .view-btn').forEach(button => {
            button.addEventListener('click', function() {
                const schoolId = this.getAttribute('data-id');
                window.location.href = `school-view.html?id=${schoolId}`;
            });
        });

        // أزرار التعديل
        document.querySelectorAll('#schools-table .edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const schoolId = this.getAttribute('data-id');
                window.location.href = `school-edit.html?id=${schoolId}`;
            });
        });

        // أزرار الحذف
        document.querySelectorAll('#schools-table .delete-btn').forEach(button => {
            button.addEventListener('click', function() {
                const schoolId = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذه المدرسة؟')) {
                    deleteSchool(schoolId);
                }
            });
        });
    } catch (error) {
        console.error('خطأ في إضافة مستمعي الأحداث لأزرار الإجراءات في جدول المدارس:', error);
    }
}

/**
 * إضافة مستمعي الأحداث لأزرار الإجراءات في جدول الطلبات
 */
function addOrderActionListeners() {
    try {
        // أزرار العرض
        document.querySelectorAll('#orders-table .view-btn').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-id');
                window.location.href = `order-view.html?id=${orderId}`;
            });
        });
    } catch (error) {
        console.error('خطأ في إضافة مستمعي الأحداث لأزرار الإجراءات في جدول الطلبات:', error);
    }
}

/**
 * تهيئة الإجراءات السريعة
 */
function initializeQuickActions() {
    try {
        const quickActionsBtn = document.getElementById('quick-actions-btn');
        const quickActionsMenu = document.getElementById('quick-actions-menu');

        if (!quickActionsBtn || !quickActionsMenu) return;

        // إضافة مستمع حدث للنقر على زر الإجراءات السريعة
        quickActionsBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            quickActionsMenu.classList.toggle('show');
        });

        // إغلاق قائمة الإجراءات السريعة عند النقر في أي مكان آخر
        document.addEventListener('click', function() {
            quickActionsMenu.classList.remove('show');
        });

        // منع إغلاق القائمة عند النقر داخلها
        quickActionsMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // إضافة مستمعي الأحداث للإجراءات
        const quickActions = document.querySelectorAll('.quick-action');
        quickActions.forEach(action => {
            action.addEventListener('click', function() {
                const actionType = this.getAttribute('data-action');

                switch (actionType) {
                    case 'add-school':
                        window.location.href = 'school-add.html';
                        break;
                    case 'add-user':
                        window.location.href = 'user-add.html';
                        break;
                    case 'add-product':
                        window.location.href = 'product-add.html';
                        break;
                    case 'view-reports':
                        window.location.href = 'reports.html';
                        break;
                }
            });
        });
    } catch (error) {
        console.error('خطأ في تهيئة الإجراءات السريعة:', error);
    }
}

/**
 * إنشاء الرسوم البيانية
 * @param {Object} db - قاعدة البيانات
 */
function createCharts(db) {
    try {
        // إنشاء رسم بياني للمبيعات
        const salesChartCtx = document.getElementById('sales-chart');
        if (!salesChartCtx) return;

        // الحصول على بيانات المبيعات للأشهر الستة الماضية
        const salesData = getLastMonthsSales(db, 6);

        // إنشاء الرسم البياني
        const salesChart = new Chart(salesChartCtx, {
            type: 'bar',
            data: {
                labels: salesData.labels,
                datasets: [{
                    label: 'المبيعات (ريال)',
                    data: salesData.values,
                    backgroundColor: 'rgba(46, 125, 50, 0.7)',
                    borderColor: 'rgba(46, 125, 50, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبيعات (ريال)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الشهر'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'المبيعات الشهرية',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    } catch (error) {
        console.error('خطأ في إنشاء الرسوم البيانية:', error);
    }
}

/**
 * الحصول على بيانات المبيعات للأشهر الماضية
 * @param {Object} db - قاعدة البيانات
 * @param {number} months - عدد الأشهر
 * @returns {Object} - بيانات المبيعات
 */
function getLastMonthsSales(db, months) {
    try {
        const labels = [];
        const values = [];

        // الحصول على التاريخ الحالي
        const now = new Date();

        // إنشاء مصفوفة للأشهر الماضية
        for (let i = months - 1; i >= 0; i--) {
            const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthName = month.toLocaleDateString('ar-SA', { month: 'long' });
            labels.push(monthName);

            // حساب المبيعات لهذا الشهر
            const monthSales = db.orders.reduce((sum, order) => {
                const orderDate = new Date(order.date);
                if (orderDate.getMonth() === month.getMonth() && orderDate.getFullYear() === month.getFullYear() && order.status === 'completed') {
                    return sum + order.totalPrice;
                }
                return sum;
            }, 0);

            values.push(monthSales);
        }

        return { labels, values };
    } catch (error) {
        console.error('خطأ في الحصول على بيانات المبيعات:', error);
        return { labels: [], values: [] };
    }
}

/**
 * تهيئة مستمعي الأحداث
 */
function initializeEventListeners() {
    try {
        // زر تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                try {
                    // الحصول على المستخدم الحالي
                    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));

                    // تسجيل نشاط تسجيل الخروج
                    if (currentUser) {
                        logActivity('logout', currentUser.id, 'تسجيل خروج من لوحة التحكم');
                    }

                    // حذف بيانات المستخدم من جميع وسائل التخزين
                    sessionStorage.clear();
                    localStorage.removeItem('currentUserRole');
                    localStorage.removeItem('currentUserId');

                    // حذف الكوكيز المتعلقة بالمستخدم
                    document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                    console.log('تم تسجيل الخروج بنجاح');

                    // التوجيه إلى صفحة تسجيل الدخول
                    window.location.href = '../auth/login.html';
                } catch (error) {
                    console.error('خطأ في تسجيل الخروج:', error);
                    alert('حدث خطأ في تسجيل الخروج. سيتم إعادة تحميل الصفحة.');
                    window.location.reload();
                }
            });
        }

        // زر تحديث الإحصائيات
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                // الحصول على قاعدة البيانات
                const db = getDatabase();

                // تحديث الإحصائيات
                updateStats(db);

                // تحديث الرسوم البيانية
                createCharts(db);

                // إضافة تأثير دوران للزر
                this.classList.add('rotating');

                // إزالة التأثير بعد ثانية واحدة
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 1000);
            });
        }
    } catch (error) {
        console.error('خطأ في تهيئة مستمعي الأحداث:', error);
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeDashboard);
