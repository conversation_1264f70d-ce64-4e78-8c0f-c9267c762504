<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer Glow Effect -->
  <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
    <feGaussianBlur stdDeviation="5" result="blur" />
    <feComposite in="SourceGraphic" in2="blur" operator="over" />
  </filter>

  <!-- Gradient Definitions -->
  <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#43a047" />
    <stop offset="100%" stop-color="#1b5e20" />
  </linearGradient>

  <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#ffd54f" />
    <stop offset="100%" stop-color="#f57f17" />
  </linearGradient>

  <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#ff8a65" />
    <stop offset="100%" stop-color="#d84315" />
  </linearGradient>

  <!-- Background Circle with Shadow -->
  <circle cx="100" cy="100" r="95" fill="url(#greenGradient)" filter="url(#glow)" />
  <circle cx="100" cy="100" r="85" fill="#ffffff" />

  <!-- Inner Design - Stylized Plate and Food -->
  <circle cx="100" cy="100" r="70" fill="#e8f5e9" />

  <!-- Plate Border with Gradient -->
  <path d="M100 40C67.9086 40 42 65.9086 42 98C42 130.091 67.9086 156 100 156C132.091 156 158 130.091 158 98C158 65.9086 132.091 40 100 40ZM100 150C71.2223 150 48 126.778 48 98C48 69.2223 71.2223 46 100 46C128.778 46 152 69.2223 152 98C152 126.778 128.778 150 100 150Z" fill="url(#greenGradient)" />

  <!-- Food Items with Gradients and Shadows -->
  <path d="M85 75C85 75 90 65 100 65C110 65 115 75 115 75L120 90H80L85 75Z" fill="url(#yellowGradient)" filter="url(#glow)" />
  <path d="M75 95C75 95 80 85 95 85C110 85 115 95 115 95L120 110H70L75 95Z" fill="#4caf50" filter="url(#glow)" />
  <path d="M90 115C90 115 95 105 105 105C115 105 120 115 120 115L125 130H85L90 115Z" fill="url(#orangeGradient)" filter="url(#glow)" />

  <!-- Utensils with Enhanced Style -->
  <path d="M65 70C65 70 60 90 65 110C70 130 75 135 75 135" stroke="#1b5e20" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />
  <path d="M135 70C135 70 140 90 135 110C130 130 125 135 125 135" stroke="#1b5e20" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />

  <!-- Arabic Text for "Success Intervals" with Enhanced Style -->
  <path d="M70 170C70 170 75 165 80 170C85 175 90 170 90 170" stroke="url(#greenGradient)" stroke-width="3" filter="url(#glow)" />
  <path d="M95 170C95 170 100 165 105 170C110 175 115 170 115 170" stroke="url(#greenGradient)" stroke-width="3" filter="url(#glow)" />
  <path d="M120 170C120 170 125 165 130 170C135 175 140 170 140 170" stroke="url(#greenGradient)" stroke-width="3" filter="url(#glow)" />

  <!-- Decorative Elements with Glow -->
  <circle cx="70" cy="80" r="4" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="130" cy="80" r="4" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="80" cy="130" r="4" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="120" cy="130" r="4" fill="url(#yellowGradient)" filter="url(#glow)" />

  <!-- Steam Effect with Enhanced Style -->
  <path d="M90 60C90 60 85 50 90 45C95 40 100 45 100 45" stroke="#1b5e20" stroke-width="3" stroke-linecap="round" filter="url(#glow)" />
  <path d="M100 60C100 60 105 50 100 45C95 40 100 35 105 35" stroke="#1b5e20" stroke-width="3" stroke-linecap="round" filter="url(#glow)" />
  <path d="M110 60C110 60 115 50 110 45C105 40 110 35 115 35" stroke="#1b5e20" stroke-width="3" stroke-linecap="round" filter="url(#glow)" />
</svg>
