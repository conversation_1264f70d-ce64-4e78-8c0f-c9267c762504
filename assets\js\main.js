/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة القائمة المتجاوبة
    initializeResponsiveMenu();

    // تهيئة الشريط العلوي المتجاوب
    initializeHeaderScroll();

    // وظيفة تهيئة القائمة المتجاوبة
    function initializeResponsiveMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const mainNav = document.querySelector('.main-nav');

        if (menuToggle && mainNav) {
            // إضافة مستمع حدث للنقر على زر القائمة
            menuToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                mainNav.classList.toggle('active');

                // تبديل الأيقونة
                const icon = this.querySelector('i');
                if (icon) {
                    if (icon.classList.contains('fa-bars')) {
                        icon.classList.remove('fa-bars');
                        icon.classList.add('fa-times');
                    } else {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }

                // إضافة/إزالة فئة للجسم لمنع التمرير
                if (mainNav.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                if (mainNav && mainNav.classList.contains('active') &&
                    !event.target.closest('.main-nav') &&
                    !event.target.closest('.menu-toggle')) {

                    mainNav.classList.remove('active');
                    document.body.style.overflow = '';

                    const icon = menuToggle.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }
            });
        }
    }

    // وظيفة لتغيير مظهر الشريط العلوي عند التمرير
    function initializeHeaderScroll() {
        const header = document.querySelector('.header');

        if (header) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        }
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });

                // Close mobile menu if open
                if (mainNav.classList.contains('active')) {
                    mainNav.classList.remove('active');
                    const icon = menuToggle.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }
        });
    });

    // Contact Form Validation
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simple validation
            let isValid = true;
            const name = document.getElementById('name');
            const email = document.getElementById('email');
            const subject = document.getElementById('subject');
            const message = document.getElementById('message');

            if (!name.value.trim()) {
                isValid = false;
                showError(name, 'الرجاء إدخال الاسم');
            } else {
                removeError(name);
            }

            if (!email.value.trim()) {
                isValid = false;
                showError(email, 'الرجاء إدخال البريد الإلكتروني');
            } else if (!isValidEmail(email.value)) {
                isValid = false;
                showError(email, 'الرجاء إدخال بريد إلكتروني صحيح');
            } else {
                removeError(email);
            }

            if (!subject.value.trim()) {
                isValid = false;
                showError(subject, 'الرجاء إدخال الموضوع');
            } else {
                removeError(subject);
            }

            if (!message.value.trim()) {
                isValid = false;
                showError(message, 'الرجاء إدخال الرسالة');
            } else {
                removeError(message);
            }

            if (isValid) {
                // In a real application, you would send the form data to a server
                // For now, we'll just show a success message
                alert('تم إرسال الرسالة بنجاح!');
                contactForm.reset();
            }
        });
    }

    // Helper functions for form validation
    function showError(input, message) {
        const formGroup = input.parentElement;
        let errorElement = formGroup.querySelector('.error-message');

        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.style.color = 'red';
            errorElement.style.fontSize = '0.8rem';
            errorElement.style.marginTop = '5px';
            formGroup.appendChild(errorElement);
        }

        errorElement.textContent = message;
        input.style.borderColor = 'red';
    }

    function removeError(input) {
        const formGroup = input.parentElement;
        const errorElement = formGroup.querySelector('.error-message');

        if (errorElement) {
            formGroup.removeChild(errorElement);
        }

        input.style.borderColor = '';
    }

    function isValidEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }
});

// Simulated Database (for demo purposes)
// In a real application, this would be stored in a server-side database
const db = {
    users: [
        {
            id: 1,
            username: 'admin',
            password: 'admin123',
            role: 'admin',
            name: 'مدير النظام'
        },
        {
            id: 2,
            username: 'school1',
            password: 'school123',
            role: 'school',
            name: 'مدرسة النموذجية',
            schoolId: 1
        },
        {
            id: 3,
            username: 'student1',
            password: 'student123',
            role: 'student',
            name: 'أحمد محمد',
            noorId: '1234567890',
            schoolId: 1,
            grade: '3',
            balance: 100,
            parentId: 4,
            allergies: [
                {
                    id: 1,
                    type: 'food',
                    name: 'الفول السوداني',
                    description: 'حساسية شديدة من الفول السوداني ومشتقاته',
                    severity: 'high',
                    instructions: 'يجب تجنب أي منتجات تحتوي على الفول السوداني، وفي حالة التعرض يجب استخدام حقنة الأدرينالين فوراً والاتصال بالطوارئ'
                }
            ]
        },
        {
            id: 4,
            username: 'parent1',
            password: 'parent123',
            role: 'parent',
            name: 'محمد علي',
            email: '<EMAIL>',
            phone: '0512345678',
            children: [3]
        },
        {
            id: 5,
            username: 'cashier1',
            password: 'cashier123',
            role: 'staff',
            staffType: 'cashier',
            name: 'خالد أحمد',
            schoolId: 1,
            permissions: ['sell', 'view_products', 'view_students', 'view_reports'],
            status: 'active'
        },
        {
            id: 6,
            username: 'cook1',
            password: 'cook123',
            role: 'staff',
            staffType: 'cook',
            name: 'سعيد محمد',
            schoolId: 1,
            permissions: ['view_products', 'view_orders'],
            status: 'active'
        },
        {
            id: 7,
            username: 'manager1',
            password: 'manager123',
            role: 'staff',
            staffType: 'manager',
            name: 'فهد العلي',
            schoolId: 1,
            permissions: ['sell', 'view_products', 'view_students', 'view_reports', 'manage_products', 'manage_orders'],
            status: 'active'
        }
    ],
    products: [
        {
            id: 1,
            name: 'ساندويتش جبن',
            price: 5,
            category: 'ساندويتشات',
            image: 'sandwich.jpg',
            description: 'ساندويتش جبن طازج',
            status: 'active'
        },
        {
            id: 2,
            name: 'عصير برتقال',
            price: 3,
            category: 'مشروبات',
            image: 'orange-juice.jpg',
            description: 'عصير برتقال طبيعي 100%',
            status: 'active'
        },
        {
            id: 3,
            name: 'كيك شوكولاتة',
            price: 4,
            category: 'حلويات',
            image: 'chocolate-cake.jpg',
            description: 'كيك شوكولاتة طازج',
            status: 'active'
        },
        {
            id: 4,
            name: 'سلطة فواكه',
            price: 6,
            category: 'وجبات خفيفة',
            image: 'fruit-salad.jpg',
            description: 'سلطة فواكه طازجة',
            status: 'active'
        },
        {
            id: 5,
            name: 'ماء معدني',
            price: 1,
            category: 'مشروبات',
            image: 'water.jpg',
            description: 'ماء معدني نقي',
            status: 'active'
        },
        {
            id: 6,
            name: 'ساندويتش دجاج',
            price: 7,
            category: 'ساندويتشات',
            image: 'chicken-sandwich.jpg',
            description: 'ساندويتش دجاج طازج مع خضروات',
            status: 'active'
        },
        {
            id: 7,
            name: 'عصير تفاح',
            price: 3,
            category: 'مشروبات',
            image: 'apple-juice.jpg',
            description: 'عصير تفاح طازج',
            status: 'active'
        },
        {
            id: 8,
            name: 'بسكويت',
            price: 2,
            category: 'وجبات خفيفة',
            image: 'cookies.jpg',
            description: 'بسكويت بالشوكولاتة',
            status: 'inactive'
        }
    ],
    schools: [
        {
            id: 1,
            name: 'مدرسة النموذجية',
            address: 'الرياض، حي النزهة',
            phone: '0123456789',
            email: '<EMAIL>'
        },
        {
            id: 2,
            name: 'مدرسة الرياض الأهلية',
            address: 'الرياض، حي الملز',
            phone: '0123456788',
            email: '<EMAIL>'
        },
        {
            id: 3,
            name: 'مدرسة المستقبل',
            address: 'الرياض، حي العليا',
            phone: '0123456787',
            email: '<EMAIL>'
        },
        {
            id: 4,
            name: 'مدرسة الأندلس',
            address: 'الرياض، حي الروضة',
            phone: '0123456786',
            email: '<EMAIL>'
        },
        {
            id: 5,
            name: 'مدرسة الفيصلية',
            address: 'الرياض، حي الفيصلية',
            phone: '0123456785',
            email: '<EMAIL>'
        },
        {
            id: 6,
            name: 'مدرسة الرواد',
            address: 'الرياض، حي الورود',
            phone: '0123456784',
            email: '<EMAIL>'
        },
        {
            id: 7,
            name: 'مدرسة المعرفة',
            address: 'الرياض، حي الياسمين',
            phone: '0123456783',
            email: '<EMAIL>'
        },
        {
            id: 8,
            name: 'مدرسة الإبداع',
            address: 'الرياض، حي النخيل',
            phone: '0123456782',
            email: '<EMAIL>'
        }
    ],
    orders: [
        {
            id: 1,
            userId: 3,
            schoolId: 1,
            products: [
                { productId: 1, quantity: 2 },
                { productId: 2, quantity: 1 }
            ],
            totalPrice: 13,
            status: 'completed',
            date: '2025-05-15'
        },
        {
            id: 2,
            userId: 3,
            schoolId: 1,
            products: [
                { productId: 3, quantity: 1 },
                { productId: 1, quantity: 1 }
            ],
            totalPrice: 8,
            status: 'pending',
            date: '2025-05-16'
        },
        {
            id: 3,
            userId: 3,
            schoolId: 1,
            products: [
                { productId: 2, quantity: 2 }
            ],
            totalPrice: 6,
            status: 'processing',
            date: '2025-05-16'
        },
        {
            id: 4,
            userId: 3,
            schoolId: 1,
            products: [
                { productId: 1, quantity: 1 },
                { productId: 2, quantity: 1 },
                { productId: 3, quantity: 1 }
            ],
            totalPrice: 11,
            status: 'cancelled',
            date: '2025-05-14'
        }
    ],
    notifications: [
        {
            id: 1,
            userId: 4,
            title: 'طلب جديد',
            message: 'قام ابنك أحمد بطلب منتجات بقيمة 13 ريال',
            isRead: false,
            date: '2025-05-15'
        }
    ],
    settings: {
        themes: [
            {
                id: 1,
                name: 'الافتراضي',
                primaryColor: '#2e7d32',
                secondaryColor: '#f9a825',
                darkColor: '#1b5e20',
                lightColor: '#e8f5e9'
            },
            {
                id: 2,
                name: 'أزرق',
                primaryColor: '#1976d2',
                secondaryColor: '#ff9800',
                darkColor: '#0d47a1',
                lightColor: '#e3f2fd'
            },
            {
                id: 3,
                name: 'أرجواني',
                primaryColor: '#7b1fa2',
                secondaryColor: '#ffc107',
                darkColor: '#4a148c',
                lightColor: '#f3e5f5'
            },
            {
                id: 4,
                name: 'برتقالي',
                primaryColor: '#e65100',
                secondaryColor: '#4caf50',
                darkColor: '#bf360c',
                lightColor: '#fff3e0'
            },
            {
                id: 5,
                name: 'وردي',
                primaryColor: '#c2185b',
                secondaryColor: '#00bcd4',
                darkColor: '#880e4f',
                lightColor: '#fce4ec'
            }
        ],
        customization: {
            colors: {
                primary: '#2e7d32',
                secondary: '#f9a825',
                dark: '#1b5e20',
                light: '#e8f5e9',
                text: '#333333',
                background: '#ffffff',
                surface: '#f5f5f5'
            },
            fonts: {
                main: 'Tajawal',
                size: 'medium',
                weight: 'normal'
            },
            animations: {
                speed: 'normal',
                enabled: true
            },
            darkMode: {
                enabled: false,
                auto: false,
                startTime: '19:00',
                endTime: '06:00'
            }
        }
    }
};

// Initialize database - check if password hashing is disabled
(function() {
    try {
        // Check if password hashing is disabled
        const hashingDisabled = localStorage.getItem('disablePasswordHashing') === 'true';

        if (hashingDisabled) {
            console.log('Password hashing is disabled - using simple passwords');
            // Just ensure database exists without hashing
            const existingDB = localStorage.getItem('cafeteriaDB');
            if (!existingDB) {
                saveDatabase(db);
                console.log('Database initialized with simple passwords');
            }
        } else {
            // Use the original hashing system
            initializeDatabase().then(() => {
                console.log('Database initialization completed with hashing');
            }).catch(error => {
                console.error('Failed to initialize database:', error);
            });
        }

        // Mark system as ready
        window.systemReady = true;
        // Dispatch custom event to notify other scripts
        window.dispatchEvent(new CustomEvent('systemReady'));
    } catch (error) {
        console.error('Failed to initialize database:', error);
        window.systemReady = false;
    }
})();

// Function to get data from localStorage
function getDatabase() {
    return JSON.parse(localStorage.getItem('cafeteriaDB')) || db;
}

// Function to save data to localStorage
function saveDatabase(data) {
    localStorage.setItem('cafeteriaDB', JSON.stringify(data));
}

// Password hashing utilities for secure authentication
const PasswordUtils = {
    // Simple hash function using Web Crypto API (for demo - in production use bcrypt or similar)
    async hashPassword(password) {
        try {
            // Convert password to ArrayBuffer
            const encoder = new TextEncoder();
            const data = encoder.encode(password + 'cafeteria_salt_2024'); // Add salt

            // Hash the password using SHA-256
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);

            // Convert to hex string
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

            return hashHex;
        } catch (error) {
            console.error('Error hashing password:', error);
            // Fallback to simple hash if crypto API fails
            return this.simpleHash(password);
        }
    },

    // Fallback simple hash function
    simpleHash(password) {
        let hash = 0;
        const saltedPassword = password + 'cafeteria_salt_2024';
        for (let i = 0; i < saltedPassword.length; i++) {
            const char = saltedPassword.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    },

    // Verify password against hash
    async verifyPassword(password, hash) {
        try {
            const hashedInput = await this.hashPassword(password);
            return hashedInput === hash;
        } catch (error) {
            console.error('Error verifying password:', error);
            return false;
        }
    },

    // Check if password is already hashed (simple check)
    isHashed(password) {
        // Assume if password is longer than 20 chars and contains only hex, it's hashed
        return password && password.length > 20 && /^[a-f0-9]+$/i.test(password);
    }
};

// Function to wait for system to be ready
function waitForSystemReady() {
    return new Promise((resolve) => {
        if (window.systemReady) {
            resolve();
        } else {
            window.addEventListener('systemReady', resolve, { once: true });
            // Fallback timeout
            setTimeout(resolve, 5000);
        }
    });
}

// Enhanced user registration function with proper error handling
async function registerUser(userData, userType = 'student') {
    try {
        // Wait for system to be ready
        await waitForSystemReady();

        // Ensure PasswordUtils is available
        if (!window.PasswordUtils || typeof window.PasswordUtils.hashPassword !== 'function') {
            throw new Error('Password hashing system not available');
        }

        // Get fresh database
        const db = getDatabase();

        // Check if username already exists
        if (db.users.some(user => user.username === userData.username)) {
            throw new Error('اسم المستخدم موجود بالفعل، الرجاء اختيار اسم آخر!');
        }

        // Hash the password
        const hashedPassword = await window.PasswordUtils.hashPassword(userData.password);
        console.log(`Password hashed successfully for ${userType}:`, userData.username);

        // Create new user with hashed password
        const newUser = {
            ...userData,
            id: db.users.length + 1,
            password: hashedPassword,
            role: userType
        };

        // Add user to database
        db.users.push(newUser);

        // Save database immediately
        saveDatabase(db);

        // Verify the user was saved correctly
        const savedDb = getDatabase();
        const savedUser = savedDb.users.find(u => u.username === userData.username);

        if (!savedUser) {
            throw new Error('فشل في حفظ المستخدم في قاعدة البيانات');
        }

        console.log('User registered successfully:', savedUser.username);
        return { success: true, user: savedUser };

    } catch (error) {
        console.error('Error registering user:', error);
        return { success: false, error: error.message };
    }
}

// Database migration utility to hash existing plain text passwords
async function migratePasswordsToHashed() {
    try {
        const db = getDatabase();
        let migrationNeeded = false;

        console.log('Checking if password migration is needed...');

        // Check if any users have plain text passwords
        for (let user of db.users) {
            if (!PasswordUtils.isHashed(user.password)) {
                migrationNeeded = true;
                console.log(`Migrating password for user: ${user.username}`);
                user.password = await PasswordUtils.hashPassword(user.password);
            }
        }

        if (migrationNeeded) {
            console.log('Saving migrated passwords to database...');
            saveDatabase(db);
            console.log('Password migration completed successfully!');
            return true;
        } else {
            console.log('No password migration needed - all passwords are already hashed.');
            return false;
        }
    } catch (error) {
        console.error('Error during password migration:', error);
        return false;
    }
}

// Enhanced database initialization with password migration
async function initializeDatabase() {
    try {
        // Check if database exists in localStorage
        const existingDB = localStorage.getItem('cafeteriaDB');

        if (!existingDB) {
            console.log('Initializing new database with hashed passwords...');
            // Hash passwords for default users before storing
            for (let user of db.users) {
                if (!PasswordUtils.isHashed(user.password)) {
                    user.password = await PasswordUtils.hashPassword(user.password);
                }
            }
            saveDatabase(db);
            console.log('Database initialized with secure password hashing.');
        } else {
            console.log('Existing database found, checking for password migration...');
            await migratePasswordsToHashed();
        }
    } catch (error) {
        console.error('Error initializing database:', error);
    }
}

// Function to apply theme
function applyTheme(themeId) {
    const db = getDatabase();
    const theme = db.settings.themes.find(t => t.id === themeId);

    if (theme) {
        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
        document.documentElement.style.setProperty('--secondary-color', theme.secondaryColor);
        document.documentElement.style.setProperty('--dark-color', theme.darkColor);
        document.documentElement.style.setProperty('--light-color', theme.lightColor);

        // Save current theme to localStorage
        localStorage.setItem('currentTheme', themeId);

        return true;
    }

    return false;
}

// Function to get current theme
function getCurrentTheme() {
    return parseInt(localStorage.getItem('currentTheme')) || 1; // Default to theme 1
}

// تطبيق السمة المحفوظة عند تحميل الصفحة بطريقة آمنة
function safeApplyTheme() {
    try {
        // التحقق من وجود مدير السمات الجديد
        if (window.ThemeManager) {
            // استخدام مدير السمات الجديد إذا كان متاحاً
            console.log('Using ThemeManager for theme application');
            return;
        }

        // استخدام الطريقة القديمة إذا لم يكن مدير السمات متاحاً
        const currentThemeId = getCurrentTheme();
        applyTheme(currentThemeId);
        console.log('Theme applied successfully using legacy method:', currentThemeId);
    } catch (error) {
        console.error('Error applying theme:', error);
        // استخدام القيم الافتراضية في حالة حدوث خطأ
        document.documentElement.style.setProperty('--primary-color', '#2e7d32');
        document.documentElement.style.setProperty('--secondary-color', '#f9a825');
        document.documentElement.style.setProperty('--dark-color', '#1b5e20');
        document.documentElement.style.setProperty('--light-color', '#e8f5e9');
    }
}

// تطبيق السمة فوراً لتجنب تأخير ظهور الألوان
safeApplyTheme();

// تطبيق السمة مرة أخرى عند اكتمال تحميل الصفحة للتأكد
document.addEventListener('DOMContentLoaded', function() {
    safeApplyTheme();

    // إخفاء شاشة التحميل إذا كانت موجودة
    const preloader = document.getElementById('preloader');
    if (preloader) {
        setTimeout(function() {
            preloader.style.opacity = '0';
            setTimeout(function() {
                preloader.style.display = 'none';
            }, 500);
        }, 500);
    }

    // تحميل مدير السمات إذا كان متاحاً
    if (window.ThemeManager && typeof window.ThemeManager.init === 'function') {
        window.ThemeManager.init();
    }
});

// تصدير الدوال للاستخدام العام
window.getDatabase = getDatabase;
window.saveDatabase = saveDatabase;
window.applyTheme = applyTheme;
window.getCurrentTheme = getCurrentTheme;
window.PasswordUtils = PasswordUtils;
window.migratePasswordsToHashed = migratePasswordsToHashed;
window.initializeDatabase = initializeDatabase;
window.waitForSystemReady = waitForSystemReady;
window.registerUser = registerUser;
