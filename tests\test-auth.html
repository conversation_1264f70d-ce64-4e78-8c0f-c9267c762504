<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المصادقة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1b5e20;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام المصادقة المحسن</h1>
        <p>هذه الصفحة لاختبار إصلاحات نظام المصادقة وتشفير كلمات المرور</p>

        <div class="test-section">
            <h3>1. اختبار تهيئة قاعدة البيانات</h3>
            <button onclick="testDatabaseInit()">اختبار تهيئة قاعدة البيانات</button>
            <div id="db-init-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار تشفير كلمات المرور</h3>
            <input type="text" id="test-password" placeholder="كلمة مرور للاختبار" value="test123">
            <button onclick="testPasswordHashing()">اختبار التشفير</button>
            <div id="hash-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار ترحيل كلمات المرور</h3>
            <button onclick="testPasswordMigration()">اختبار ترحيل كلمات المرور</button>
            <div id="migration-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار تسجيل الدخول</h3>
            <input type="text" id="login-username" placeholder="اسم المستخدم" value="admin">
            <input type="password" id="login-password" placeholder="كلمة المرور" value="admin123">
            <select id="login-role">
                <option value="admin">مدير</option>
                <option value="student">طالب</option>
                <option value="parent">ولي أمر</option>
                <option value="school">مدرسة</option>
                <option value="staff">موظف</option>
            </select>
            <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="login-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. عرض المستخدمين وحالة كلمات المرور</h3>
            <button onclick="showUsers()">عرض المستخدمين</button>
            <div id="users-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. اختبار تدفق التسجيل والدخول الكامل</h3>
            <input type="text" id="test-reg-username" placeholder="اسم مستخدم جديد" value="">
            <input type="password" id="test-reg-password" placeholder="كلمة مرور جديدة" value="test123">
            <input type="text" id="test-reg-name" placeholder="الاسم الكامل" value="مستخدم تجريبي">
            <input type="email" id="test-reg-email" placeholder="البريد الإلكتروني" value="<EMAIL>">
            <br>
            <button onclick="testFullRegistrationFlow()">اختبار التسجيل والدخول الكامل</button>
            <div id="full-flow-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>7. إعادة تعيين قاعدة البيانات</h3>
            <button onclick="resetDatabase()" style="background: #d32f2f;">إعادة تعيين قاعدة البيانات</button>
            <div id="reset-result" class="result"></div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="assets/js/main.js"></script>

    <script>
        // انتظار تحميل النظام
        setTimeout(() => {
            console.log('نظام الاختبار جاهز');
            document.getElementById('db-init-result').innerHTML = '<div class="info">النظام جاهز للاختبار</div>';
        }, 1000);

        async function testDatabaseInit() {
            const resultDiv = document.getElementById('db-init-result');
            try {
                resultDiv.innerHTML = '<div class="info">جاري اختبار تهيئة قاعدة البيانات...</div>';

                if (typeof window.initializeDatabase === 'function') {
                    await window.initializeDatabase();
                    resultDiv.innerHTML = '<div class="success">✓ تم تهيئة قاعدة البيانات بنجاح</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ دالة تهيئة قاعدة البيانات غير متوفرة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تهيئة قاعدة البيانات: ${error.message}</div>`;
            }
        }

        async function testPasswordHashing() {
            const resultDiv = document.getElementById('hash-result');
            const password = document.getElementById('test-password').value;

            if (!password) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء إدخال كلمة مرور للاختبار</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">جاري اختبار تشفير كلمة المرور...</div>';

                if (window.PasswordUtils && typeof window.PasswordUtils.hashPassword === 'function') {
                    const hash = await window.PasswordUtils.hashPassword(password);
                    const isValid = await window.PasswordUtils.verifyPassword(password, hash);

                    resultDiv.innerHTML = `
                        <div class="success">✓ تم تشفير كلمة المرور بنجاح</div>
                        <div class="info">كلمة المرور الأصلية: ${password}</div>
                        <div class="info">كلمة المرور المشفرة: ${hash}</div>
                        <div class="success">✓ التحقق من كلمة المرور: ${isValid ? 'نجح' : 'فشل'}</div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ نظام تشفير كلمات المرور غير متوفر</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تشفير كلمة المرور: ${error.message}</div>`;
            }
        }

        async function testPasswordMigration() {
            const resultDiv = document.getElementById('migration-result');
            try {
                resultDiv.innerHTML = '<div class="info">جاري اختبار ترحيل كلمات المرور...</div>';

                if (typeof window.migratePasswordsToHashed === 'function') {
                    const migrated = await window.migratePasswordsToHashed();
                    resultDiv.innerHTML = `<div class="success">✓ تم ترحيل كلمات المرور: ${migrated ? 'تم الترحيل' : 'لا حاجة للترحيل'}</div>`;
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ دالة ترحيل كلمات المرور غير متوفرة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في ترحيل كلمات المرور: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            const role = document.getElementById('login-role').value;

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">جاري اختبار تسجيل الدخول...</div>';

                const db = window.getDatabase();
                if (!db || !db.users) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                const user = db.users.find(u => u.username === username && u.role === role);
                if (!user) {
                    resultDiv.innerHTML = '<div class="error">✗ المستخدم غير موجود</div>';
                    return;
                }

                let passwordMatch = false;
                if (window.PasswordUtils && typeof window.PasswordUtils.verifyPassword === 'function') {
                    passwordMatch = await window.PasswordUtils.verifyPassword(password, user.password);
                } else {
                    passwordMatch = (user.password === password);
                }

                if (passwordMatch) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ تم تسجيل الدخول بنجاح</div>
                        <div class="info">المستخدم: ${user.name} (${user.username})</div>
                        <div class="info">الدور: ${user.role}</div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ كلمة المرور غير صحيحة</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تسجيل الدخول: ${error.message}</div>`;
            }
        }

        function showUsers() {
            const resultDiv = document.getElementById('users-result');
            try {
                const db = window.getDatabase();
                if (!db || !db.users) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let html = '<div class="info">المستخدمون في النظام:</div>';
                db.users.forEach(user => {
                    const isHashed = window.PasswordUtils ? window.PasswordUtils.isHashed(user.password) : false;
                    html += `
                        <div class="info">
                            ${user.name} (${user.username}) - ${user.role} -
                            كلمة المرور: ${isHashed ? 'مشفرة ✓' : 'غير مشفرة ✗'}
                        </div>
                    `;
                });
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في عرض المستخدمين: ${error.message}</div>`;
            }
        }

        async function testFullRegistrationFlow() {
            const resultDiv = document.getElementById('full-flow-result');
            const username = document.getElementById('test-reg-username').value || `testuser_${Date.now()}`;
            const password = document.getElementById('test-reg-password').value;
            const name = document.getElementById('test-reg-name').value;
            const email = document.getElementById('test-reg-email').value;

            if (!password || !name || !email) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء ملء جميع الحقول</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">جاري اختبار التدفق الكامل...</div>';

                // Step 1: Wait for system to be ready
                resultDiv.innerHTML += '<div class="info">1. انتظار جاهزية النظام...</div>';
                await window.waitForSystemReady();
                resultDiv.innerHTML += '<div class="success">✓ النظام جاهز</div>';

                // Step 2: Register new user
                resultDiv.innerHTML += '<div class="info">2. تسجيل مستخدم جديد...</div>';
                const userData = {
                    username: username,
                    password: password,
                    name: name,
                    email: email,
                    phone: '0500000000',
                    schoolId: 1,
                    grade: '1',
                    noorId: `${Date.now()}`,
                    balance: 0
                };

                const registrationResult = await window.registerUser(userData, 'student');

                if (!registrationResult.success) {
                    resultDiv.innerHTML += `<div class="error">✗ فشل التسجيل: ${registrationResult.error}</div>`;
                    return;
                }

                resultDiv.innerHTML += '<div class="success">✓ تم تسجيل المستخدم بنجاح</div>';

                // Step 3: Verify user was saved with hashed password
                resultDiv.innerHTML += '<div class="info">3. التحقق من حفظ المستخدم...</div>';
                const db = window.getDatabase();
                const savedUser = db.users.find(u => u.username === username);

                if (!savedUser) {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم العثور على المستخدم في قاعدة البيانات</div>';
                    return;
                }

                const isHashed = window.PasswordUtils.isHashed(savedUser.password);
                resultDiv.innerHTML += `<div class="success">✓ المستخدم محفوظ - كلمة المرور ${isHashed ? 'مشفرة' : 'غير مشفرة'}</div>`;

                // Step 4: Test login with the same credentials
                resultDiv.innerHTML += '<div class="info">4. اختبار تسجيل الدخول...</div>';

                const user = db.users.find(u => u.username === username && u.role === 'student');
                if (!user) {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم العثور على المستخدم للدخول</div>';
                    return;
                }

                const passwordMatch = await window.PasswordUtils.verifyPassword(password, user.password);

                if (passwordMatch) {
                    resultDiv.innerHTML += '<div class="success">✓ تم تسجيل الدخول بنجاح!</div>';
                    resultDiv.innerHTML += '<div class="success"><strong>🎉 اختبار التدفق الكامل نجح!</strong></div>';
                    resultDiv.innerHTML += `<div class="info">المستخدم: ${user.name} (${user.username})</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="error">✗ فشل تسجيل الدخول - كلمة المرور غير صحيحة</div>';
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في اختبار التدفق: ${error.message}</div>`;
                console.error('Full flow test error:', error);
            }
        }

        function resetDatabase() {
            const resultDiv = document.getElementById('reset-result');
            try {
                localStorage.removeItem('cafeteriaDB');
                resultDiv.innerHTML = '<div class="success">✓ تم إعادة تعيين قاعدة البيانات. يرجى تحديث الصفحة.</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين قاعدة البيانات: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
