<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التسجيل</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2e7d32;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .test-button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 0.9rem;
        }
        .test-button:hover {
            background: #1b5e20;
        }
        .test-button.danger {
            background: #d32f2f;
        }
        .test-button.danger:hover {
            background: #b71c1c;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .user-list {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
        .form-test {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-test input, .form-test select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .form-test label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام التسجيل</h1>
            <p>اختبار إنشاء الحسابات الجديدة وتسجيل الدخول</p>
        </div>

        <div class="test-section">
            <div class="test-title">📊 فحص قاعدة البيانات</div>
            <button class="test-button" onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <button class="test-button" onclick="listAllUsers()">عرض جميع المستخدمين</button>
            <button class="test-button" onclick="checkDatabaseStructure()">فحص هيكل قاعدة البيانات</button>
            <div id="database-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">👤 اختبار إنشاء حساب جديد</div>
            <div class="form-test">
                <label>نوع المستخدم:</label>
                <select id="test-user-type">
                    <option value="student">طالب</option>
                    <option value="staff">عامل</option>
                    <option value="parent">ولي أمر</option>
                    <option value="admin">مدير</option>
                    <option value="school">مدرسة</option>
                </select>
                
                <label>اسم المستخدم:</label>
                <input type="text" id="test-username" placeholder="أدخل اسم المستخدم">
                
                <label>كلمة المرور:</label>
                <input type="password" id="test-password" placeholder="أدخل كلمة المرور">
                
                <label>الاسم الكامل:</label>
                <input type="text" id="test-fullname" placeholder="أدخل الاسم الكامل">
                
                <label>البريد الإلكتروني:</label>
                <input type="email" id="test-email" placeholder="أدخل البريد الإلكتروني">
            </div>
            <button class="test-button" onclick="testCreateAccount()">إنشاء حساب تجريبي</button>
            <button class="test-button" onclick="generateRandomUser()">إنشاء مستخدم عشوائي</button>
            <div id="registration-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔐 اختبار تسجيل الدخول</div>
            <button class="test-button" onclick="testLoginWithNewAccount()">تسجيل دخول بآخر حساب تم إنشاؤه</button>
            <button class="test-button" onclick="testLoginFlow()">اختبار تدفق تسجيل الدخول الكامل</button>
            <div id="login-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 أدوات الصيانة</div>
            <button class="test-button" onclick="cleanupTestUsers()">حذف المستخدمين التجريبيين</button>
            <button class="test-button danger" onclick="resetDatabase()">إعادة تعيين قاعدة البيانات</button>
            <button class="test-button" onclick="exportDatabase()">تصدير قاعدة البيانات</button>
            <div id="maintenance-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 روابط سريعة</div>
            <button class="test-button" onclick="window.open('pages/auth/register.html', '_blank')">فتح صفحة التسجيل</button>
            <button class="test-button" onclick="window.open('pages/auth/login.html', '_blank')">فتح صفحة تسجيل الدخول</button>
            <button class="test-button" onclick="window.open('test-login-switching.html', '_blank')">اختبار التبديل</button>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        let lastCreatedUser = null;

        // فحص قاعدة البيانات
        function checkDatabase() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                if (!db) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ قاعدة البيانات غير موجودة';
                    return;
                }
                
                const usersByRole = {};
                if (db.users && Array.isArray(db.users)) {
                    db.users.forEach(user => {
                        if (!usersByRole[user.role]) {
                            usersByRole[user.role] = 0;
                        }
                        usersByRole[user.role]++;
                    });
                }
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ قاعدة البيانات سليمة<br>
                    📊 إجمالي المستخدمين: ${db.users ? db.users.length : 0}<br>
                    📈 التوزيع: ${JSON.stringify(usersByRole, null, 2)}
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في فحص قاعدة البيانات: ${error.message}`;
            }
        }

        // عرض جميع المستخدمين
        function listAllUsers() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                if (!db || !db.users || db.users.length === 0) {
                    resultDiv.className = 'result warning';
                    resultDiv.innerHTML = '⚠️ لا توجد بيانات مستخدمين';
                    return;
                }
                
                const usersList = db.users.map((user, index) => 
                    `${index + 1}. ${user.role}: ${user.username} - ${user.name || 'بدون اسم'} (ID: ${user.id})`
                ).join('<br>');
                
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `
                    <div class="user-list">
                        <strong>📋 قائمة المستخدمين (${db.users.length}):</strong><br>
                        ${usersList}
                    </div>
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في عرض المستخدمين: ${error.message}`;
            }
        }

        // فحص هيكل قاعدة البيانات
        function checkDatabaseStructure() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                const requiredTables = ['users', 'schools', 'products', 'orders'];
                const missingTables = [];
                
                requiredTables.forEach(table => {
                    if (!db[table]) {
                        missingTables.push(table);
                    }
                });
                
                if (missingTables.length > 0) {
                    resultDiv.className = 'result warning';
                    resultDiv.innerHTML = `⚠️ جداول مفقودة: ${missingTables.join(', ')}`;
                } else {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✅ هيكل قاعدة البيانات سليم';
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في فحص الهيكل: ${error.message}`;
            }
        }

        // اختبار إنشاء حساب
        function testCreateAccount() {
            const resultDiv = document.getElementById('registration-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 جاري إنشاء الحساب...';
            
            try {
                const userData = {
                    userType: document.getElementById('test-user-type').value,
                    username: document.getElementById('test-username').value,
                    password: document.getElementById('test-password').value,
                    fullName: document.getElementById('test-fullname').value,
                    email: document.getElementById('test-email').value
                };
                
                if (!userData.username || !userData.password || !userData.fullName) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ يرجى ملء جميع الحقول المطلوبة';
                    return;
                }
                
                // محاكاة نظام التسجيل
                const db = getDatabase();
                
                // التحقق من وجود اسم المستخدم
                const existingUser = db.users.find(u => u.username === userData.username);
                if (existingUser) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ اسم المستخدم موجود بالفعل';
                    return;
                }
                
                // إنشاء المستخدم الجديد
                const newId = Math.max(...db.users.map(u => u.id || 0), 0) + 1;
                const newUser = {
                    id: newId,
                    username: userData.username,
                    password: userData.password,
                    role: userData.userType,
                    name: userData.fullName,
                    email: userData.email,
                    status: 'active',
                    createdAt: new Date().toISOString()
                };
                
                db.users.push(newUser);
                saveDatabase(db);
                lastCreatedUser = newUser;
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ تم إنشاء الحساب بنجاح!<br>المعرف: ${newUser.id}<br>النوع: ${newUser.role}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في إنشاء الحساب: ${error.message}`;
            }
        }

        // إنشاء مستخدم عشوائي
        function generateRandomUser() {
            const userTypes = ['student', 'staff', 'parent', 'admin', 'school'];
            const randomType = userTypes[Math.floor(Math.random() * userTypes.length)];
            const randomId = Math.floor(Math.random() * 10000);
            
            document.getElementById('test-user-type').value = randomType;
            document.getElementById('test-username').value = `test_${randomType}_${randomId}`;
            document.getElementById('test-password').value = 'test123';
            document.getElementById('test-fullname').value = `مستخدم تجريبي ${randomId}`;
            document.getElementById('test-email').value = `test${randomId}@example.com`;
            
            testCreateAccount();
        }

        // اختبار تسجيل الدخول بآخر حساب
        function testLoginWithNewAccount() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            
            if (!lastCreatedUser) {
                resultDiv.className = 'result warning';
                resultDiv.innerHTML = '⚠️ لا يوجد حساب تم إنشاؤه مؤخراً. قم بإنشاء حساب أولاً.';
                return;
            }
            
            try {
                const db = getDatabase();
                const user = db.users.find(u => 
                    u.username === lastCreatedUser.username && 
                    u.password === lastCreatedUser.password &&
                    u.role === lastCreatedUser.role
                );
                
                if (user) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم العثور على المستخدم في قاعدة البيانات<br>الاسم: ${user.name}<br>النوع: ${user.role}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ لم يتم العثور على المستخدم في قاعدة البيانات';
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في اختبار تسجيل الدخول: ${error.message}`;
            }
        }

        // اختبار تدفق تسجيل الدخول الكامل
        function testLoginFlow() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 جاري اختبار تدفق تسجيل الدخول...';
            
            // محاكاة عملية تسجيل الدخول الكاملة
            setTimeout(() => {
                try {
                    const db = getDatabase();
                    const testResults = [];
                    
                    // اختبار كل نوع مستخدم
                    const userTypes = ['admin', 'staff', 'student', 'parent', 'school'];
                    
                    userTypes.forEach(type => {
                        const usersOfType = db.users.filter(u => u.role === type);
                        if (usersOfType.length > 0) {
                            testResults.push(`✅ ${type}: ${usersOfType.length} مستخدم`);
                        } else {
                            testResults.push(`⚠️ ${type}: لا يوجد مستخدمين`);
                        }
                    });
                    
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = `
                        <strong>نتائج اختبار تدفق تسجيل الدخول:</strong><br>
                        ${testResults.join('<br>')}
                    `;
                    
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ خطأ في اختبار التدفق: ${error.message}`;
                }
            }, 1000);
        }

        // حذف المستخدمين التجريبيين
        function cleanupTestUsers() {
            const resultDiv = document.getElementById('maintenance-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                const originalCount = db.users.length;
                
                // حذف المستخدمين التجريبيين
                db.users = db.users.filter(user => 
                    !user.username.startsWith('test_') && 
                    !user.email.includes('@example.com')
                );
                
                const deletedCount = originalCount - db.users.length;
                saveDatabase(db);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ تم حذف ${deletedCount} مستخدم تجريبي`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في حذف المستخدمين: ${error.message}`;
            }
        }

        // إعادة تعيين قاعدة البيانات
        function resetDatabase() {
            if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
                const resultDiv = document.getElementById('maintenance-result');
                resultDiv.style.display = 'block';
                
                try {
                    localStorage.removeItem('cafeteriaDB');
                    localStorage.removeItem('smartCanteen_users');
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✅ تم إعادة تعيين قاعدة البيانات. يرجى تحديث الصفحة.';
                    
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                    
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ خطأ في إعادة التعيين: ${error.message}`;
                }
            }
        }

        // تصدير قاعدة البيانات
        function exportDatabase() {
            const resultDiv = document.getElementById('maintenance-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                const dataStr = JSON.stringify(db, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `cafeteria_db_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ تم تصدير قاعدة البيانات';
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في التصدير: ${error.message}`;
            }
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkDatabase, 1000);
        });
    </script>
</body>
</html>
