<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العاملين - فواصل النجاح</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header h1 {
            color: #2e7d32;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.2rem;
        }

        .test-link {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #2e7d32;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-links a {
            background: rgba(255,255,255,0.9);
            color: #2e7d32;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            background: white;
        }

        .actions-bar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .search-container {
            display: flex;
            gap: 10px;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #2e7d32;
            box-shadow: 0 0 10px rgba(46, 125, 50, 0.2);
        }

        .filter-select {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            background: white;
            cursor: pointer;
        }

        .add-staff-btn {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        .add-staff-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .staff-table-container {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .staff-table {
            width: 100%;
            border-collapse: collapse;
        }

        .staff-table th {
            background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
            color: white;
            padding: 20px 15px;
            text-align: right;
            font-weight: 600;
            font-size: 16px;
        }

        .staff-table td {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            vertical-align: middle;
        }

        .staff-table tr:hover {
            background-color: rgba(46, 125, 50, 0.05);
        }

        .staff-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(45deg, #2e7d32, #4caf50);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 0 auto;
            font-size: 18px;
        }

        .staff-status {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-active {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .status-inactive {
            background: #ffebee;
            color: #c62828;
        }

        .staff-type {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 500;
        }

        .type-cashier {
            background: #e3f2fd;
            color: #1976d2;
        }

        .type-cook {
            background: #fff3e0;
            color: #f57c00;
        }

        .type-manager {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            color: white;
        }

        .edit-btn {
            background: #2196f3;
        }

        .edit-btn:hover {
            background: #1976d2;
            transform: scale(1.1);
        }

        .delete-btn {
            background: #f44336;
        }

        .delete-btn:hover {
            background: #d32f2f;
            transform: scale(1.1);
        }

        .toggle-btn {
            background: #ff9800;
        }

        .toggle-btn:hover {
            background: #f57c00;
            transform: scale(1.1);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
        }

        .pagination button {
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .pagination button:hover {
            background: #2e7d32;
            color: white;
            border-color: #2e7d32;
        }

        .pagination button.active {
            background: #2e7d32;
            color: white;
            border-color: #2e7d32;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #e0e0e0;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .empty-state p {
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .test-link {
                position: static;
                display: inline-block;
                margin-top: 15px;
            }

            .actions-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: none;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .staff-table-container {
                overflow-x: auto;
            }

            .staff-table {
                min-width: 600px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .nav-links {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <a href="../../tests/index.html" class="test-link">
        <i class="fas fa-flask"></i> مركز الاختبارات
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-user-tie"></i> إدارة العاملين</h1>
            <p>إدارة شاملة لحسابات العاملين في النظام</p>
        </div>

        <div class="nav-links">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
            <a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a>
            <a href="../staff/login.html"><i class="fas fa-sign-in-alt"></i> دخول العاملين</a>
            <a href="../../tests/staff-management.html"><i class="fas fa-vial"></i> اختبار العاملين</a>
        </div>

        <div class="actions-bar">
            <div class="search-container">
                <input type="text" class="search-input" id="searchInput" placeholder="البحث عن عامل...">
                <select class="filter-select" id="filterType">
                    <option value="">جميع الأنواع</option>
                    <option value="cashier">أمين الصندوق</option>
                    <option value="cook">طباخ</option>
                    <option value="manager">مدير</option>
                </select>
                <select class="filter-select" id="filterStatus">
                    <option value="">جميع الحالات</option>
                    <option value="active">مفعل</option>
                    <option value="inactive">غير مفعل</option>
                </select>
            </div>
            <button class="add-staff-btn" onclick="openAddModal()">
                <i class="fas fa-plus"></i>
                إضافة عامل جديد
            </button>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalStaff">0</div>
                <div class="stat-label">إجمالي العاملين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeStaff">0</div>
                <div class="stat-label">العاملين المفعلين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="inactiveStaff">0</div>
                <div class="stat-label">العاملين غير المفعلين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="managersCount">0</div>
                <div class="stat-label">المدراء</div>
            </div>
        </div>

        <div class="staff-table-container">
            <table class="staff-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>اسم المستخدم</th>
                        <th>النوع</th>
                        <th>المدرسة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="staffTableBody">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </tbody>
            </table>
            
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-user-friends"></i>
                <h3>لا يوجد عاملين</h3>
                <p>لم يتم العثور على أي عاملين في النظام</p>
                <button class="add-staff-btn" onclick="openAddModal()">
                    <i class="fas fa-plus"></i>
                    إضافة أول عامل
                </button>
            </div>
        </div>

        <div class="pagination" id="pagination">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // متغيرات عامة
        let currentPage = 1;
        const itemsPerPage = 10;
        let filteredStaff = [];
        let allStaff = [];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadStaffData();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterStaff);
            document.getElementById('filterType').addEventListener('change', filterStaff);
            document.getElementById('filterStatus').addEventListener('change', filterStaff);
        }

        // تحميل بيانات العاملين
        function loadStaffData() {
            try {
                const db = getDatabase();
                allStaff = db.users.filter(user => user.role === 'staff') || [];
                filteredStaff = [...allStaff];
                
                updateStats();
                renderStaffTable();
                renderPagination();
            } catch (error) {
                console.error('خطأ في تحميل بيانات العاملين:', error);
                showEmptyState();
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            const total = allStaff.length;
            const active = allStaff.filter(staff => staff.status === 'active').length;
            const inactive = total - active;
            const managers = allStaff.filter(staff => staff.staffType === 'manager').length;

            document.getElementById('totalStaff').textContent = total;
            document.getElementById('activeStaff').textContent = active;
            document.getElementById('inactiveStaff').textContent = inactive;
            document.getElementById('managersCount').textContent = managers;
        }

        // فلترة العاملين
        function filterStaff() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('filterType').value;
            const statusFilter = document.getElementById('filterStatus').value;

            filteredStaff = allStaff.filter(staff => {
                const matchesSearch = staff.name.toLowerCase().includes(searchTerm) || 
                                    staff.username.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || staff.staffType === typeFilter;
                const matchesStatus = !statusFilter || staff.status === statusFilter;

                return matchesSearch && matchesType && matchesStatus;
            });

            currentPage = 1;
            renderStaffTable();
            renderPagination();
        }

        // عرض جدول العاملين
        function renderStaffTable() {
            const tbody = document.getElementById('staffTableBody');
            const emptyState = document.getElementById('emptyState');

            if (filteredStaff.length === 0) {
                tbody.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageStaff = filteredStaff.slice(startIndex, endIndex);

            tbody.innerHTML = pageStaff.map(staff => `
                <tr>
                    <td>
                        <div class="staff-avatar">
                            ${staff.name.charAt(0)}
                        </div>
                    </td>
                    <td><strong>${staff.name}</strong></td>
                    <td>${staff.username}</td>
                    <td>
                        <span class="staff-type type-${staff.staffType}">
                            ${getStaffTypeText(staff.staffType)}
                        </span>
                    </td>
                    <td>${getSchoolName(staff.schoolId)}</td>
                    <td>
                        <span class="staff-status status-${staff.status}">
                            ${staff.status === 'active' ? 'مفعل' : 'غير مفعل'}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit-btn" onclick="editStaff(${staff.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn toggle-btn" onclick="toggleStaffStatus(${staff.id})" title="تغيير الحالة">
                                <i class="fas fa-toggle-${staff.status === 'active' ? 'on' : 'off'}"></i>
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteStaff(${staff.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // عرض التصفح
        function renderPagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredStaff.length / itemsPerPage);

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // زر السابق
            paginationHTML += `
                <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            // أرقام الصفحات
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                    paginationHTML += `
                        <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 2 || i === currentPage + 2) {
                    paginationHTML += '<span>...</span>';
                }
            }

            // زر التالي
            paginationHTML += `
                <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // تغيير الصفحة
        function changePage(page) {
            const totalPages = Math.ceil(filteredStaff.length / itemsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderStaffTable();
                renderPagination();
            }
        }

        // دوال مساعدة
        function getStaffTypeText(type) {
            const types = {
                'cashier': 'أمين الصندوق',
                'cook': 'طباخ',
                'manager': 'مدير'
            };
            return types[type] || type;
        }

        function getSchoolName(schoolId) {
            try {
                const db = getDatabase();
                const school = db.schools.find(s => s.id === schoolId);
                return school ? school.name : 'غير محدد';
            } catch (error) {
                return 'غير محدد';
            }
        }

        function showEmptyState() {
            document.getElementById('staffTableBody').innerHTML = '';
            document.getElementById('emptyState').style.display = 'block';
        }

        // دوال الإجراءات (ستتم إضافتها لاحقاً)
        function openAddModal() {
            alert('سيتم إضافة نافذة إضافة عامل جديد قريباً');
        }

        function editStaff(id) {
            alert(`سيتم إضافة نافذة تعديل العامل ${id} قريباً`);
        }

        function toggleStaffStatus(id) {
            if (confirm('هل أنت متأكد من تغيير حالة هذا العامل؟')) {
                try {
                    const db = getDatabase();
                    const staffIndex = db.users.findIndex(u => u.id === id && u.role === 'staff');
                    
                    if (staffIndex !== -1) {
                        db.users[staffIndex].status = db.users[staffIndex].status === 'active' ? 'inactive' : 'active';
                        saveDatabase(db);
                        loadStaffData();
                        alert('تم تغيير حالة العامل بنجاح');
                    }
                } catch (error) {
                    alert('حدث خطأ في تغيير حالة العامل');
                }
            }
        }

        function deleteStaff(id) {
            if (confirm('هل أنت متأكد من حذف هذا العامل؟ لا يمكن التراجع عن هذا الإجراء.')) {
                try {
                    const db = getDatabase();
                    db.users = db.users.filter(u => !(u.id === id && u.role === 'staff'));
                    saveDatabase(db);
                    loadStaffData();
                    alert('تم حذف العامل بنجاح');
                } catch (error) {
                    alert('حدث خطأ في حذف العامل');
                }
            }
        }
    </script>
</body>
</html>
