# 📋 دليل نظام إدارة العاملين

## 🎯 نظرة عامة

نظام إدارة العاملين يسمح للمدير بإنشاء وإدارة حسابات العاملين في النظام بشكل كامل، مع التحكم في الصلاحيات وحالة التفعيل.

---

## 🔧 المتطلبات

- **صلاحيات المدير**: يجب تسجيل الدخول كمدير للوصول لنظام إدارة العاملين
- **متصفح حديث**: يدعم JavaScript و localStorage
- **اتصال بالإنترنت**: لتحميل الخطوط والأيقونات

---

## 📚 كيفية الاستخدام

### 1. 🏠 الوصول لنظام إدارة العاملين

#### الطريقة الأولى: من لوحة التحكم
1. سجل الدخول كمدير
2. اذهب إلى لوحة التحكم
3. اضغط على "إدارة العاملين" من الشريط الجانبي

#### الطريقة الثانية: الإجراءات السريعة
1. اضغط على الزر العائم (+) في لوحة التحكم
2. اختر "إضافة عامل"

### 2. ➕ إنشاء حساب عامل جديد

1. **اضغط على "إضافة عامل جديد"**
2. **املأ البيانات المطلوبة:**
   - **اسم المستخدم**: اسم فريد للعامل
   - **كلمة المرور**: كلمة مرور قوية
   - **الاسم الكامل**: اسم العامل الحقيقي
   - **نوع العامل**: اختر من القائمة
     - أمين الصندوق
     - طباخ
     - مدير
   - **المدرسة**: اختر المدرسة التابع لها
   - **الحالة**: مفعل أو غير مفعل

3. **حدد الصلاحيات:**
   - ✅ البيع
   - ✅ عرض المنتجات
   - ✅ إدارة المنتجات
   - ✅ عرض الطلبات
   - ✅ إدارة الطلبات
   - ✅ عرض الطلاب
   - ✅ عرض التقارير

4. **اضغط "حفظ"**

### 3. ✏️ تعديل بيانات عامل

1. ابحث عن العامل في القائمة
2. اضغط على أيقونة التعديل (✏️)
3. عدل البيانات المطلوبة
4. اضغط "حفظ"

**ملاحظة**: لا يمكن تعديل كلمة المرور من هنا لأسباب أمنية

### 4. 🔄 تفعيل/إلغاء تفعيل حساب

#### من صفحة إدارة العاملين:
1. اضغط على أيقونة التعديل للعامل
2. غير حالة "الحالة" إلى مفعل/غير مفعل
3. اضغط "حفظ"

#### من صفحة الاختبار:
1. افتح `test-staff-management.html`
2. استخدم قسم "تفعيل/إلغاء تفعيل حساب"

### 5. 🗑️ حذف عامل

1. ابحث عن العامل في القائمة
2. اضغط على أيقونة الحذف (🗑️)
3. أكد الحذف في النافذة المنبثقة

**تحذير**: لا يمكن التراجع عن عملية الحذف!

---

## 🔐 تسجيل دخول العاملين

### للعاملين:
1. اذهب إلى `pages/staff/login.html`
2. أدخل اسم المستخدم وكلمة المرور المحددة من قبل المدير
3. اضغط "تسجيل الدخول"

### شروط تسجيل الدخول:
- ✅ اسم المستخدم صحيح
- ✅ كلمة المرور صحيحة
- ✅ الحساب مفعل (status = 'active')
- ✅ الدور = 'staff'

---

## 🧪 اختبار النظام

### استخدام صفحة الاختبار:
1. افتح `test-staff-management.html`
2. استخدم الأقسام المختلفة:
   - **إنشاء حساب عامل جديد**
   - **اختبار تسجيل دخول العامل**
   - **عرض جميع العاملين**
   - **تفعيل/إلغاء تفعيل حساب**
   - **اختبار التدفق الكامل**

### اختبار التدفق الكامل:
يقوم بـ:
1. إنشاء حساب عامل تجريبي
2. اختبار تسجيل الدخول
3. اختبار إلغاء التفعيل
4. اختبار منع الدخول للحساب المُلغى

---

## 🔧 استكشاف الأخطاء

### مشكلة: لا يمكن تسجيل الدخول
**الحلول:**
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. تأكد من أن الحساب مفعل
3. تحقق من وجود العامل في قاعدة البيانات
4. افتح أدوات المطور وتحقق من رسائل الخطأ

### مشكلة: لا تظهر البيانات
**الحلول:**
1. تحديث الصفحة
2. مسح cache المتصفح
3. التأكد من تحميل ملف `main.js`
4. فحص localStorage في أدوات المطور

### مشكلة: خطأ في الصلاحيات
**الحلول:**
1. تأكد من تسجيل الدخول كمدير
2. تحقق من وجود بيانات المستخدم في sessionStorage
3. إعادة تسجيل الدخول

---

## 📊 هيكل البيانات

### بيانات العامل في قاعدة البيانات:
```javascript
{
  id: 1,
  username: "staff_user",
  password: "staff123",
  name: "اسم العامل",
  role: "staff",
  staffType: "cashier", // cashier, cook, manager
  schoolId: 1,
  status: "active", // active, inactive
  permissions: ["sell", "view_products", "view_students"]
}
```

### أنواع العاملين والصلاحيات الافتراضية:
- **أمين الصندوق**: البيع، عرض المنتجات، عرض الطلاب
- **طباخ**: عرض المنتجات، عرض الطلبات
- **مدير**: جميع الصلاحيات

---

## 🚀 الميزات المتقدمة

### 1. البحث والفلترة
- البحث بالاسم أو اسم المستخدم
- فلترة حسب نوع العامل
- فلترة حسب المدرسة
- فلترة حسب الحالة

### 2. التصفح بالصفحات
- عرض 10 عاملين في كل صفحة
- أزرار التنقل بين الصفحات

### 3. الإحصائيات
- عدد العاملين الإجمالي
- عدد العاملين المفعلين/غير المفعلين
- توزيع العاملين حسب النوع

---

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. تحقق من دليل استكشاف الأخطاء أعلاه
2. استخدم صفحة الاختبار للتشخيص
3. فحص console المتصفح للأخطاء
4. التأكد من تحديث المتصفح

---

## 🔄 التحديثات المستقبلية

الميزات المخطط إضافتها:
- [ ] تغيير كلمة المرور للعاملين
- [ ] سجل أنشطة العاملين
- [ ] إشعارات للعاملين
- [ ] تصدير قائمة العاملين
- [ ] استيراد العاملين من ملف Excel

---

**تم إنشاء هذا الدليل في:** ديسمبر 2024  
**إصدار النظام:** 1.0  
**آخر تحديث:** ديسمبر 2024
