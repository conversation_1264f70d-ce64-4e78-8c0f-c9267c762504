<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التوافق مع المتصفحات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .feature-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .feature-supported {
            background: #e8f5e9;
            border-color: #4caf50;
        }
        .feature-not-supported {
            background: #ffebee;
            border-color: #f44336;
        }
        .browser-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .performance-meter {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .performance-bar {
            height: 100%;
            background: linear-gradient(90deg, #f44336, #ff9800, #4caf50);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 اختبار التوافق مع المتصفحات</h1>
            <p>فحص شامل لتوافق النظام مع مختلف المتصفحات والأجهزة</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="database-test.html">🗄️ اختبار قاعدة البيانات</a>
            <a href="admin-login-quick.html">⚡ اختبار المدير</a>
        </div>

        <div class="test-section">
            <h3>1. معلومات المتصفح والجهاز</h3>
            <button onclick="showBrowserInfo()">عرض معلومات المتصفح</button>
            <div id="browser-info-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. فحص الميزات المطلوبة</h3>
            <button onclick="checkRequiredFeatures()">فحص الميزات</button>
            <div id="features-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار الأداء</h3>
            <button onclick="runPerformanceTest()">اختبار الأداء</button>
            <div id="performance-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار التخزين المحلي</h3>
            <button onclick="testLocalStorage()">اختبار localStorage</button>
            <button onclick="testSessionStorage()">اختبار sessionStorage</button>
            <button onclick="testCookies()">اختبار الكوكيز</button>
            <div id="storage-test-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار الشبكة والاتصال</h3>
            <button onclick="testNetworkFeatures()">اختبار الشبكة</button>
            <div id="network-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. تقرير التوافق الشامل</h3>
            <button onclick="generateCompatibilityReport()">إنشاء تقرير شامل</button>
            <div id="report-result" class="result"></div>
        </div>
    </div>

    <script>
        // عرض معلومات المتصفح
        function showBrowserInfo() {
            const resultDiv = document.getElementById('browser-info-result');
            
            try {
                const nav = navigator;
                const screen = window.screen;
                
                let html = '<div class="browser-info">';
                html += '<h4>معلومات المتصفح:</h4>';
                html += `<strong>اسم المتصفح:</strong> ${getBrowserName()}<br>`;
                html += `<strong>الإصدار:</strong> ${nav.appVersion}<br>`;
                html += `<strong>وكيل المستخدم:</strong> ${nav.userAgent}<br>`;
                html += `<strong>اللغة:</strong> ${nav.language}<br>`;
                html += `<strong>المنصة:</strong> ${nav.platform}<br>`;
                html += `<strong>الكوكيز مفعلة:</strong> ${nav.cookieEnabled ? 'نعم' : 'لا'}<br>`;
                html += `<strong>متصل بالإنترنت:</strong> ${nav.onLine ? 'نعم' : 'لا'}<br>`;
                html += '</div>';

                html += '<div class="browser-info">';
                html += '<h4>معلومات الشاشة:</h4>';
                html += `<strong>الدقة:</strong> ${screen.width} × ${screen.height}<br>`;
                html += `<strong>عمق الألوان:</strong> ${screen.colorDepth} بت<br>`;
                html += `<strong>نسبة البكسل:</strong> ${window.devicePixelRatio}<br>`;
                html += `<strong>المنطقة الزمنية:</strong> ${Intl.DateTimeFormat().resolvedOptions().timeZone}<br>`;
                html += '</div>';

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في عرض معلومات المتصفح: ${error.message}</div>`;
            }
        }

        // فحص الميزات المطلوبة
        function checkRequiredFeatures() {
            const resultDiv = document.getElementById('features-result');
            
            try {
                const features = {
                    'localStorage': typeof(Storage) !== "undefined" && window.localStorage,
                    'sessionStorage': typeof(Storage) !== "undefined" && window.sessionStorage,
                    'JSON': typeof JSON !== "undefined",
                    'fetch API': typeof fetch !== "undefined",
                    'Promise': typeof Promise !== "undefined",
                    'async/await': checkAsyncAwait(),
                    'ES6 Arrow Functions': checkArrowFunctions(),
                    'CSS Grid': checkCSSGrid(),
                    'CSS Flexbox': checkCSSFlexbox(),
                    'Web Workers': typeof Worker !== "undefined",
                    'Service Workers': 'serviceWorker' in navigator,
                    'Geolocation': 'geolocation' in navigator,
                    'File API': typeof FileReader !== "undefined",
                    'Canvas': checkCanvas(),
                    'WebGL': checkWebGL(),
                    'Touch Events': 'ontouchstart' in window,
                    'Device Orientation': 'DeviceOrientationEvent' in window,
                    'Notifications': 'Notification' in window,
                    'Clipboard API': 'clipboard' in navigator,
                    'Intersection Observer': 'IntersectionObserver' in window
                };

                let html = '<div class="info">نتائج فحص الميزات:</div>';
                html += '<div class="feature-grid">';

                let supportedCount = 0;
                const totalFeatures = Object.keys(features).length;

                for (const [feature, supported] of Object.entries(features)) {
                    const className = supported ? 'feature-supported' : 'feature-not-supported';
                    const icon = supported ? '✓' : '✗';
                    html += `
                        <div class="feature-item ${className}">
                            <span>${feature}</span>
                            <span>${icon}</span>
                        </div>
                    `;
                    if (supported) supportedCount++;
                }

                html += '</div>';

                const compatibilityPercentage = Math.round((supportedCount / totalFeatures) * 100);
                html += `<div class="info">نسبة التوافق: ${compatibilityPercentage}% (${supportedCount}/${totalFeatures})</div>`;

                if (compatibilityPercentage >= 90) {
                    html += '<div class="success">🎉 توافق ممتاز! المتصفح يدعم جميع الميزات تقريباً</div>';
                } else if (compatibilityPercentage >= 75) {
                    html += '<div class="warning">⚠️ توافق جيد مع بعض القيود</div>';
                } else {
                    html += '<div class="error">❌ توافق ضعيف - قد تواجه مشاكل في الاستخدام</div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في فحص الميزات: ${error.message}</div>`;
            }
        }

        // اختبار الأداء
        function runPerformanceTest() {
            const resultDiv = document.getElementById('performance-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار الأداء...</div>';

                const tests = [
                    { name: 'معالجة JSON', test: testJSONPerformance },
                    { name: 'عمليات DOM', test: testDOMPerformance },
                    { name: 'حلقات JavaScript', test: testLoopPerformance },
                    { name: 'تخزين البيانات', test: testStoragePerformance }
                ];

                let html = '<div class="info">نتائج اختبار الأداء:</div>';
                let totalScore = 0;

                tests.forEach(test => {
                    const startTime = performance.now();
                    test.test();
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    let score = 100;
                    if (duration > 100) score = 50;
                    if (duration > 500) score = 25;
                    if (duration > 1000) score = 10;
                    
                    totalScore += score;
                    
                    html += `
                        <div class="browser-info">
                            <strong>${test.name}:</strong> ${duration.toFixed(2)} مللي ثانية (النقاط: ${score}/100)
                            <div class="performance-meter">
                                <div class="performance-bar" style="width: ${score}%"></div>
                            </div>
                        </div>
                    `;
                });

                const averageScore = Math.round(totalScore / tests.length);
                html += `<div class="info">النقاط الإجمالية: ${averageScore}/100</div>`;

                if (averageScore >= 80) {
                    html += '<div class="success">🚀 أداء ممتاز!</div>';
                } else if (averageScore >= 60) {
                    html += '<div class="warning">⚡ أداء جيد</div>';
                } else {
                    html += '<div class="error">🐌 أداء بطيء</div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في اختبار الأداء: ${error.message}</div>`;
            }
        }

        // اختبار التخزين المحلي
        function testLocalStorage() {
            testStorageMethod('localStorage', localStorage);
        }

        function testSessionStorage() {
            testStorageMethod('sessionStorage', sessionStorage);
        }

        function testCookies() {
            const resultDiv = document.getElementById('storage-test-result');
            
            try {
                // اختبار كتابة الكوكيز
                document.cookie = "test=value; path=/";
                
                // اختبار قراءة الكوكيز
                const cookieExists = document.cookie.includes("test=value");
                
                if (cookieExists) {
                    resultDiv.innerHTML += '<div class="success">✓ الكوكيز تعمل بشكل صحيح</div>';
                    // حذف الكوكيز التجريبية
                    document.cookie = "test=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                } else {
                    resultDiv.innerHTML += '<div class="error">✗ الكوكيز لا تعمل</div>';
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في اختبار الكوكيز: ${error.message}</div>`;
            }
        }

        function testStorageMethod(name, storage) {
            const resultDiv = document.getElementById('storage-test-result');
            
            try {
                const testKey = 'compatibility_test';
                const testValue = { test: true, timestamp: Date.now() };
                
                // اختبار الكتابة
                storage.setItem(testKey, JSON.stringify(testValue));
                
                // اختبار القراءة
                const retrieved = JSON.parse(storage.getItem(testKey));
                
                // اختبار الحذف
                storage.removeItem(testKey);
                
                if (retrieved && retrieved.test === true) {
                    resultDiv.innerHTML += `<div class="success">✓ ${name} يعمل بشكل صحيح</div>`;
                } else {
                    resultDiv.innerHTML += `<div class="error">✗ ${name} لا يعمل بشكل صحيح</div>`;
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في اختبار ${name}: ${error.message}</div>`;
            }
        }

        // اختبار الشبكة
        function testNetworkFeatures() {
            const resultDiv = document.getElementById('network-result');
            
            try {
                let html = '<div class="info">نتائج اختبار الشبكة:</div>';

                // فحص حالة الاتصال
                html += `<div class="${navigator.onLine ? 'success' : 'error'}">
                    ${navigator.onLine ? '✓' : '✗'} حالة الاتصال: ${navigator.onLine ? 'متصل' : 'غير متصل'}
                </div>`;

                // فحص نوع الاتصال
                if ('connection' in navigator) {
                    const connection = navigator.connection;
                    html += `<div class="info">نوع الاتصال: ${connection.effectiveType || 'غير معروف'}</div>`;
                    html += `<div class="info">سرعة التحميل: ${connection.downlink || 'غير معروف'} Mbps</div>`;
                }

                // اختبار fetch API
                if (typeof fetch !== 'undefined') {
                    html += '<div class="success">✓ fetch API متوفر</div>';
                } else {
                    html += '<div class="error">✗ fetch API غير متوفر</div>';
                }

                // اختبار XMLHttpRequest
                if (typeof XMLHttpRequest !== 'undefined') {
                    html += '<div class="success">✓ XMLHttpRequest متوفر</div>';
                } else {
                    html += '<div class="error">✗ XMLHttpRequest غير متوفر</div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في اختبار الشبكة: ${error.message}</div>`;
            }
        }

        // إنشاء تقرير التوافق الشامل
        function generateCompatibilityReport() {
            const resultDiv = document.getElementById('report-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري إنشاء التقرير الشامل...</div>';

                let html = '<div class="info">📋 تقرير التوافق الشامل:</div>';
                
                // معلومات أساسية
                html += '<div class="browser-info">';
                html += '<h4>المعلومات الأساسية:</h4>';
                html += `المتصفح: ${getBrowserName()}<br>`;
                html += `المنصة: ${navigator.platform}<br>`;
                html += `اللغة: ${navigator.language}<br>`;
                html += `التاريخ: ${new Date().toLocaleString('ar-SA')}<br>`;
                html += '</div>';

                // تقييم عام
                const overallScore = calculateOverallCompatibility();
                html += `<div class="browser-info">`;
                html += `<h4>التقييم العام:</h4>`;
                html += `النقاط: ${overallScore}/100<br>`;
                
                if (overallScore >= 90) {
                    html += '<span class="success">التقييم: ممتاز 🌟</span><br>';
                    html += 'التوصية: النظام متوافق بالكامل مع هذا المتصفح';
                } else if (overallScore >= 75) {
                    html += '<span class="warning">التقييم: جيد ⚡</span><br>';
                    html += 'التوصية: النظام متوافق مع قيود طفيفة';
                } else if (overallScore >= 50) {
                    html += '<span class="warning">التقييم: مقبول ⚠️</span><br>';
                    html += 'التوصية: قد تواجه بعض المشاكل';
                } else {
                    html += '<span class="error">التقييم: ضعيف ❌</span><br>';
                    html += 'التوصية: يُنصح بتحديث المتصفح';
                }
                html += '</div>';

                // توصيات
                html += '<div class="browser-info">';
                html += '<h4>التوصيات:</h4>';
                html += getRecommendations(overallScore);
                html += '</div>';

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إنشاء التقرير: ${error.message}</div>`;
            }
        }

        // دوال مساعدة
        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'Google Chrome';
            if (userAgent.includes('Firefox')) return 'Mozilla Firefox';
            if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Microsoft Edge';
            if (userAgent.includes('Opera')) return 'Opera';
            return 'متصفح غير معروف';
        }

        function checkAsyncAwait() {
            try {
                eval('(async () => {})');
                return true;
            } catch (e) {
                return false;
            }
        }

        function checkArrowFunctions() {
            try {
                eval('(() => {})');
                return true;
            } catch (e) {
                return false;
            }
        }

        function checkCSSGrid() {
            return CSS.supports('display', 'grid');
        }

        function checkCSSFlexbox() {
            return CSS.supports('display', 'flex');
        }

        function checkCanvas() {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && canvas.getContext('2d'));
        }

        function checkWebGL() {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
        }

        // اختبارات الأداء
        function testJSONPerformance() {
            const data = { test: 'data', numbers: Array.from({length: 1000}, (_, i) => i) };
            for (let i = 0; i < 100; i++) {
                JSON.stringify(data);
                JSON.parse(JSON.stringify(data));
            }
        }

        function testDOMPerformance() {
            const container = document.createElement('div');
            for (let i = 0; i < 100; i++) {
                const element = document.createElement('div');
                element.textContent = `Element ${i}`;
                container.appendChild(element);
            }
        }

        function testLoopPerformance() {
            let sum = 0;
            for (let i = 0; i < 100000; i++) {
                sum += i;
            }
        }

        function testStoragePerformance() {
            const data = { test: 'performance', timestamp: Date.now() };
            for (let i = 0; i < 50; i++) {
                localStorage.setItem(`test_${i}`, JSON.stringify(data));
                localStorage.getItem(`test_${i}`);
                localStorage.removeItem(`test_${i}`);
            }
        }

        function calculateOverallCompatibility() {
            // حساب تقريبي للتوافق العام
            let score = 0;
            
            // الميزات الأساسية
            if (typeof localStorage !== 'undefined') score += 20;
            if (typeof JSON !== 'undefined') score += 15;
            if (typeof fetch !== 'undefined') score += 15;
            if (typeof Promise !== 'undefined') score += 15;
            
            // الميزات المتقدمة
            if (checkAsyncAwait()) score += 10;
            if (checkCSSGrid()) score += 10;
            if (checkCSSFlexbox()) score += 10;
            if (checkCanvas()) score += 5;
            
            return Math.min(score, 100);
        }

        function getRecommendations(score) {
            let recommendations = '';
            
            if (score < 75) {
                recommendations += '• يُنصح بتحديث المتصفح إلى أحدث إصدار<br>';
            }
            
            if (!checkAsyncAwait()) {
                recommendations += '• المتصفح لا يدعم async/await - قد تواجه مشاكل في الأداء<br>';
            }
            
            if (typeof localStorage === 'undefined') {
                recommendations += '• التخزين المحلي غير مدعوم - لن تعمل بعض الميزات<br>';
            }
            
            if (score >= 90) {
                recommendations += '• المتصفح متوافق بالكامل - لا توجد توصيات خاصة<br>';
            }
            
            recommendations += '• تأكد من تفعيل JavaScript<br>';
            recommendations += '• تأكد من تفعيل الكوكيز<br>';
            
            return recommendations || 'لا توجد توصيات خاصة';
        }
    </script>
</body>
</html>
