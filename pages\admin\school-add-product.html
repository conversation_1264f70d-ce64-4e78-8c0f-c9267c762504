<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج جديد - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }
        
        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            background-color: var(--dark-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #eee;
        }
        
        .image-preview {
            width: 100%;
            height: 200px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            display: none;
        }
        
        .image-preview i {
            font-size: 3rem;
            color: #ddd;
        }
        
        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }
        
        .logout-btn i {
            margin-left: 8px;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير المدرسة</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير المدرسة</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="school-dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="school-students.html"><i class="fas fa-users"></i> إدارة الطلاب</a></li>
                <li><a href="school-products.html" class="active"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="school-orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="school-reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="school-settings.html"><i class="fas fa-cog"></i> إعدادات المدرسة</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1>إضافة منتج جديد</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>
            
            <div class="content-card">
                <h2>معلومات المنتج</h2>
                <form id="add-product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-name">اسم المنتج</label>
                            <input type="text" id="product-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="product-price">السعر (ريال)</label>
                            <input type="number" id="product-price" name="price" min="0" step="0.5" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-category">الفئة</label>
                        <select id="product-category" name="category" required>
                            <option value="">اختر الفئة</option>
                            <option value="ساندويتشات">ساندويتشات</option>
                            <option value="مشروبات">مشروبات</option>
                            <option value="حلويات">حلويات</option>
                            <option value="وجبات خفيفة">وجبات خفيفة</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-description">وصف المنتج</label>
                        <textarea id="product-description" name="description"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-image">صورة المنتج</label>
                        <div class="image-preview" id="image-preview">
                            <i class="fas fa-image"></i>
                            <img id="preview-img" src="#" alt="معاينة الصورة">
                        </div>
                        <input type="file" id="product-image" name="image" accept="image/*">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="cancel-btn" class="btn btn-secondary">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المنتج</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is school admin
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'school') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            
            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            
            // Image Preview
            const productImage = document.getElementById('product-image');
            const previewImg = document.getElementById('preview-img');
            const previewIcon = document.querySelector('#image-preview i');
            
            productImage.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        previewImg.style.display = 'block';
                        previewIcon.style.display = 'none';
                    }
                    
                    reader.readAsDataURL(file);
                }
            });
            
            // Form Submission
            const addProductForm = document.getElementById('add-product-form');
            
            addProductForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const name = document.getElementById('product-name').value;
                const price = parseFloat(document.getElementById('product-price').value);
                const category = document.getElementById('product-category').value;
                const description = document.getElementById('product-description').value;
                
                // Get database
                const db = getDatabase();
                
                // Create new product
                const newProduct = {
                    id: db.products.length + 1,
                    name: name,
                    price: price,
                    category: category,
                    description: description,
                    image: 'default-product.jpg' // In a real app, we would upload the image
                };
                
                // Add product to database
                db.products.push(newProduct);
                
                // Save database
                saveDatabase(db);
                
                // Alert success and redirect
                alert('تم إضافة المنتج بنجاح!');
                window.location.href = 'school-dashboard.html';
            });
            
            // Cancel Button
            document.getElementById('cancel-btn').addEventListener('click', function() {
                window.location.href = 'school-dashboard.html';
            });
            
            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
    </script>
</body>
</html>
