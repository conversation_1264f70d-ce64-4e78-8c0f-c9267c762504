<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام المقصف المدرسي</title>
    <meta name="description" content="نظام نقطة بيع للعاملين في المقصف المدرسي">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        /* متغيرات CSS - الوضع النهاري */
        :root {
            --primary-color: #2e7d32;
            --primary-dark: #1b5e20;
            --primary-light: #4caf50;
            --secondary-color: #66bb6a;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --info-color: #2196f3;
            --light-color: #f8f9fa;
            --dark-color: #333;
            --border-color: #e0e0e0;
            --text-color: #333;
            --text-muted: #666;
            --card-bg: #ffffff;
            --bg-light: #f8f9fa;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;

            /* متغيرات خاصة بالثيم */
            --body-bg: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            --header-bg: rgba(255,255,255,0.95);
            --sidebar-bg: rgba(255,255,255,0.95);
            --input-bg: #ffffff;
            --button-bg: #f8f9fa;
            --hover-bg: #f0f0f0;
        }

        /* متغيرات CSS - الوضع الليلي */
        [data-theme="dark"] {
            --primary-color: #66bb6a;
            --primary-dark: #4caf50;
            --primary-light: #81c784;
            --secondary-color: #81c784;
            --success-color: #66bb6a;
            --danger-color: #ef5350;
            --warning-color: #ffb74d;
            --info-color: #42a5f5;
            --light-color: #2a2a2a;
            --dark-color: #e0e0e0;
            --border-color: #404040;
            --text-color: #e0e0e0;
            --text-muted: #b0b0b0;
            --card-bg: #1e1e1e;
            --bg-light: #2a2a2a;
            --shadow: 0 2px 10px rgba(0,0,0,0.4);
            --border-radius: 8px;
            --transition: all 0.3s ease;

            /* متغيرات خاصة بالثيم الليلي */
            --body-bg: linear-gradient(135deg, #1a1a1a 0%, #0d1b0f 100%);
            --header-bg: rgba(30,30,30,0.95);
            --sidebar-bg: rgba(30,30,30,0.95);
            --input-bg: #2a2a2a;
            --button-bg: #2a2a2a;
            --hover-bg: #404040;
        }

        /* إعادة تعيين الأنماط */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--body-bg);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-color);
            overflow-x: hidden;
            overflow-y: auto;
            transition: var(--transition);
        }

        /* رابط مركز الاختبارات */
        .test-center-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--primary-color);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            transition: var(--transition);
            box-shadow: var(--shadow);
        }

        .test-center-link:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        /* الهيدر */
        .header {
            background: var(--header-bg);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            min-height: 50px;
            transition: var(--transition);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* لوجو الشركة */
        .company-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .company-name {
            display: flex;
            flex-direction: column;
            line-height: 1;
        }

        .company-name-en {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: 'Arial', sans-serif;
        }

        .company-name-ar {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-muted);
            margin-top: 1px;
        }

        .page-title {
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-info {
            text-align: left;
            font-size: 0.8rem;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.8rem;
        }

        .user-role {
            color: var(--text-muted);
            font-size: 0.7rem;
        }

        .logout-btn {
            background: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(244, 67, 54, 0.2);
            padding: 6px 12px;
            border-radius: 15px;
            text-decoration: none;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .logout-btn:hover {
            background: var(--danger-color);
            color: white;
            transform: translateY(-2px);
        }

        /* زر إظهار/إخفاء الطلبات المسبقة */
        .toggle-preorders-btn {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 25px;
            padding: 8px 16px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 12px;
            position: relative;
            overflow: hidden;
        }

        .toggle-preorders-btn:hover {
            background: rgba(255, 193, 7, 0.2);
            border-color: var(--warning-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .toggle-preorders-btn.active {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }

        .toggle-preorders-btn.active:hover {
            background: #f57c00;
        }

        .toggle-text {
            font-size: 0.8rem;
        }

        .preorders-badge {
            background: var(--danger-color);
            color: white;
            font-size: 0.7rem;
            font-weight: 700;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .preorders-badge.zero {
            background: var(--text-muted);
            animation: none;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* ===== قسم مسح الباركود المتقدم ===== */
        .barcode-scanner-section {
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: var(--shadow);
            border: 2px solid var(--border-color);
            transition: var(--transition);
        }

        .barcode-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1rem;
        }

        .barcode-header i {
            font-size: 1.2rem;
        }

        .barcode-input-area {
            position: relative;
            margin-bottom: 10px;
        }

        .barcode-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.9rem;
            background: var(--input-bg);
            color: var(--text-color);
            transition: var(--transition);
            font-family: 'Courier New', monospace;
        }

        .barcode-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .barcode-input.scanning {
            border-color: var(--warning-color);
            background: rgba(255, 152, 0, 0.05);
        }

        .barcode-input.success {
            border-color: var(--success-color);
            background: rgba(76, 175, 80, 0.05);
        }

        .barcode-input.error {
            border-color: var(--danger-color);
            background: rgba(244, 67, 54, 0.05);
        }

        .barcode-status {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 8px;
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .barcode-status.scanning {
            color: var(--warning-color);
        }

        .barcode-status.success {
            color: var(--success-color);
        }

        .barcode-status.error {
            color: var(--danger-color);
        }

        .barcode-status i {
            font-size: 0.9rem;
        }

        .barcode-instructions {
            background: rgba(33, 150, 243, 0.05);
            border: 1px solid rgba(33, 150, 243, 0.2);
            border-radius: 6px;
            padding: 8px;
            font-size: 0.75rem;
            color: var(--info-color);
        }

        .barcode-instructions i {
            margin-left: 5px;
        }

        /* تأثيرات الباركود */
        .barcode-scanner-section.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 20px rgba(46, 125, 50, 0.2);
        }

        @keyframes barcodeScan {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .barcode-scanner-section.scanning {
            animation: barcodeScan 1s ease-in-out infinite;
        }

        /* ===== زر تبديل الوضع الليلي/النهاري ===== */
        .theme-toggle-btn {
            background: var(--button-bg);
            border: 2px solid var(--border-color);
            border-radius: 25px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-color);
            position: relative;
            overflow: hidden;
        }

        .theme-toggle-btn:hover {
            background: var(--hover-bg);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .theme-toggle-track {
            width: 40px;
            height: 20px;
            background: var(--border-color);
            border-radius: 20px;
            position: relative;
            transition: all 0.3s ease;
        }

        .theme-toggle-thumb {
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            right: 2px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .theme-icon {
            font-size: 0.6rem;
            color: var(--warning-color);
            transition: all 0.3s ease;
        }

        /* حالة الوضع الليلي */
        [data-theme="dark"] .theme-toggle-track {
            background: var(--primary-color);
        }

        [data-theme="dark"] .theme-toggle-thumb {
            transform: translateX(-20px);
            background: var(--card-bg);
        }

        [data-theme="dark"] .theme-icon {
            color: var(--info-color);
        }

        .theme-label {
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }

        /* تأثيرات متقدمة للزر */
        .theme-toggle-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .theme-toggle-btn:hover::before {
            left: 100%;
        }

        /* تأثير النقر */
        .theme-toggle-btn:active {
            transform: translateY(0) scale(0.98);
        }

        /* تأثير التبديل */
        .theme-toggle-btn.switching .theme-toggle-thumb {
            animation: thumbBounce 0.6s ease;
        }

        @keyframes thumbBounce {
            0%, 100% { transform: translateX(0) scale(1); }
            25% { transform: translateX(-5px) scale(1.1); }
            50% { transform: translateX(-15px) scale(0.9); }
            75% { transform: translateX(-25px) scale(1.1); }
        }

        [data-theme="dark"] .theme-toggle-btn.switching .theme-toggle-thumb {
            animation: thumbBounceDark 0.6s ease;
        }

        @keyframes thumbBounceDark {
            0%, 100% { transform: translateX(-20px) scale(1); }
            25% { transform: translateX(-15px) scale(1.1); }
            50% { transform: translateX(-5px) scale(0.9); }
            75% { transform: translateX(5px) scale(1.1); }
        }

        /* المحتوى الرئيسي */
        .main-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            gap: 12px;
            padding: 12px;
            min-height: calc(100vh - 50px);
            max-height: calc(100vh - 50px);
            overflow: visible;
            transition: grid-template-columns 0.5s ease;
        }

        /* العمود الأيسر - الطلبات المسبقة */
        .preorders-sidebar {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 12px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: right center;
        }

        /* حالة الإخفاء */
        .preorders-sidebar.hidden {
            transform: translateX(-100%) scale(0.8);
            opacity: 0;
            pointer-events: none;
            width: 0;
            padding: 0;
            margin: 0;
            min-width: 0;
        }

        /* حالة الظهور */
        .preorders-sidebar.visible {
            transform: translateX(0) scale(1);
            opacity: 1;
            pointer-events: all;
        }

        /* تأثير النبضة عند وجود طلبات جديدة */
        .preorders-sidebar.new-orders {
            animation: newOrderPulse 2s ease-in-out infinite;
        }

        @keyframes newOrderPulse {
            0%, 100% {
                box-shadow: var(--shadow);
            }
            50% {
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
                transform: scale(1.02);
            }
        }

        /* ===== نظام التخطيط المتطور ===== */

        /* التخطيط الكامل: يسار + وسط + يمين */
        .main-container.layout-full {
            grid-template-columns: 280px 1fr 320px;
        }

        /* التخطيط الأيسر: يسار + وسط (العمود الأيمن مخفي) */
        .main-container.layout-left-only {
            grid-template-columns: 280px 1fr;
        }

        /* التخطيط الأيمن: وسط + يمين */
        .main-container.layout-right-only {
            grid-template-columns: 1fr 320px;
        }

        /* التخطيط الوسط: وسط فقط */
        .main-container.layout-center-only {
            grid-template-columns: 1fr;
        }

        /* التخطيط بدون العمود الأيسر: وسط + يمين (العمود الأيسر مخفي) */
        .main-container.layout-no-left {
            grid-template-columns: 1fr 320px;
        }

        /* ===== أنماط الأعمدة المتطورة ===== */

        /* العمود الأيسر */
        .preorders-sidebar {
            transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
            transform-origin: right center;
        }

        .preorders-sidebar.sidebar-visible {
            transform: translateX(0) scale(1);
            opacity: 1;
            pointer-events: all;
        }

        .preorders-sidebar.sidebar-hidden {
            transform: translateX(-100%) scale(0.9);
            opacity: 0;
            pointer-events: none;
            width: 0;
            padding: 0;
            margin: 0;
            min-width: 0;
            overflow: hidden;
        }

        /* العمود الأيمن */
        .student-card-section {
            transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
            transform-origin: left center;
        }

        .student-card-section.sidebar-visible {
            transform: translateX(0) scale(1);
            opacity: 1;
            pointer-events: all;
        }

        .student-card-section.sidebar-hidden {
            transform: translateX(100%) scale(0.9);
            opacity: 0;
            pointer-events: none;
            width: 0;
            padding: 0;
            margin: 0;
            min-width: 0;
            overflow: hidden;
        }

        /* تأثيرات متطورة */
        .new-orders-pulse {
            animation: advancedPulse 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
        }

        @keyframes advancedPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(255, 193, 7, 0.8);
            }
        }

        /* تأثيرات قسم الطلبات المسبقة المتقدمة */
        .prepaid-orders {
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            transform-origin: top center;
            overflow: hidden;
        }

        .prepaid-orders.section-hidden {
            max-height: 0;
            padding: 0;
            margin: 0;
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }

        .prepaid-orders:not(.section-hidden) {
            max-height: 500px;
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* تأثير الانزلاق المتقدم */
        .prepaid-orders-list {
            transition: all 0.3s ease-in-out;
        }

        .section-hidden .prepaid-orders-list {
            transform: translateY(-10px);
            opacity: 0;
        }

        /* تأثيرات الانتقال المتطورة */
        .main-container {
            transition: grid-template-columns 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        /* تحسينات الأداء */
        .preorders-sidebar,
        .student-card-section {
            will-change: transform, opacity;
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* حالات التحميل */
        .sidebar-loading {
            position: relative;
            overflow: hidden;
        }

        .sidebar-loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }









        /* ===== نظام البحث المتقدم ===== */

        .advanced-search-container {
            position: relative;
            margin-bottom: 15px;
        }

        .student-search-input.advanced-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .student-search-input.advanced-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            transform: translateY(-2px);
        }

        .search-indicator {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .search-indicator:hover {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        .search-indicator.searching {
            animation: searchPulse 1s infinite;
        }

        @keyframes searchPulse {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.2); }
        }

        /* نتائج البحث السريع */
        .quick-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .quick-result-item {
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-result-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .quick-result-item:last-child {
            border-bottom: none;
        }

        .result-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .result-info {
            flex: 1;
        }

        .result-name {
            font-weight: 600;
            color: #333;
            font-size: 13px;
        }

        .result-details {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        /* أزرار البحث المتقدمة */
        .advanced-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-top: 15px;
        }

        .advanced-scan-btn,
        .advanced-manual-btn,
        .quick-scan-btn {
            position: relative;
            padding: 10px 12px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .advanced-scan-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, #45a049 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .advanced-manual-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .quick-scan-btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .advanced-scan-btn:hover,
        .advanced-manual-btn:hover,
        .quick-scan-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .advanced-scan-btn:active,
        .advanced-manual-btn:active,
        .quick-scan-btn:active {
            transform: translateY(-1px);
        }

        .btn-text {
            font-size: 10px;
            font-weight: 500;
        }

        .btn-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* تأثيرات التحميل */
        .searching .btn-text {
            opacity: 0;
        }

        .searching .btn-loader {
            display: block !important;
        }

        .preorders-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .preorders-title {
            color: var(--warning-color);
            font-size: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .preorders-count {
            background: var(--warning-color);
            color: white;
            font-size: 0.7rem;
            font-weight: 700;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* أنماط لتمييز أنواع الطلبات */
        .order-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
            margin-top: 4px;
        }

        .order-type-badge.parent {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .order-type-badge.student {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .prepaid-student-name,
        .sidebar-preorder-student {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .prepaid-order-type,
        .sidebar-preorder-type {
            margin-top: 8px;
        }

        /* تأثيرات الطلبات الجديدة */
        .new-order-highlight {
            animation: newOrderPulse 3s ease-in-out;
            border: 2px solid #4CAF50 !important;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.5) !important;
        }

        @keyframes newOrderPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
            }
            25% {
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(76, 175, 80, 0.7);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 40px rgba(76, 175, 80, 0.9);
            }
            75% {
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(76, 175, 80, 0.7);
            }
        }

        /* تأثير الطلب الجديد في الشريط الجانبي */
        .sidebar-preorder-item.new-order-highlight {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
            border-left: 4px solid #4CAF50;
        }

        /* تأثير الطلب الجديد في القائمة الرئيسية */
        .prepaid-order.new-order-highlight {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
            border: 2px solid #4CAF50;
        }

        /* أنماط شارة الطلب الجديد */
        @keyframes newBadgePulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        .new-order-badge {
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .new-order-badge i {
            margin-right: 4px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* أزرار التحكم في الطلبات المسبقة */
        .preorders-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .toggle-left-preorders-btn {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            font-size: 0.8rem;
        }

        .toggle-left-preorders-btn:hover {
            background: #2196f3;
            color: white;
            transform: scale(1.1);
        }

        .toggle-left-preorders-btn.hidden {
            background: rgba(255, 87, 34, 0.1);
            color: #ff5722;
            border-color: rgba(255, 87, 34, 0.3);
        }

        .toggle-left-preorders-btn.hidden:hover {
            background: #ff5722;
            color: white;
        }

        .toggle-left-preorders-btn.hidden i {
            transform: rotate(180deg);
        }

        .toggle-left-preorders-btn i {
            transition: transform 0.3s ease;
        }

        .refresh-preorders-btn {
            background: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .refresh-preorders-btn:hover {
            background: var(--warning-color);
            color: white;
            transform: rotate(180deg);
        }

        .preorders-list {
            flex: 1;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
            transform-origin: top center;
            overflow: hidden;
        }

        /* تأثيرات إخفاء وإظهار قائمة الطلبات المسبقة في العمود الأيسر */
        .preorders-list.section-hidden {
            max-height: 0;
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
            padding: 0;
            margin: 0;
        }

        .preorders-list:not(.section-hidden) {
            max-height: calc(100vh - 200px);
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .empty-preorders {
            text-align: center;
            padding: 20px;
            color: var(--text-muted);
        }

        .empty-preorders i {
            font-size: 2rem;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        .empty-preorders p {
            font-size: 0.9rem;
            margin: 0;
        }

        .sidebar-preorder-item {
            background: white;
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .sidebar-preorder-item:hover {
            background: rgba(255, 193, 7, 0.1);
            border-color: var(--warning-color);
            transform: translateX(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .sidebar-preorder-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }

        .sidebar-preorder-student {
            font-weight: 600;
            font-size: 0.85rem;
            color: var(--text-color);
        }

        .sidebar-preorder-time {
            font-size: 0.7rem;
            color: var(--text-muted);
        }

        .sidebar-preorder-product {
            font-size: 0.8rem;
            color: var(--text-color);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .sidebar-preorder-total {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--warning-color);
        }

        .sidebar-preorder-actions {
            display: flex;
            gap: 4px;
            margin-top: 6px;
        }

        .sidebar-preorder-btn {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.7rem;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .sidebar-preorder-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .sidebar-preorder-btn.cancel {
            background: var(--danger-color);
        }

        .sidebar-preorder-btn.cancel:hover {
            background: #c62828;
        }

        /* قسم المنتجات */
        .products-section {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 12px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: var(--transition);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .section-title {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-box {
            position: relative;
            width: 200px;
        }

        .search-box input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .search-box i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        /* فلاتر المنتجات */
        .products-filters {
            display: flex;
            gap: 6px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 4px 10px;
            border: 2px solid var(--border-color);
            background: white;
            color: var(--text-color);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* شبكة المنتجات المحسنة */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
            flex: 1;
            overflow-y: auto;
            padding: 8px;
            max-height: calc(100vh - 140px);
            align-content: start;
        }

        /* المنتجات المصغرة في الصفحة الخارجية */
        .external-products-section {
            margin-bottom: 10px;
        }

        .external-products-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .external-products-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .external-product-btn {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 5px 3px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            min-height: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .external-product-btn:hover {
            border-color: var(--secondary-color);
            background: rgba(255, 152, 0, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .external-product-btn.out-of-stock {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
        }

        .external-product-btn.out-of-stock:hover {
            transform: none;
            border-color: var(--border-color);
            background: #f5f5f5;
        }

        .external-product-icon {
            font-size: 0.7rem;
            color: var(--secondary-color);
            margin-bottom: 1px;
        }

        .external-emoji {
            font-size: 1rem;
            display: block;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
        }

        .external-product-name {
            font-size: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .external-product-price {
            font-size: 0.45rem;
            font-weight: 700;
            color: var(--secondary-color);
            line-height: 1;
        }

        .external-product-btn.out-of-stock .external-product-icon,
        .external-product-btn.out-of-stock .external-product-name,
        .external-product-btn.out-of-stock .external-product-price {
            color: var(--text-muted);
        }

        /* زر الطلب المسبق في الشاشة الخارجية */
        .external-preorder-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(255, 193, 7, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.5rem;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 5;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
        }

        .external-product-btn:hover .external-preorder-btn {
            opacity: 1;
            transform: scale(1.1);
        }

        .external-preorder-btn:hover {
            background: #f57c00;
            transform: scale(1.2);
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5);
        }

        .product-card {
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: 15px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: var(--shadow);
            margin: 3px;
            min-height: 120px;
        }

        .product-card:hover {
            transform: translateY(-6px) scale(1.03);
            box-shadow: 0 12px 35px rgba(0,0,0,0.18);
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
        }

        .product-card:active {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
        }

        .product-card.out-of-stock {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .product-card.out-of-stock::after {
            content: 'نفد';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(244, 67, 54, 0.9);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.7rem;
        }

        .product-image {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 1.4rem;
            color: white;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.4);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .product-card:hover .product-image {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.5);
        }

        .product-card:hover .product-image::before {
            animation: shine 0.6s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }

        .product-emoji {
            font-size: 1.6rem;
            display: block;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.25));
            transition: all 0.4s ease;
            position: relative;
            z-index: 2;
        }

        .product-card:hover .product-emoji {
            transform: scale(1.15) rotate(8deg);
            filter: drop-shadow(0 5px 12px rgba(0,0,0,0.35));
        }

        /* أنماط خاصة للمنتجات الشعبية في المقصف */
        .product-card.popular .product-emoji {
            animation: popularBounce 2s infinite;
        }

        @keyframes popularBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .product-name {
            font-weight: 700;
            margin-bottom: 4px;
            font-size: 0.8rem;
            color: var(--text-color);
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-shadow: 0 1px 3px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .product-card:hover .product-name {
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .product-price {
            color: var(--primary-color);
            font-weight: 800;
            font-size: 0.9rem;
            margin-bottom: 2px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.15);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .product-card:hover .product-price {
            transform: scale(1.05);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .product-stock {
            font-size: 0.65rem;
            color: var(--text-muted);
            line-height: 1;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .product-card:hover .product-stock {
            color: var(--text-color);
        }

        .product-card.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.12) 0%, rgba(46, 125, 50, 0.18) 100%);
            transform: translateY(-5px) scale(1.06);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.5);
            animation: selectedPulse 2s infinite;
        }

        @keyframes selectedPulse {
            0%, 100% {
                box-shadow: 0 8px 25px rgba(46, 125, 50, 0.5);
                transform: translateY(-5px) scale(1.06);
            }
            50% {
                box-shadow: 0 12px 35px rgba(46, 125, 50, 0.7);
                transform: translateY(-6px) scale(1.07);
            }
        }

        .popular-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff6b35, #ff8e53);
            color: white;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            box-shadow: 0 3px 12px rgba(255, 107, 53, 0.5);
            z-index: 10;
            animation: popularPulse 2s infinite;
            border: 2px solid white;
        }

        @keyframes popularPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .product-card.popular {
            border-color: rgba(255, 107, 53, 0.3);
        }

        .product-card.popular:hover {
            border-color: #ff6b35;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
        }

        /* زر التعديل */
        .edit-btn {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(33, 150, 243, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            cursor: pointer;
            opacity: 0;
            transition: all 0.4s ease;
            z-index: 5;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .product-card:hover .edit-btn {
            opacity: 1;
            transform: scale(1.1);
        }

        .edit-btn:hover {
            background: #1976d2;
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.5);
        }

        /* زر الطلب المسبق */
        .preorder-btn-card {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 193, 7, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            cursor: pointer;
            opacity: 0;
            transition: all 0.4s ease;
            z-index: 5;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        .product-card:hover .preorder-btn-card {
            opacity: 1;
            transform: scale(1.1);
        }

        .preorder-btn-card:hover {
            background: #f57c00;
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
        }

        /* تحسينات الاستجابة للمنتجات */
        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
                gap: 10px;
            }

            .product-card {
                min-height: 110px;
                padding: 10px;
            }

            .product-image {
                width: 45px;
                height: 45px;
                font-size: 1.3rem;
            }
        }

        @media (max-width: 900px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 8px;
            }

            .product-card {
                min-height: 100px;
                padding: 8px;
            }

            .product-image {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .product-emoji {
                font-size: 1.4rem;
            }

            .product-name {
                font-size: 0.75rem;
            }

            .product-price {
                font-size: 0.85rem;
            }
        }

        @media (max-width: 600px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
                gap: 6px;
            }

            .product-card {
                min-height: 90px;
                padding: 6px;
                border-radius: 12px;
            }

            .product-image {
                width: 35px;
                height: 35px;
                font-size: 1.1rem;
                margin-bottom: 6px;
            }

            .product-emoji {
                font-size: 1.3rem;
            }

            .product-name {
                font-size: 0.7rem;
                margin-bottom: 3px;
            }

            .product-price {
                font-size: 0.8rem;
            }

            .product-stock {
                font-size: 0.6rem;
            }
        }

        /* قسم مسح بطاقة الطالب */
        .student-card-section {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 12px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            height: fit-content;
            overflow: hidden;
            transition: var(--transition);
        }

        .card-scanner-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .scanner-title {
            color: var(--primary-color);
            font-size: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .scanner-status {
            background: var(--success-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .scanner-status.offline {
            background: var(--danger-color);
        }

        /* منطقة مسح البطاقة */
        .card-input-area {
            background: var(--input-bg);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            margin-bottom: 10px;
            transition: var(--transition);
        }

        .card-input-area.active {
            border-color: var(--primary-color);
            background: rgba(46, 125, 50, 0.05);
        }

        .card-input-area .scan-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 6px;
            animation: pulse 2s infinite;
        }

        .card-input-area h3 {
            color: var(--text-color);
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .student-search-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 1rem;
            text-align: center;
            transition: var(--transition);
            margin-bottom: 15px;
        }

        .student-search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .search-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .scan-btn, .manual-search-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .scan-btn {
            background: var(--primary-color);
            color: white;
        }

        .scan-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .manual-search-btn {
            background: var(--info-color);
            color: white;
        }

        .manual-search-btn:hover {
            background: #1976d2;
            transform: translateY(-2px);
        }

        /* قائمة الطلاب السريعة */
        .quick-students {
            margin-top: 8px;
        }

        .quick-students-title {
            color: var(--text-color);
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .students-list {
            display: flex;
            flex-direction: column;
            gap: 3px;
            max-height: 120px;
            overflow-y: auto;
        }

        .student-quick-item {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 6px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .student-quick-item:hover {
            background: rgba(46, 125, 50, 0.05);
            border-color: var(--primary-color);
            transform: translateX(-3px);
        }

        .student-quick-info {
            flex: 1;
        }

        .student-quick-name {
            font-weight: 600;
            font-size: 0.75rem;
            color: var(--text-color);
            margin-bottom: 1px;
        }

        .student-quick-id {
            font-size: 0.65rem;
            color: var(--text-muted);
        }

        .student-quick-balance {
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--success-color);
        }

        .student-quick-balance.low {
            color: var(--warning-color);
        }

        .student-quick-balance.empty {
            color: var(--danger-color);
        }

        /* نافذة معلومات الطالب المنبثقة */
        .student-info-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .student-info-content {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            max-width: 95vw;
            width: 900px;
            max-height: 85vh;
            height: auto;
            overflow: visible;
            box-shadow: 0 15px 35px rgba(0,0,0,0.25);
            animation: slideUp 0.3s ease;
            position: relative;
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 15px;
        }

        /* العمود الأيسر - معلومات الطالب */
        .student-info-left {
            display: flex;
            flex-direction: column;
        }

        /* العمود الأيمن - البيع السريع */
        .student-info-right {
            display: flex;
            flex-direction: column;
        }

        /* قسم الطلبات المسبقة */
        .preorders-section {
            margin-bottom: 12px;
            background: rgba(255, 193, 7, 0.05);
            border: 1px solid rgba(255, 193, 7, 0.2);
            border-radius: 8px;
            padding: 8px;
        }

        .preorders-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            position: relative;
        }

        .preorders-count {
            background: var(--warning-color);
            color: white;
            font-size: 0.7rem;
            font-weight: 700;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            margin-right: auto;
        }

        .preorders-container {
            max-height: 120px;
            overflow-y: auto;
        }

        .preorder-item {
            background: white;
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 6px;
            padding: 6px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .preorder-item:hover {
            background: rgba(255, 193, 7, 0.1);
            border-color: var(--warning-color);
            transform: translateX(-2px);
        }

        .preorder-item:last-child {
            margin-bottom: 0;
        }

        .preorder-icon {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, var(--warning-color), #ffb74d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            flex-shrink: 0;
        }

        .preorder-details {
            flex: 1;
            min-width: 0;
        }

        .preorder-product {
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 1px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .preorder-info {
            font-size: 0.6rem;
            color: var(--text-muted);
            display: flex;
            gap: 8px;
        }

        .preorder-actions {
            display: flex;
            gap: 4px;
            flex-shrink: 0;
        }

        .preorder-btn {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 6px;
            font-size: 0.6rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preorder-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        .preorder-btn.cancel {
            background: var(--danger-color);
        }

        .preorder-btn.cancel:hover {
            background: #c62828;
        }

        .no-preorders {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.7rem;
            padding: 12px;
            font-style: italic;
        }

        .products-mini-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 4px;
            margin-bottom: 8px;
        }

        .product-mini-btn {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 6px 4px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            min-height: 35px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .product-mini-btn:hover {
            border-color: var(--secondary-color);
            background: rgba(255, 152, 0, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .product-mini-btn.out-of-stock {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
        }

        .product-mini-btn.out-of-stock:hover {
            transform: none;
            border-color: var(--border-color);
            background: #f5f5f5;
        }

        .product-mini-icon {
            font-size: 0.8rem;
            color: var(--secondary-color);
            margin-bottom: 1px;
        }

        .mini-emoji {
            font-size: 0.9rem;
            display: block;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
        }

        .product-mini-name {
            font-size: 0.55rem;
            font-weight: 600;
            color: var(--text-color);
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .product-mini-price {
            font-size: 0.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            line-height: 1;
        }

        .product-mini-btn.out-of-stock .product-mini-icon,
        .product-mini-btn.out-of-stock .product-mini-name,
        .product-mini-btn.out-of-stock .product-mini-price {
            color: var(--text-muted);
        }

        .student-info-header {
            text-align: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .student-avatar {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 6px;
            font-size: 1.2rem;
            color: white;
            font-weight: bold;
        }

        .student-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 2px;
        }

        .student-id {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-bottom: 6px;
        }

        .student-balance {
            font-size: 0.95rem;
            font-weight: 700;
            padding: 6px 12px;
            border-radius: 15px;
            display: inline-block;
        }

        .student-balance.high {
            background: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
        }

        .student-balance.medium {
            background: rgba(255, 152, 0, 0.1);
            color: var(--warning-color);
        }

        .student-balance.low {
            background: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        /* معلومات إضافية */
        .student-details {
            margin: 6px 0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.75rem;
        }

        .detail-label {
            font-weight: 600;
            color: var(--text-color);
        }

        .detail-value {
            color: var(--text-muted);
        }

        .allergy-warning {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 6px;
            padding: 6px;
            margin: 6px 0;
            text-align: center;
        }

        .allergy-warning .warning-icon {
            font-size: 1.2rem;
            color: var(--danger-color);
            margin-bottom: 3px;
        }

        .allergy-warning .warning-text {
            font-weight: 600;
            color: var(--danger-color);
            margin-bottom: 5px;
        }

        .allergy-warning .allergy-list {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        /* أزرار البيع السريع */
        .quick-sale-section {
            margin: 10px 0;
        }

        .quick-sale-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .quick-amounts-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 6px;
            margin-bottom: 10px;
        }

        .quick-amount-btn {
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 10px 6px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            min-height: 45px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow);
        }

        .quick-amount-btn:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(46, 125, 50, 0.1) 100%);
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        }

        .quick-amount-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }

        .amount-value {
            font-size: 1.2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 1px;
        }

        .amount-label {
            font-size: 0.6rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .quick-amount-btn:hover .amount-value {
            color: var(--primary-dark);
        }

        .quick-amount-btn:hover .amount-label {
            color: var(--primary-color);
        }

        /* مؤشر المبلغ المضاف */
        .amount-indicator {
            position: absolute;
            top: -6px;
            right: -6px;
            background: var(--success-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            border: 2px solid white;
            animation: pulse 0.3s ease;
        }

        /* إجمالي الطلب */
        .order-summary {
            background: var(--input-bg);
            border-radius: 8px;
            padding: 8px;
            margin: 8px 0;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 0.85rem;
        }

        .summary-row.total {
            font-weight: 700;
            font-size: 0.95rem;
            color: var(--primary-color);
            border-top: 2px solid var(--border-color);
            padding-top: 3px;
            margin-top: 3px;
        }

        /* أزرار العمليات */
        .modal-actions {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-top: 10px;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 0.85rem;
        }

        .action-btn.primary {
            background: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .action-btn.secondary {
            background: var(--border-color);
            color: var(--text-color);
        }

        .action-btn.secondary:hover {
            background: var(--text-muted);
            color: white;
        }

        .close-modal-btn {
            position: absolute;
            top: 15px;
            left: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-muted);
            cursor: pointer;
            transition: var(--transition);
        }

        .close-modal-btn:hover {
            color: var(--danger-color);
            transform: scale(1.1);
        }

        /* الرسوم المتحركة */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }

            .student-card-section {
                order: -1;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                gap: 6px;
            }

            .product-card {
                min-height: 75px;
                padding: 6px;
            }

            .product-image {
                width: 30px;
                height: 30px;
                font-size: 0.9rem;
            }

            .product-name {
                font-size: 0.7rem;
            }

            .product-price {
                font-size: 0.75rem;
            }

            .product-stock {
                font-size: 0.6rem;
            }

            .student-info-content {
                width: 95%;
                padding: 15px;
                max-height: 98vh;
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .quick-amounts-grid {
                grid-template-columns: repeat(5, 1fr);
                gap: 4px;
            }

            .quick-amount-btn {
                min-height: 45px;
                padding: 10px 6px;
            }

            .amount-value {
                font-size: 1.2rem;
            }

            .amount-label {
                font-size: 0.6rem;
            }

            .amount-indicator {
                width: 16px;
                height: 16px;
                font-size: 0.6rem;
                top: -4px;
                right: -4px;
            }
        }

        /* الطلبات مسبقة الدفع */
        .prepaid-orders {
            border-top: 2px solid var(--border-color);
            padding-top: 8px;
            margin-top: 8px;
        }

        .prepaid-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .prepaid-title {
            color: var(--info-color);
            font-size: 0.85rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .prepaid-count {
            background: var(--info-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .refresh-orders-btn {
            background: rgba(33, 150, 243, 0.1);
            color: var(--info-color);
            border: 1px solid rgba(33, 150, 243, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .refresh-orders-btn:hover {
            background: var(--info-color);
            color: white;
        }

        .prepaid-orders-list {
            max-height: 120px;
            overflow-y: auto;
        }

        .prepaid-order {
            background: rgba(33, 150, 243, 0.05);
            border: 1px solid rgba(33, 150, 243, 0.2);
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .prepaid-order:hover {
            background: rgba(33, 150, 243, 0.1);
            transform: translateY(-2px);
        }

        .prepaid-order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3px;
        }

        .prepaid-student-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.75rem;
        }

        .prepaid-order-total {
            color: var(--info-color);
            font-weight: 700;
            font-size: 0.75rem;
        }

        .prepaid-order-items {
            font-size: 0.7rem;
            color: var(--text-muted);
            margin-bottom: 3px;
        }

        .prepaid-order-time {
            font-size: 0.65rem;
            color: var(--text-muted);
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 20px 10px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 5px;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .empty-state p {
            font-size: 0.75rem;
        }



        /* نافذة تعديل المنتج */
        .edit-product-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(8px);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .edit-product-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: 3px solid var(--primary-color);
        }

        .edit-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .edit-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .edit-subtitle {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .edit-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-input {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(46, 125, 50, 0.1);
            transform: translateY(-2px);
        }

        .form-input.error {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.1);
        }

        .error-message {
            color: var(--danger-color);
            font-size: 0.8rem;
            margin-top: 5px;
            display: none;
        }

        .edit-actions {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .edit-btn-action {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .edit-btn-save {
            background: linear-gradient(135deg, var(--primary-color), #4caf50);
            color: white;
        }

        .edit-btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
        }

        .edit-btn-cancel {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .edit-btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }

        .close-edit-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-muted);
            cursor: pointer;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-edit-btn:hover {
            color: var(--danger-color);
            background: rgba(244, 67, 54, 0.1);
            transform: scale(1.1);
        }



        /* النوافذ المنبثقة */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideUp 0.3s ease;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        /* الفاتورة */
        .receipt {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 10px;
            border: 2px dashed var(--border-color);
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .receipt-header {
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
        }

        .receipt-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .receipt-info {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .receipt-items {
            margin: 20px 0;
            text-align: left;
        }

        .receipt-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px dotted var(--border-color);
        }

        .receipt-total {
            border-top: 2px solid var(--border-color);
            padding-top: 15px;
            margin-top: 15px;
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .receipt-footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px solid var(--border-color);
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* CSS للفاتورة الحرارية المتقدمة */
        .receipt-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .receipt-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            max-width: 420px;
            width: 95%;
            max-height: 85vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 2px solid #e0e0e0;
        }

        /* الفاتورة الحرارية */
        .thermal-receipt {
            background: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            width: 100%;
            max-width: 380px;
            margin: 0 auto;
            padding: 15px;
            border: 1px solid #ddd;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* رأس الفاتورة */
        .thermal-receipt .receipt-header {
            text-align: center;
            margin-bottom: 15px;
        }

        .logo-section {
            margin-bottom: 10px;
        }

        .receipt-logo {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
            letter-spacing: 1px;
        }

        .company-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .separator-line {
            text-align: center;
            margin: 8px 0;
            font-weight: bold;
            color: #333;
            letter-spacing: -1px;
        }

        .thermal-receipt .receipt-info {
            text-align: left;
            margin: 10px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
        }

        .receipt-number {
            font-weight: bold;
            font-family: 'Arial', sans-serif;
        }

        /* عناصر الفاتورة */
        .thermal-receipt .receipt-items {
            margin: 15px 0;
        }

        .items-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 5px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            margin-bottom: 5px;
            padding: 3px 0;
            background: #f0f0f0;
        }

        .items-separator {
            text-align: center;
            margin: 5px 0;
            font-size: 10px;
            color: #666;
        }

        .thermal-receipt .receipt-item {
            margin-bottom: 8px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 5px;
        }

        .item-name {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 2px;
        }

        .item-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 5px;
            font-size: 10px;
            text-align: center;
            color: #555;
        }

        /* مجاميع الفاتورة */
        .receipt-totals {
            margin: 15px 0;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 11px;
        }

        .final-total {
            font-weight: bold;
            font-size: 14px;
            background: #f0f0f0;
            padding: 5px;
            margin: 8px 0;
            border: 1px solid #ddd;
        }

        .payment-info {
            margin-top: 10px;
            padding-top: 8px;
            border-top: 1px dotted #ccc;
        }

        .payment-method {
            font-weight: bold;
        }

        .remaining-balance {
            font-weight: bold;
            color: #2e7d32;
        }

        /* تذييل الفاتورة */
        .thermal-receipt .receipt-footer {
            text-align: center;
            margin-top: 15px;
        }

        .qr-section {
            margin: 10px 0;
        }

        .qr-code {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .qr-text {
            font-size: 9px;
            color: #666;
        }

        .footer-messages {
            margin: 12px 0;
        }

        .thank-message {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .wish-message {
            font-size: 10px;
            color: #666;
            margin-bottom: 5px;
        }

        .return-policy {
            font-size: 8px;
            color: #888;
            font-style: italic;
        }

        .contact-info {
            margin: 10px 0;
            font-size: 8px;
            color: #666;
            line-height: 1.3;
        }

        .receipt-end {
            margin-top: 15px;
            border-top: 1px dotted #ccc;
            padding-top: 8px;
        }

        .cut-line {
            font-size: 10px;
            color: #999;
            margin-bottom: 5px;
        }

        .print-time {
            font-size: 8px;
            color: #aaa;
        }

        /* CSS لنافذة تمرير البطاقة */
        .card-swipe-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(8px);
        }

        .card-swipe-content {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: 3px solid var(--primary-color);
        }

        .swipe-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .swipe-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        .swipe-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .swipe-subtitle {
            font-size: 1rem;
            color: var(--text-muted);
        }

        .swipe-note {
            background: rgba(52, 152, 219, 0.1);
            color: #2980b9;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            margin: 10px 0 0 0;
            border: 1px solid rgba(52, 152, 219, 0.2);
            font-weight: 500;
        }

        /* رسوم متحركة لتمرير البطاقة */
        .swipe-animation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            padding: 20px;
            background: rgba(46, 125, 50, 0.05);
            border-radius: 15px;
            border: 2px dashed var(--primary-color);
        }

        .card-slot {
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .card-reader {
            width: 80px;
            height: 50px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 8px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .reader-light {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }

        .reader-slot {
            position: absolute;
            bottom: 5px;
            left: 10px;
            right: 10px;
            height: 3px;
            background: #1a1a1a;
            border-radius: 2px;
        }

        .swipe-card {
            width: 60px;
            height: 38px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 6px;
            position: relative;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            animation: cardSwipe 3s infinite;
        }

        .card-chip {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 12px;
            height: 10px;
            background: #ffd700;
            border-radius: 2px;
        }

        .card-stripe {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 6px;
            background: #1a1a1a;
            transform: translateY(-50%);
        }

        .swipe-arrow {
            font-size: 1.5rem;
            color: var(--primary-color);
            animation: bounce 2s infinite;
        }

        /* تعليمات الاستخدام */
        .swipe-instructions {
            margin: 25px 0;
        }

        .instruction-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 10px;
            background: var(--input-bg);
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .instruction-item i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .instruction-item span {
            font-size: 0.9rem;
            color: var(--text-color);
        }

        /* قسم الإدخال اليدوي */
        .manual-input-section {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 2px dashed var(--border-color);
        }

        .manual-divider {
            text-align: center;
            margin-bottom: 15px;
            position: relative;
        }

        .manual-divider span {
            background: white;
            padding: 0 15px;
            color: var(--text-muted);
            font-weight: 600;
        }

        .manual-card-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .manual-card-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(46, 125, 50, 0.1);
        }

        .manual-search-btn {
            width: 100%;
            padding: 12px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .manual-search-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        /* ملخص الطلب المسبق */
        .preorder-summary {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 12px;
        }

        .preorder-summary h3 {
            color: var(--text-color);
            margin-bottom: 15px;
            text-align: center;
        }

        .summary-content {
            margin-bottom: 20px;
        }

        .summary-actions {
            display: flex;
            gap: 10px;
        }

        .confirm-preorder-btn {
            flex: 1;
            padding: 12px;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .confirm-preorder-btn:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }

        .cancel-preorder-btn {
            flex: 1;
            padding: 12px;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .cancel-preorder-btn:hover {
            background: #c62828;
            transform: translateY(-2px);
        }

        /* الرسوم المتحركة */
        @keyframes cardSwipe {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(15px); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(0); }
            40% { transform: translateX(-5px); }
            60% { transform: translateX(-3px); }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }

            .cart-section {
                order: -1;
            }

            .search-box {
                width: 200px;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
            }

            .product-card {
                padding: 10px;
            }

            .product-image {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .product-name {
                font-size: 0.8rem;
            }

            .product-price {
                font-size: 0.9rem;
            }

            .cart-items {
                max-height: 200px;
            }

            .prepaid-orders-list {
                max-height: 150px;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 10px 15px;
            }

            .page-title {
                font-size: 1.2rem;
            }

            .user-info {
                display: none;
            }

            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .search-box {
                width: 100%;
            }

            .products-filters {
                justify-content: center;
            }

            .filter-btn {
                font-size: 0.8rem;
                padding: 6px 12px;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            }

            .cart-section {
                padding: 15px;
            }

            .payment-buttons {
                flex-direction: row;
            }

            .test-center-link {
                display: none;
            }
        }

        /* تأثيرات الحركة */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* تمرير مخصص */
        .cart-items::-webkit-scrollbar,
        .prepaid-orders-list::-webkit-scrollbar,
        .products-grid::-webkit-scrollbar {
            width: 6px;
        }

        .cart-items::-webkit-scrollbar-track,
        .prepaid-orders-list::-webkit-scrollbar-track,
        .products-grid::-webkit-scrollbar-track {
            background: var(--bg-light);
            border-radius: 10px;
        }

        .cart-items::-webkit-scrollbar-thumb,
        .prepaid-orders-list::-webkit-scrollbar-thumb,
        .products-grid::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 10px;
        }

        .cart-items::-webkit-scrollbar-thumb:hover,
        .prepaid-orders-list::-webkit-scrollbar-thumb:hover,
        .products-grid::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* أنماط الطلبات المسبقة الجديدة */
        .sidebar-preorder-parent {
            font-size: 0.8rem;
            color: #666;
            margin-top: 3px;
            font-style: italic;
        }

        .prepaid-order-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 0.85rem;
        }

        .prepaid-order-parent {
            color: #666;
            font-style: italic;
        }

        .prepaid-order-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .complete-btn, .cancel-btn {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .complete-btn {
            background: var(--success-color);
            color: white;
        }

        .complete-btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .cancel-btn {
            background: var(--danger-color);
            color: white;
        }

        .cancel-btn:hover {
            background: #d32f2f;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <!-- رابط مركز الاختبارات -->
    <a href="../../tests/index.html" class="test-center-link" style="display: none;">
        <i class="fas fa-flask"></i> مركز الاختبارات
    </a>

    <!-- الهيدر -->
    <header class="header">
        <div class="header-left">
            <!-- لوجو الشركة -->
            <div class="company-logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="company-name">
                    <div class="company-name-en">Smart Canteen</div>
                    <div class="company-name-ar">سمارت مقصف</div>
                </div>
            </div>

            <h1 class="page-title">
                <i class="fas fa-cash-register"></i>
                نقطة البيع
            </h1>
        </div>
        <div class="header-right">
            <div class="user-info">
                <div class="user-name" id="user-name">العامل</div>
                <div class="user-role" id="user-role">كاشير</div>
            </div>

            <!-- زر إظهار/إخفاء الطلبات المسبقة -->
            <button class="toggle-preorders-btn" id="toggle-preorders-btn" onclick="togglePreordersSidebar()">
                <i class="fas fa-clock"></i>
                <span class="toggle-text">الطلبات المسبقة</span>
                <span class="preorders-badge" id="header-preorders-count">0</span>
            </button>

            <!-- زر التحكم في الصوت -->
            <button class="sound-toggle-btn" id="sound-toggle-btn" onclick="toggleNotificationSound()">
                <i class="fas fa-volume-up" id="sound-icon"></i>
                <span class="sound-label" id="sound-label">صوت التنبيه</span>
            </button>

            <!-- زر تبديل الوضع الليلي/النهاري -->
            <button class="theme-toggle-btn" id="theme-toggle" onclick="toggleTheme()">
                <div class="theme-toggle-track">
                    <div class="theme-toggle-thumb">
                        <i class="theme-icon fas fa-sun"></i>
                    </div>
                </div>
                <span class="theme-label">الوضع النهاري</span>
            </button>

            <a href="login.html" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-container">
        <!-- العمود الأيسر - الطلبات المسبقة -->
        <aside class="preorders-sidebar">
            <div class="preorders-section">
                <div class="preorders-header">
                    <div class="preorders-title">
                        <i class="fas fa-clock"></i>
                        الطلبات المسبقة
                        <span class="preorders-count" id="sidebar-preorders-count">0</span>
                    </div>
                    <div class="preorders-controls">
                        <button class="toggle-left-preorders-btn" id="toggle-left-preorders-btn" onclick="toggleLeftPreordersSidebar()" title="إخفاء/إظهار الطلبات المسبقة">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="refresh-preorders-btn" onclick="manualRefreshSidebar()" title="تحديث الطلبات">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preorders-list" id="sidebar-preorders-list">
                    <div class="empty-preorders">
                        <i class="fas fa-clock"></i>
                        <p>لا توجد طلبات مسبقة</p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- قسم المنتجات -->
        <section class="products-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-shopping-basket"></i>
                    المنتجات المتاحة
                </h2>
                <div class="search-box">
                    <input type="text" id="product-search" placeholder="البحث عن منتج...">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <!-- فلاتر المنتجات -->
            <div class="products-filters">
                <button class="filter-btn active" data-category="all">الكل</button>
                <button class="filter-btn" data-category="drinks">مشروبات</button>
                <button class="filter-btn" data-category="snacks">وجبات خفيفة</button>
                <button class="filter-btn" data-category="meals">وجبات</button>
                <button class="filter-btn" data-category="sweets">حلويات</button>
            </div>

            <!-- شبكة المنتجات -->
            <div class="products-grid" id="products-grid">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </section>

        <!-- قسم مسح بطاقة الطالب -->
        <aside class="student-card-section right-sidebar">
            <!-- رأس قسم مسح البطاقة -->
            <div class="card-scanner-header">
                <div class="scanner-title">
                    <i class="fas fa-id-card"></i>
                    مسح بطاقة الطالب
                </div>
                <div class="scanner-status" id="scanner-status">متصل</div>
            </div>

            <!-- منطقة مسح البطاقة -->
            <div class="card-input-area" id="card-input-area">
                <div class="scan-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <h3>امسح بطاقة الطالب أو أدخل البيانات يدوياً</h3>

                <div class="advanced-search-container">
                    <input type="text"
                           class="student-search-input advanced-input"
                           id="student-search-input"
                           placeholder="رقم البطاقة، اسم الطالب، أو رقم نور..."
                           autocomplete="off">

                    <!-- مؤشر البحث المتقدم -->
                    <div class="search-indicator" id="search-indicator">
                        <i class="fas fa-search"></i>
                    </div>

                    <!-- نتائج البحث السريع -->
                    <div class="quick-results" id="quick-results" style="display: none;">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>

                <div class="search-buttons advanced-buttons">
                    <button class="scan-btn advanced-scan-btn" id="scan-btn">
                        <i class="fas fa-search"></i>
                        <span class="btn-text">بحث متقدم</span>
                        <div class="btn-loader" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                    <button class="manual-search-btn advanced-manual-btn" id="manual-search-btn">
                        <i class="fas fa-keyboard"></i>
                        <span class="btn-text">إدخال يدوي</span>
                    </button>
                    <button class="quick-scan-btn" id="quick-scan-btn" onclick="activateQuickScanDirect()">
                        <i class="fas fa-qrcode"></i>
                        <span class="btn-text">مسح سريع</span>
                    </button>
                </div>
            </div>

            <!-- قسم الباركود المتقدم -->
            <div class="barcode-scanner-section">
                <div class="barcode-header">
                    <i class="fas fa-barcode"></i>
                    مسح الباركود
                </div>
                <div class="barcode-input-area">
                    <input type="text"
                           id="barcode-input"
                           class="barcode-input"
                           placeholder="امسح الباركود أو أدخله يدوياً..."
                           autocomplete="off">
                    <div class="barcode-status" id="barcode-status">
                        <i class="fas fa-qrcode"></i>
                        <span>جاهز للمسح</span>
                    </div>
                </div>
                <div class="barcode-instructions">
                    <p><i class="fas fa-info-circle"></i> امسح باركود المنتج لإضافته مباشرة</p>
                    <button class="test-scan-btn" onclick="testQuickScan()" style="margin-top: 8px; padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 4px; font-size: 0.7rem; cursor: pointer;">
                        <i class="fas fa-flask"></i> اختبار المسح
                    </button>
                </div>
            </div>

            <!-- الطلبات مسبقة الدفع -->
            <div class="prepaid-orders">
                <div class="prepaid-header">
                    <div class="prepaid-title">
                        <i class="fas fa-clock"></i>
                        الطلبات مسبقة الدفع
                        <span class="prepaid-count" id="prepaid-count">0</span>
                    </div>
                    <button class="refresh-orders-btn" id="refresh-orders-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>

                <div class="prepaid-orders-list" id="prepaid-orders-list">
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <h3>لا توجد طلبات</h3>
                        <p>لا توجد طلبات مسبقة الدفع حالياً</p>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <!-- نافذة الفاتورة -->
    <div id="receipt-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="receipt" id="receipt">
                <!-- محتوى الفاتورة سيتم إنشاؤه بواسطة JavaScript -->
            </div>
            <div class="modal-actions">
                <button class="payment-btn primary" onclick="printReceipt()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
                <button class="payment-btn secondary" onclick="closeReceiptModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة معلومات الطالب -->
    <div id="student-info-modal" class="student-info-modal" style="display: none;">
        <div class="student-info-content">
            <button class="close-modal-btn" onclick="closeStudentModal()">
                <i class="fas fa-times"></i>
            </button>

            <!-- العمود الأيسر - معلومات الطالب -->
            <div class="student-info-left">
                <!-- معلومات الطالب -->
                <div class="student-info-header">
                    <div class="student-avatar" id="student-avatar">أ</div>
                    <div class="student-name" id="student-name">اسم الطالب</div>
                    <div class="student-id" id="student-id">رقم نور: 123456</div>
                    <div class="student-balance high" id="student-balance">50.00 ريال</div>
                </div>

                <!-- تحذير الحساسية -->
                <div class="allergy-warning" id="allergy-warning" style="display: none;">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="warning-text">تحذير: حساسية غذائية</div>
                    <div class="allergy-list" id="allergy-list">المكسرات، الألبان</div>
                </div>

                <!-- تفاصيل إضافية -->
                <div class="student-details">
                    <div class="detail-item">
                        <span class="detail-label">الصف:</span>
                        <span class="detail-value" id="student-grade">الصف الخامس</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">آخر عملية شراء:</span>
                        <span class="detail-value" id="last-purchase">اليوم 10:30 ص</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">إجمالي المشتريات:</span>
                        <span class="detail-value" id="total-purchases">25 عملية</span>
                    </div>
                </div>

                <!-- ملخص الطلب -->
                <div class="order-summary" id="order-summary" style="display: none;">
                    <div class="summary-row">
                        <span>عدد الأصناف:</span>
                        <span id="order-items-count">0</span>
                    </div>
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="order-subtotal">0.00 ريال</span>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (15%):</span>
                        <span id="order-tax">0.00 ريال</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="order-total">0.00 ريال</span>
                    </div>
                    <div class="summary-row">
                        <span>الرصيد المتبقي:</span>
                        <span id="remaining-balance">0.00 ريال</span>
                    </div>
                </div>

                <!-- أزرار العمليات -->
                <div class="modal-actions">
                    <button class="action-btn primary" id="complete-sale-btn" onclick="completeSale()" disabled>
                        <i class="fas fa-check"></i>
                        إتمام البيع
                        <small style="font-size: 0.7rem; opacity: 0.8;">(Enter)</small>
                    </button>
                    <button class="action-btn secondary" onclick="clearOrder()">
                        <i class="fas fa-trash"></i>
                        مسح الطلب
                        <small style="font-size: 0.7rem; opacity: 0.8;">(Delete)</small>
                    </button>
                    <button class="action-btn secondary" onclick="closeStudentModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                        <small style="font-size: 0.7rem; opacity: 0.8;">(Esc)</small>
                    </button>
                </div>
            </div>

            <!-- العمود الأيمن - البيع السريع والطلبات المسبقة -->
            <div class="student-info-right">
                <!-- قسم الطلبات المسبقة -->
                <div class="preorders-section">
                    <div class="preorders-title">
                        <i class="fas fa-clock"></i>
                        الطلبات المسبقة
                        <span class="preorders-count" id="preorders-count">0</span>
                    </div>

                    <div class="preorders-container" id="preorders-container">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>

                <!-- قسم البيع السريع بالريالات -->
                <div class="quick-sale-section">
                    <div class="quick-sale-title">
                        <i class="fas fa-coins"></i>
                        البيع بالمبلغ (ريال)
                        <span style="font-size: 0.65rem; color: var(--text-muted); margin-right: 8px;">
                            (اختر المبلغ المطلوب)
                        </span>
                    </div>

                    <div class="quick-amounts-grid" id="quick-amounts-grid">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>

                <!-- مؤشر اختصارات لوحة المفاتيح -->
                <div style="text-align: center; margin-top: 6px; padding: 4px; background: var(--bg-light); border-radius: 3px; font-size: 0.6rem; color: var(--text-muted);">
                    <strong>اختصارات:</strong>
                    <span>1-20 للمبالغ</span> • <span>Enter: إتمام</span> • <span>Esc: إلغاء</span>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الطلب مسبق الدفع -->
    <div id="prepaid-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>تأكيد تسليم الطلب</h3>
            <div id="prepaid-order-details">
                <!-- تفاصيل الطلب -->
            </div>
            <div class="modal-actions">
                <button class="payment-btn primary" id="confirm-prepaid-btn">
                    <i class="fas fa-check"></i>
                    تأكيد التسليم
                </button>
                <button class="payment-btn secondary" onclick="closePrepaidModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>



    <!-- نافذة تعديل المنتج -->
    <div id="edit-product-modal" class="edit-product-modal" style="display: none;">
        <div class="edit-product-content">
            <button class="close-edit-btn" onclick="closeEditModal()">
                <i class="fas fa-times"></i>
            </button>

            <div class="edit-header">
                <div class="edit-title">
                    <i class="fas fa-edit"></i>
                    تعديل المنتج
                </div>
                <div class="edit-subtitle">قم بتعديل اسم المنتج وسعره</div>
            </div>

            <form class="edit-form" id="edit-product-form">
                <div class="form-group">
                    <label class="form-label" for="edit-product-name">
                        <i class="fas fa-tag"></i>
                        اسم المنتج
                    </label>
                    <input type="text"
                           id="edit-product-name"
                           class="form-input"
                           placeholder="أدخل اسم المنتج..."
                           required>
                    <div class="error-message" id="name-error">يرجى إدخال اسم صحيح للمنتج</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="edit-product-price">
                        <i class="fas fa-money-bill-wave"></i>
                        السعر (ريال)
                    </label>
                    <input type="number"
                           id="edit-product-price"
                           class="form-input"
                           placeholder="0.00"
                           min="0.01"
                           step="0.01"
                           required>
                    <div class="error-message" id="price-error">يرجى إدخال سعر صحيح أكبر من صفر</div>
                </div>

                <div class="edit-actions">
                    <button type="submit" class="edit-btn-action edit-btn-save">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <button type="button" class="edit-btn-action edit-btn-cancel" onclick="closeEditModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة الفاتورة -->
    <div id="receipt-modal" class="receipt-modal">
        <div class="receipt-container">
            <button class="close-modal-btn" onclick="closeReceiptModal()">×</button>
            <div id="receipt" class="receipt"></div>
            <div class="modal-actions">
                <button class="action-btn primary" onclick="printReceipt()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="action-btn secondary" onclick="closeReceiptModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة تمرير البطاقة للطلب المسبق -->
    <div id="card-swipe-modal" class="card-swipe-modal">
        <div class="card-swipe-content">
            <button class="close-modal-btn" onclick="closeCardSwipeModal()">×</button>

            <div class="swipe-header">
                <div class="swipe-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h2 class="swipe-title">تمرير بطاقة الطالب</h2>
                <p class="swipe-subtitle">للطلب المسبق</p>
            </div>

            <div class="swipe-animation">
                <div class="card-slot">
                    <div class="card-reader">
                        <div class="reader-light"></div>
                        <div class="reader-slot"></div>
                    </div>
                    <div class="swipe-card">
                        <div class="card-chip"></div>
                        <div class="card-stripe"></div>
                    </div>
                </div>
                <div class="swipe-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>

            <div class="swipe-instructions">
                <div class="instruction-item">
                    <i class="fas fa-hand-point-right"></i>
                    <span>مرر البطاقة في القارئ</span>
                </div>
                <div class="instruction-item">
                    <i class="fas fa-clock"></i>
                    <span>انتظر حتى يتم التعرف على البطاقة</span>
                </div>
                <div class="instruction-item">
                    <i class="fas fa-check-circle"></i>
                    <span>سيتم إنشاء الطلب المسبق تلقائياً</span>
                </div>
            </div>

            <div class="manual-input-section">
                <div class="manual-divider">
                    <span>أو</span>
                </div>
                <input type="text"
                       id="manual-card-input"
                       class="manual-card-input"
                       placeholder="أدخل رقم البطاقة أو اسم الطالب يدوياً"
                       onkeypress="handleManualCardInput(event)">
                <button class="manual-search-btn" onclick="searchForPreorder()">
                    <i class="fas fa-search"></i>
                    بحث
                </button>
            </div>

            <div class="preorder-summary" id="preorder-summary" style="display: none;">
                <h3>ملخص الطلب المسبق</h3>
                <div class="summary-content" id="summary-content"></div>
                <div class="summary-actions">
                    <button class="confirm-preorder-btn" onclick="confirmPreorder()">
                        <i class="fas fa-check"></i>
                        تأكيد الطلب المسبق
                    </button>
                    <button class="cancel-preorder-btn" onclick="cancelPreorderCreation()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let products = [];
        let students = [];
        let prepaidOrders = [];
        let currentUser = null;
        let currentStudent = null;
        let currentOrder = [];
        let totalAmount = 0;
        let currentEditingProduct = null;
        let quickAmounts = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];

        // مراقبة تغييرات localStorage
        function watchForProductUpdates() {
            // مراقبة تغييرات localStorage
            window.addEventListener('storage', function(e) {
                if (e.key === 'canteenProducts' && e.newValue) {
                    console.log('تم اكتشاف تحديث في المنتجات من صفحة أخرى');

                    // تحديث المنتجات
                    products = JSON.parse(e.newValue);

                    // تحديث جميع العروض
                    displayProducts();
                    updateMiniProductsDisplay();
                    updateExternalProductsDisplay();

                    showNotification('تم تحديث قائمة المنتجات', 'info');
                }
            });

            // فحص دوري للتحديثات (كل 5 ثوان)
            setInterval(function() {
                const savedProducts = localStorage.getItem('canteenProducts');
                if (savedProducts) {
                    const newProducts = JSON.parse(savedProducts);

                    // مقارنة عدد المنتجات أو آخر تعديل
                    if (newProducts.length !== products.length ||
                        JSON.stringify(newProducts) !== JSON.stringify(products)) {

                        console.log('تم اكتشاف تحديث في المنتجات (فحص دوري)');
                        products = newProducts;

                        // تحديث جميع العروض
                        displayProducts();
                        updateMiniProductsDisplay();
                        updateExternalProductsDisplay();

                        showNotification('تم تحديث قائمة المنتجات', 'info');
                    }
                }
            }, 5000); // كل 5 ثوان
        }

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            initializePOS();
            loadProducts();
            loadStudents();
            loadPrepaidOrders();
            setupEventListeners();
            updateStudentsList();
            updatePrepaidOrdersDisplay();
            updateQuickProductsDisplay();
            watchForProductUpdates(); // بدء مراقبة التحديثات
            initAdvancedSearch(); // تهيئة البحث المتقدم

            // تأكيد تفعيل المسح السريع
            setTimeout(() => {
                const quickScanBtn = document.getElementById('quick-scan-btn');
                if (quickScanBtn) {
                    console.log('✅ زر المسح السريع جاهز');
                } else {
                    console.error('❌ زر المسح السريع غير موجود');
                }
            }, 1000);

            // ===== تهيئة بسيطة ومباشرة =====
            console.log('🚀 تهيئة العمود الأيمن...');

            const rightSidebar = document.querySelector('.student-card-section');
            const leftSidebar = document.querySelector('.preorders-sidebar');
            const mainContainer = document.querySelector('.main-container');
            const toggleBtn = document.getElementById('toggle-preorders-btn');

            if (rightSidebar && leftSidebar && mainContainer && toggleBtn) {
                // تطبيق الحالة الافتراضية
                rightSidebar.classList.add('sidebar-visible');
                leftSidebar.classList.add('sidebar-visible');
                mainContainer.classList.add('layout-full');
                toggleBtn.classList.add('active');

                console.log('✅ تم تهيئة الأعمدة بنجاح');
                console.log('العمود الأيمن:', rightSidebar.className);
                console.log('العمود الأيسر:', leftSidebar.className);
                console.log('الحاوية الرئيسية:', mainContainer.className);
            } else {
                console.error('❌ فشل في العثور على العناصر المطلوبة');
                console.log('العناصر:', {
                    rightSidebar: rightSidebar,
                    leftSidebar: leftSidebar,
                    mainContainer: mainContainer,
                    toggleBtn: toggleBtn
                });
            }
        });

        // تهيئة نقطة البيع
        function initializePOS() {
            // تحميل بيانات المستخدم من sessionStorage أولاً ثم localStorage
            let userData = null;

            try {
                // محاولة تحميل من sessionStorage أولاً
                const sessionUser = sessionStorage.getItem('currentUser');
                if (sessionUser) {
                    userData = JSON.parse(sessionUser);
                    console.log('تم تحميل بيانات المستخدم من sessionStorage:', userData);
                }

                // إذا لم توجد في sessionStorage، جرب localStorage
                if (!userData) {
                    const localUser = localStorage.getItem('currentUser');
                    if (localUser) {
                        userData = JSON.parse(localUser);
                        console.log('تم تحميل بيانات المستخدم من localStorage:', userData);
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم:', error);
            }

            // تعيين بيانات المستخدم
            currentUser = userData || {
                name: 'العامل',
                role: 'كاشير',
                username: 'staff'
            };

            // عرض بيانات المستخدم في الواجهة
            const userNameElement = document.getElementById('user-name');
            const userRoleElement = document.getElementById('user-role');

            if (userNameElement) {
                userNameElement.textContent = currentUser.name || currentUser.username || 'العامل';
            }

            if (userRoleElement) {
                userRoleElement.textContent = currentUser.role === 'staff' ? 'كاشير' : currentUser.role || 'كاشير';
            }

            console.log('تم تهيئة نقطة البيع للمستخدم:', currentUser);
        }

        // تحميل بيانات الطلاب من قاعدة البيانات
        function loadStudents() {
            try {
                // الحصول على قاعدة البيانات
                const db = getDatabase();

                // تحميل جميع الطلاب من قاعدة البيانات
                const allUsers = db.users || [];
                students = allUsers.filter(user => user.role === 'student').map(student => {
                    return {
                        id: student.id,
                        name: student.name,
                        studentId: student.noorId || student.studentId || student.id.toString(),
                        cardId: student.cardId || `CARD${student.id.toString().padStart(3, '0')}`,
                        grade: student.grade || 'غير محدد',
                        balance: student.balance || 0,
                        allergies: student.allergies || [],
                        schoolId: student.schoolId,
                        lastPurchase: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
                        totalPurchases: Math.floor(Math.random() * 50),
                        frequentItems: [1, 2, 3]
                    };
                });

                // إضافة طلاب تجريبيين إضافيين للاختبار
                const additionalStudents = [
                    {
                        id: 1001,
                        name: 'سارة أحمد الخالد',
                        studentId: '2234567890',
                        cardId: 'CARD001',
                        grade: 'الصف الخامس',
                        balance: 75.50,
                        allergies: [],
                        lastPurchase: new Date(Date.now() - 2 * 60 * 60 * 1000),
                        totalPurchases: 25,
                        frequentItems: [1, 2, 3]
                    },
                    {
                        id: 1002,
                        name: 'فاطمة سعد الأحمد',
                        studentId: '3234567890',
                        cardId: 'CARD002',
                        grade: 'الصف الرابع',
                        balance: 45.25,
                        allergies: ['المكسرات', 'الألبان'],
                        lastPurchase: new Date(Date.now() - 30 * 60 * 1000),
                        totalPurchases: 18,
                        frequentItems: [2, 4, 5]
                    },
                    {
                        id: 1003,
                        name: 'خالد عبدالله الزهراني',
                        studentId: '4234567890',
                        cardId: 'CARD003',
                        grade: 'الصف السادس',
                        balance: 12.75,
                        allergies: [],
                        lastPurchase: new Date(Date.now() - 24 * 60 * 60 * 1000),
                        totalPurchases: 42,
                        frequentItems: [1, 3, 6]
                    },
                    {
                        id: 1004,
                        name: 'نورا علي الشهري',
                        studentId: '5234567890',
                        cardId: 'CARD004',
                        grade: 'الصف الثالث',
                        balance: 89.00,
                        allergies: ['الفول السوداني'],
                        lastPurchase: new Date(Date.now() - 4 * 60 * 60 * 1000),
                        totalPurchases: 31,
                        frequentItems: [2, 7, 8]
                    },
                    {
                        id: 1005,
                        name: 'محمد عمر القحطاني',
                        studentId: '6234567890',
                        cardId: 'CARD005',
                        grade: 'الصف الخامس',
                        balance: 5.50,
                        allergies: [],
                        lastPurchase: new Date(Date.now() - 6 * 60 * 60 * 1000),
                        totalPurchases: 15,
                        frequentItems: [1, 2, 5]
                    }
                ];

                // إضافة الطلاب الإضافيين للقائمة
                students = students.concat(additionalStudents);

                console.log('تم تحميل بيانات الطلاب:', students.length, 'طالب');
            } catch (error) {
                console.error('خطأ في تحميل بيانات الطلاب:', error);
                // في حالة الخطأ، استخدام بيانات تجريبية
                students = [
                    {
                        id: 1001,
                        name: 'أحمد محمد العلي',
                        studentId: '1234567890',
                        cardId: 'CARD001',
                        grade: 'الصف الخامس',
                        balance: 75.50,
                        allergies: [],
                        lastPurchase: new Date(Date.now() - 2 * 60 * 60 * 1000),
                        totalPurchases: 25,
                        frequentItems: [1, 2, 3]
                    }
                ];
            }
        }

        // تحميل المنتجات
        function loadProducts() {
            // محاولة تحميل المنتجات من localStorage أولاً
            const savedProducts = localStorage.getItem('canteenProducts');

            if (savedProducts) {
                products = JSON.parse(savedProducts);
                console.log('تم تحميل المنتجات من التخزين المحلي:', products.length, 'منتج');
            } else {
                // بيانات تجريبية للمنتجات (30 منتج مقصف مدرسي حقيقي)
                products = [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, icon: '🥙', isPopular: true },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, icon: '🧀', isPopular: false },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, icon: '🐟', isPopular: false },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, icon: '🍗', isPopular: false },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, icon: '🥗', isPopular: false },
                { id: 6, name: 'بيتزا صغيرة', price: 12.00, category: 'meals', stock: 6, icon: '🍕', isPopular: true },

                // المشروبات
                { id: 7, name: 'عصير برتقال', price: 5.00, category: 'drinks', stock: 20, icon: '🍊', isPopular: true },
                { id: 8, name: 'عصير تفاح', price: 5.50, category: 'drinks', stock: 15, icon: '🍎', isPopular: true },
                { id: 9, name: 'عصير مانجو', price: 6.00, category: 'drinks', stock: 18, icon: '🥭', isPopular: true },
                { id: 10, name: 'ماء', price: 2.00, category: 'drinks', stock: 30, icon: '💧', isPopular: true },
                { id: 11, name: 'مياه غازية', price: 3.00, category: 'drinks', stock: 22, icon: '🥤', isPopular: true },
                { id: 12, name: 'قهوة باردة', price: 7.00, category: 'drinks', stock: 13, icon: '☕', isPopular: false },
                { id: 13, name: 'شاي مثلج', price: 4.50, category: 'drinks', stock: 16, icon: '🧊', isPopular: false },

                // الوجبات الخفيفة والمقرمشات
                { id: 14, name: 'مرامي', price: 2.50, category: 'snacks', stock: 25, icon: '🟡', isPopular: true },
                { id: 15, name: 'قشار', price: 3.00, category: 'snacks', stock: 20, icon: '🟠', isPopular: true },
                { id: 16, name: 'بطل', price: 2.00, category: 'snacks', stock: 30, icon: '🔴', isPopular: true },
                { id: 17, name: 'أفخاذ', price: 4.00, category: 'snacks', stock: 18, icon: '🟤', isPopular: true },
                { id: 18, name: 'شيبس', price: 3.50, category: 'snacks', stock: 25, icon: '🍟', isPopular: true },
                { id: 19, name: 'بسكويت', price: 4.50, category: 'snacks', stock: 18, icon: '🍪', isPopular: true },
                { id: 20, name: 'كرواسون', price: 6.00, category: 'snacks', stock: 14, icon: '🥐', isPopular: true },
                { id: 21, name: 'كعك محلى', price: 5.50, category: 'snacks', stock: 16, icon: '🥨', isPopular: true },
                { id: 22, name: 'مكسرات', price: 8.00, category: 'snacks', stock: 12, icon: '🥜', isPopular: false },
                { id: 23, name: 'فشار', price: 3.50, category: 'snacks', stock: 20, icon: '🍿', isPopular: true },

                // الحلويات
                { id: 24, name: 'كيك شوكولاتة', price: 12.00, category: 'sweets', stock: 8, icon: '🍰', isPopular: false },
                { id: 25, name: 'دونات', price: 6.50, category: 'sweets', stock: 10, icon: '🍩', isPopular: false },
                { id: 26, name: 'مافين', price: 7.50, category: 'sweets', stock: 12, icon: '🧁', isPopular: false },
                { id: 27, name: 'كيك فانيليا', price: 11.00, category: 'sweets', stock: 6, icon: '🎂', isPopular: false },
                { id: 28, name: 'آيس كريم', price: 8.00, category: 'sweets', stock: 9, icon: '🍦', isPopular: true },
                { id: 29, name: 'شوكولاتة', price: 5.00, category: 'sweets', stock: 15, icon: '🍫', isPopular: true },
                { id: 30, name: 'حلوى جيلي', price: 4.00, category: 'sweets', stock: 18, icon: '🍬', isPopular: false }
                ];

                // حفظ البيانات الأولية في localStorage إذا لم تكن موجودة
                localStorage.setItem('canteenProducts', JSON.stringify(products));
                console.log('تم إنشاء وحفظ البيانات الأولية:', products.length, 'منتج');
            }

            displayProducts();
            updateMiniProductsDisplay();
            updateExternalProductsDisplay();
            console.log('تم تحميل بيانات المنتجات:', products.length);
        }

        // عرض المنتجات في الشبكة الرئيسية
        function displayProducts(filteredProducts = null) {
            const productsGrid = document.getElementById('products-grid');
            if (!productsGrid) return;

            const productsToShow = filteredProducts || products;

            if (productsToShow.length === 0) {
                productsGrid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد منتجات</h3>
                        <p>لم يتم العثور على منتجات تطابق البحث</p>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = productsToShow.map(product => `
                <div class="product-card ${product.stock === 0 ? 'out-of-stock' : ''} ${product.isPopular ? 'popular' : ''}"
                     onclick="selectProduct(${product.id})">
                    ${product.isPopular ? '<div class="popular-badge">🔥</div>' : ''}
                    <button class="edit-btn" onclick="event.stopPropagation(); openEditModal(${product.id})" title="تعديل المنتج">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="preorder-btn-card" onclick="event.stopPropagation(); openCardSwipeModal(${product.id})" title="طلب مسبق">
                        <i class="fas fa-clock"></i>
                    </button>
                    <div class="product-image">
                        <span class="product-emoji">${product.icon}</span>
                    </div>
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price.toFixed(2)} ريال</div>
                    <div class="product-stock">متوفر: ${product.stock}</div>
                </div>
            `).join('');
        }

        // تحديث عرض المنتجات المصغرة في الصفحة الخارجية
        function updateExternalProductsDisplay() {
            const externalProductsGrid = document.getElementById('external-products-grid');
            if (!externalProductsGrid) return;

            // عرض أول 8 منتجات فقط
            const displayProducts = products.slice(0, 8);

            externalProductsGrid.innerHTML = displayProducts.map(product => {
                const isOutOfStock = product.stock === 0;
                const classes = ['external-product-btn', isOutOfStock ? 'out-of-stock' : ''].filter(Boolean).join(' ');
                const clickHandler = !isOutOfStock ? `onclick="selectProduct(${product.id})"` : '';
                const title = `${product.name} - ${product.price.toFixed(2)} ريال${isOutOfStock ? ' (غير متوفر)' : ''}`;

                return `
                    <div class="${classes}" ${clickHandler} title="${title}">
                        <div class="external-product-icon">
                            <span class="external-emoji">${product.icon}</span>
                        </div>
                        <div class="external-product-name">${product.name}</div>
                        <div class="external-product-price">${product.price.toFixed(1)}</div>
                        <button class="external-preorder-btn" onclick="event.stopPropagation(); openCardSwipeModal(${product.id})" title="طلب مسبق">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }

        // اختيار منتج من الصفحة الخارجية أو الشبكة الرئيسية
        function selectProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            if (product.stock === 0) {
                showNotification('هذا المنتج غير متوفر حالياً', 'error');
                return;
            }

            // إذا كان هناك طالب محدد، أضف المنتج مباشرة
            if (currentStudent) {
                addProductToOrder(productId);
                return;
            }

            // إذا لم يكن هناك طالب محدد، اعرض رسالة
            showNotification(`تم اختيار ${product.name}. يرجى اختيار طالب لإتمام البيع.`, 'info');

            // تمييز المنتج المختار بصرياً
            highlightSelectedProduct(productId);
        }

        // تمييز المنتج المختار
        function highlightSelectedProduct(productId) {
            // إزالة التمييز من جميع المنتجات
            document.querySelectorAll('.product-card').forEach(card => {
                card.classList.remove('selected');
            });

            // إضافة التمييز للمنتج المختار
            const selectedCard = document.querySelector(`[onclick="selectProduct(${productId})"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        }

        // تحديث عرض المنتجات المصغرة
        function updateMiniProductsDisplay() {
            const productsMiniGrid = document.getElementById('products-mini-grid');
            if (!productsMiniGrid) return;

            productsMiniGrid.innerHTML = products.map(product => {
                const isOutOfStock = product.stock === 0;
                const classes = ['product-mini-btn', isOutOfStock ? 'out-of-stock' : ''].filter(Boolean).join(' ');
                const clickHandler = !isOutOfStock ? `onclick="addProductToOrder(${product.id})"` : '';
                const title = `${product.name} - ${product.price.toFixed(2)} ريال${isOutOfStock ? ' (غير متوفر)' : ''}`;

                return `
                    <div class="${classes}" ${clickHandler} title="${title}">
                        <div class="product-mini-icon">
                            <span class="mini-emoji">${product.icon}</span>
                        </div>
                        <div class="product-mini-name">${product.name}</div>
                        <div class="product-mini-price">${product.price.toFixed(1)}</div>
                    </div>
                `;
            }).join('');
        }

        // إضافة منتج للطلب
        function addProductToOrder(productId) {
            if (!currentStudent) {
                showNotification('يرجى اختيار طالب أولاً', 'warning');
                return;
            }

            const product = products.find(p => p.id === productId);
            if (!product) return;

            if (product.stock === 0) {
                showNotification('هذا المنتج غير متوفر حالياً', 'error');
                return;
            }

            // إضافة سعر المنتج للمجموع الإجمالي
            totalAmount += product.price;

            // إضافة المنتج لقائمة الطلب للعرض
            const orderItem = {
                id: `product_${Date.now()}`,
                name: product.name,
                amount: product.price,
                isProduct: true,
                productId: product.id
            };

            currentOrder.push(orderItem);

            // تقليل المخزون
            product.stock -= 1;

            updateOrderSummary();
            updateMiniProductsDisplay();
            updateExternalProductsDisplay();
            displayProducts(); // تحديث الشبكة الرئيسية
            showNotification(`تم إضافة ${product.name}`, 'success');
        }

        // تحديث ملخص الطلب (للمبالغ فقط)
        function updateOrderSummary() {
            if (!currentStudent || totalAmount === 0) {
                document.getElementById('order-summary').style.display = 'none';
                document.getElementById('complete-sale-btn').disabled = true;
                return;
            }

            const subtotal = totalAmount;
            const tax = subtotal * 0.15;
            const total = subtotal + tax;
            const remainingBalance = currentStudent.balance - total;

            document.getElementById('order-items-count').textContent = currentOrder.length;
            document.getElementById('order-subtotal').textContent = `${subtotal.toFixed(2)} ريال`;
            document.getElementById('order-tax').textContent = `${tax.toFixed(2)} ريال`;
            document.getElementById('order-total').textContent = `${total.toFixed(2)} ريال`;
            document.getElementById('remaining-balance').textContent = `${remainingBalance.toFixed(2)} ريال`;

            document.getElementById('order-summary').style.display = 'block';
            document.getElementById('complete-sale-btn').disabled = remainingBalance < 0;

            if (remainingBalance < 0) {
                document.getElementById('remaining-balance').style.color = 'var(--danger-color)';
                showNotification('رصيد الطالب غير كافي', 'warning');
            } else {
                document.getElementById('remaining-balance').style.color = 'var(--success-color)';
            }
        }







        // عرض قائمة الطلاب السريعة
        function updateStudentsList() {
            const studentsList = document.getElementById('students-list');
            if (!studentsList) return;

            // ترتيب الطلاب حسب آخر عملية شراء
            const sortedStudents = [...students].sort((a, b) => new Date(b.lastPurchase) - new Date(a.lastPurchase));

            studentsList.innerHTML = '';

            sortedStudents.slice(0, 5).forEach(student => {
                const studentItem = document.createElement('div');
                studentItem.className = 'student-quick-item';
                studentItem.onclick = () => selectStudent(student);

                const balanceClass = student.balance > 50 ? 'high' : student.balance > 20 ? 'medium' : student.balance > 0 ? 'low' : 'empty';

                studentItem.innerHTML = `
                    <div class="student-quick-info">
                        <div class="student-quick-name">${student.name}</div>
                        <div class="student-quick-id">رقم نور: ${student.studentId}</div>
                    </div>
                    <div class="student-quick-balance ${balanceClass}">${student.balance.toFixed(2)} ريال</div>
                `;

                studentsList.appendChild(studentItem);
            });
        }

        // البحث عن طالب
        function searchStudent() {
            const searchInput = document.getElementById('student-search-input');
            const searchTerm = searchInput.value.trim();

            if (!searchTerm) {
                showNotification('يرجى إدخال رقم البطاقة أو رقم نور أو اسم الطالب', 'warning');
                return;
            }

            console.log('البحث عن الطالب في النظام الرئيسي:', searchTerm);
            console.log('قائمة الطلاب المتاحة:', students);

            // البحث المحسن عن الطالب
            let foundStudent = null;
            let searchResults = [];

            // جمع جميع النتائج المحتملة أولاً
            students.forEach(student => {
                let matchScore = 0;
                let matchType = '';

                // فحص التطابق الدقيق
                if (student.studentId && student.studentId.toString() === searchTerm) {
                    matchScore = 100;
                    matchType = 'رقم نور دقيق';
                } else if (student.name && student.name.toLowerCase() === searchTerm.toLowerCase()) {
                    matchScore = 95;
                    matchType = 'اسم دقيق';
                } else if (student.cardId && student.cardId.toLowerCase() === searchTerm.toLowerCase()) {
                    matchScore = 90;
                    matchType = 'رقم بطاقة دقيق';
                }
                // فحص التطابق الجزئي
                else if (student.name && student.name.toLowerCase().includes(searchTerm.toLowerCase())) {
                    matchScore = 70;
                    matchType = 'اسم جزئي';
                } else if (student.studentId && student.studentId.toString().includes(searchTerm)) {
                    matchScore = 60;
                    matchType = 'رقم نور جزئي';
                } else if (student.cardId && student.cardId.toLowerCase().includes(searchTerm.toLowerCase())) {
                    matchScore = 50;
                    matchType = 'رقم بطاقة جزئي';
                }

                if (matchScore > 0) {
                    searchResults.push({
                        student: student,
                        score: matchScore,
                        type: matchType
                    });
                }
            });

            // ترتيب النتائج حسب النقاط (الأعلى أولاً)
            searchResults.sort((a, b) => b.score - a.score);

            console.log('نتائج البحث في النظام الرئيسي:', searchResults);

            // اختيار أفضل نتيجة
            if (searchResults.length > 0) {
                foundStudent = searchResults[0].student;
                console.log(`تم العثور على الطالب بـ ${searchResults[0].type}:`, foundStudent);
            }

            // البحث في قاعدة البيانات إذا لم يتم العثور على الطالب محلياً
            if (!foundStudent) {
                try {
                    const db = getDatabase();
                    const allUsers = db.users || [];

                    console.log('البحث في قاعدة البيانات عن:', searchTerm);

                    // البحث الدقيق في قاعدة البيانات أولاً
                    let dbStudent = allUsers.find(user => {
                        if (user.role !== 'student') return false;

                        const noorIdExact = user.noorId && user.noorId.toString() === searchTerm;
                        const nameExact = user.name && user.name.toLowerCase() === searchTerm.toLowerCase();
                        const usernameExact = user.username && user.username.toLowerCase() === searchTerm.toLowerCase();

                        return noorIdExact || nameExact || usernameExact;
                    });

                    // إذا لم يجد تطابق دقيق، البحث الجزئي
                    if (!dbStudent) {
                        dbStudent = allUsers.find(user => {
                            if (user.role !== 'student') return false;

                            const noorIdPartial = user.noorId && user.noorId.toString().includes(searchTerm);
                            const namePartial = user.name && user.name.toLowerCase().includes(searchTerm.toLowerCase());
                            const usernamePartial = user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase());

                            return noorIdPartial || namePartial || usernamePartial;
                        });
                    }

                    if (dbStudent) {
                        console.log('تم العثور على الطالب في قاعدة البيانات:', dbStudent);

                        // تحويل بيانات الطالب من قاعدة البيانات
                        foundStudent = {
                            id: dbStudent.id,
                            name: dbStudent.name,
                            studentId: dbStudent.noorId || dbStudent.id.toString(),
                            cardId: dbStudent.cardId || `CARD${dbStudent.id.toString().padStart(3, '0')}`,
                            grade: dbStudent.grade || 'غير محدد',
                            balance: dbStudent.balance || 0,
                            allergies: dbStudent.allergies || [],
                            schoolId: dbStudent.schoolId
                        };

                        // إضافة الطالب إلى القائمة المحلية
                        students.push(foundStudent);
                        console.log('تم إضافة الطالب للقائمة المحلية');
                    }
                } catch (error) {
                    console.error('خطأ في البحث في قاعدة البيانات:', error);
                }
            }

            if (foundStudent) {
                console.log('تم العثور على الطالب النهائي:', foundStudent);
                selectStudent(foundStudent);
                searchInput.value = '';

                // عرض رسالة نجاح مع نوع التطابق
                const matchInfo = searchResults.length > 0 ? ` (${searchResults[0].type})` : '';
                showNotification(`مرحباً ${foundStudent.name}${matchInfo}`, 'success');

                // إذا كان هناك أكثر من نتيجة، اعرض تحذير
                if (searchResults.length > 1) {
                    console.log('تم العثور على نتائج متعددة، تم اختيار الأفضل');
                    setTimeout(() => {
                        showNotification(`تم العثور على ${searchResults.length} نتائج، تم اختيار الأنسب`, 'info');
                    }, 1000);
                }
            } else {
                console.log('لم يتم العثور على الطالب نهائياً');
                showNotification(`لم يتم العثور على طالب باسم أو رقم: "${searchTerm}"`, 'error');

                // اقتراح إنشاء حساب جديد
                setTimeout(() => {
                    if (confirm(`لم يتم العثور على طالب باسم "${searchTerm}". هل تريد إنشاء حساب جديد؟`)) {
                        showCreateStudentModal(searchTerm);
                    }
                }, 1500);
            }
        }

        // اختيار طالب
        function selectStudent(student) {
            currentStudent = student;
            showStudentModal(student);
        }

        // عرض نافذة معلومات الطالب
        function showStudentModal(student) {
            // تحديث معلومات الطالب
            document.getElementById('student-avatar').textContent = student.name.charAt(0);
            document.getElementById('student-name').textContent = student.name;
            document.getElementById('student-id').textContent = `رقم نور: ${student.studentId}`;

            // تحديث الرصيد مع الفئة المناسبة
            const balanceElement = document.getElementById('student-balance');
            balanceElement.textContent = `${student.balance.toFixed(2)} ريال`;
            balanceElement.className = 'student-balance ' + (student.balance > 50 ? 'high' : student.balance > 20 ? 'medium' : 'low');

            // تحديث التفاصيل
            document.getElementById('student-grade').textContent = student.grade;
            document.getElementById('last-purchase').textContent = getTimeAgo(student.lastPurchase);
            document.getElementById('total-purchases').textContent = `${student.totalPurchases} عملية`;

            // عرض تحذير الحساسية إذا وجد
            const allergyWarning = document.getElementById('allergy-warning');
            if (student.allergies && student.allergies.length > 0) {
                document.getElementById('allergy-list').textContent = student.allergies.join(', ');
                allergyWarning.style.display = 'block';
            } else {
                allergyWarning.style.display = 'none';
            }

            // تحديث الطلبات المسبقة والبيع السريع
            updatePreordersDisplay(student.id);
            updateQuickAmountsDisplay();

            // إظهار النافذة
            document.getElementById('student-info-modal').style.display = 'flex';
        }

        // تحديث عرض أزرار البيع بالمبلغ
        function updateQuickAmountsDisplay() {
            const quickAmountsGrid = document.getElementById('quick-amounts-grid');
            if (!quickAmountsGrid) return;

            // مبالغ البيع السريع (بالريال)
            const quickAmounts = [
                1, 2, 3, 4, 5,
                10, 15, 20, 25, 30,
                50, 75, 100, 150, 200,
                250, 300, 500, 750, 1000
            ];

            quickAmountsGrid.innerHTML = quickAmounts.map((amount, index) => {
                const wasRecentlyClicked = recentlyClickedAmounts.includes(amount);

                return `
                    <div class="quick-amount-btn" onclick="addAmountToOrder(${amount})" title="إضافة ${amount} ريال">
                        <div class="amount-value">${amount}</div>
                        <div class="amount-label">ريال</div>
                        ${wasRecentlyClicked ? '<div class="amount-indicator">✓</div>' : ''}
                    </div>
                `;
            }).join('');
        }

        // متغير لتتبع المبالغ المضافة مؤخراً
        let recentlyClickedAmounts = [];

        // إضافة مبلغ للطلب
        function addAmountToOrder(amount) {
            if (!currentStudent) {
                showNotification('يرجى اختيار طالب أولاً', 'warning');
                return;
            }

            // إضافة المبلغ للمجموع الإجمالي
            totalAmount += amount;

            // إضافة المبلغ لقائمة الطلب للعرض
            const orderItem = {
                id: `amount_${Date.now()}`,
                name: `${amount} ريال`,
                amount: amount
            };

            currentOrder.push(orderItem);

            // إضافة المبلغ للقائمة المؤقتة للمؤشر البصري
            if (!recentlyClickedAmounts.includes(amount)) {
                recentlyClickedAmounts.push(amount);
                setTimeout(() => {
                    recentlyClickedAmounts = recentlyClickedAmounts.filter(a => a !== amount);
                    updateQuickAmountsDisplay();
                }, 1500);
            }

            updateOrderSummary();
            updateQuickAmountsDisplay();
            showNotification(`تم إضافة ${amount} ريال`, 'success');
        }

        // تحديث عرض الطلبات المسبقة
        function updatePreordersDisplay(studentId) {
            const preordersContainer = document.getElementById('preorders-container');
            const preordersCount = document.getElementById('preorders-count');

            if (!preordersContainer || !preordersCount) return;

            // الحصول على الطلبات المسبقة للطالب (بيانات تجريبية)
            const studentPreorders = [
                {
                    id: 1,
                    studentId: studentId,
                    productId: 1,
                    quantity: 2,
                    status: 'pending',
                    createdAt: new Date(Date.now() - 30 * 60 * 1000) // قبل 30 دقيقة
                },
                {
                    id: 2,
                    studentId: studentId,
                    productId: 3,
                    quantity: 1,
                    status: 'pending',
                    createdAt: new Date(Date.now() - 15 * 60 * 1000) // قبل 15 دقيقة
                }
            ].filter(order => Math.random() > 0.5); // عشوائي للتجربة

            // تحديث العداد
            preordersCount.textContent = studentPreorders.length;

            if (studentPreorders.length === 0) {
                preordersContainer.innerHTML = `
                    <div class="no-preorders">
                        <i class="fas fa-clock"></i>
                        لا توجد طلبات مسبقة
                    </div>
                `;
                return;
            }

            // عرض الطلبات المسبقة
            preordersContainer.innerHTML = studentPreorders.map(order => {
                const product = canteenProducts.find(p => p.id === order.productId);
                const orderTime = new Date(order.createdAt).toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                return `
                    <div class="preorder-item" onclick="processPreorder(${order.id})">
                        <div class="preorder-icon">
                            ${product ? product.icon : '🍽️'}
                        </div>
                        <div class="preorder-details">
                            <div class="preorder-product">${product ? product.name : 'منتج غير معروف'}</div>
                            <div class="preorder-info">
                                <span>الكمية: ${order.quantity}</span>
                                <span>الوقت: ${orderTime}</span>
                            </div>
                        </div>
                        <div class="preorder-actions">
                            <button class="preorder-btn" onclick="event.stopPropagation(); processPreorder(${order.id})" title="تنفيذ الطلب">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="preorder-btn cancel" onclick="event.stopPropagation(); cancelPreorder(${order.id})" title="إلغاء الطلب">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // تنفيذ طلب مسبق
        function processPreorder(orderId) {
            showNotification('تم تنفيذ الطلب المسبق', 'success');
            // هنا يمكن إضافة المنتج للطلب الحالي
            updatePreordersDisplay(currentStudent.id);
        }

        // إلغاء طلب مسبق
        function cancelPreorder(orderId) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                showNotification('تم إلغاء الطلب المسبق', 'info');
                updatePreordersDisplay(currentStudent.id);
            }
        }

        // إضافة مبلغ بالرقم (اختصارات لوحة المفاتيح)
        function addAmountByNumber(number) {
            const quickAmounts = [
                1, 2, 3, 4, 5,
                10, 15, 20, 25, 30,
                50, 75, 100, 150, 200,
                250, 300, 500, 750, 1000
            ];

            if (number >= 1 && number <= 20) {
                const amount = quickAmounts[number - 1];
                addAmountToOrder(amount);
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // البحث في المنتجات
            const searchInput = document.getElementById('product-search');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const filteredProducts = products.filter(product =>
                        product.name.toLowerCase().includes(searchTerm)
                    );
                    displayProducts(filteredProducts);
                });
            }

            // فلاتر الفئات
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    filterBtns.forEach(b => b.classList.remove('active'));
                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    const category = this.dataset.category;
                    if (category === 'all') {
                        displayProducts();
                    } else {
                        const filteredProducts = products.filter(product =>
                            product.category === category
                        );
                        displayProducts(filteredProducts);
                    }
                });
            });

            // البحث عن الطلاب
            const studentSearchInput = document.getElementById('student-search-input');
            if (studentSearchInput) {
                studentSearchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchStudent();
                    }
                });
            }

            const scanBtn = document.getElementById('scan-btn');
            if (scanBtn) {
                scanBtn.addEventListener('click', searchStudent);
            }

            const manualSearchBtn = document.getElementById('manual-search-btn');
            if (manualSearchBtn) {
                manualSearchBtn.addEventListener('click', searchStudent);
            }

            // تحديث الطلبات مسبقة الدفع
            const refreshBtn = document.getElementById('refresh-orders-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', loadPrepaidOrders);
            }

            // دعم لوحة المفاتيح للأرقام (1-20)
            document.addEventListener('keydown', function(e) {
                // التأكد من أن نافذة الطالب مفتوحة
                const studentModal = document.getElementById('student-info-modal');
                if (!studentModal || studentModal.style.display === 'none') {
                    return;
                }

                // التأكد من عدم التركيز على حقل إدخال
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return;
                }

                // معالجة الأرقام 1-9
                if (e.key >= '1' && e.key <= '9') {
                    e.preventDefault();
                    const number = parseInt(e.key);
                    addAmountByNumber(number);
                }
                // معالجة الرقم 0 (يمثل 10)
                else if (e.key === '0') {
                    e.preventDefault();
                    addAmountByNumber(10);
                }
                // معالجة الأرقام 11-20 باستخدام Shift + الرقم
                else if (e.shiftKey && e.key >= '1' && e.key <= '9') {
                    e.preventDefault();
                    const number = parseInt(e.key) + 10; // 11-19
                    if (number <= 20) {
                        addAmountByNumber(number);
                    }
                }
                // معالجة الرقم 20 باستخدام Shift + 0
                else if (e.shiftKey && e.key === '0') {
                    e.preventDefault();
                    addAmountByNumber(20);
                }
                // مسح الطلب باستخدام Delete أو Backspace
                else if (e.key === 'Delete' || e.key === 'Backspace') {
                    e.preventDefault();
                    clearOrder();
                }
                // إتمام البيع باستخدام Enter
                else if (e.key === 'Enter') {
                    e.preventDefault();
                    const completeSaleBtn = document.getElementById('complete-sale-btn');
                    if (completeSaleBtn && !completeSaleBtn.disabled) {
                        completeSale();
                    }
                }
                // إغلاق النافذة باستخدام Escape
                else if (e.key === 'Escape') {
                    e.preventDefault();
                    closeStudentModal();
                }
            });

            // إعداد مستمعي نافذة التعديل
            setupEditModalListeners();
        }

        // إتمام البيع
        function completeSale() {
            if (!currentStudent || totalAmount === 0) {
                showNotification('لا يوجد مبلغ لإتمام البيع', 'warning');
                return;
            }

            const subtotal = totalAmount;
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            if (currentStudent.balance < total) {
                showNotification('رصيد الطالب غير كافي', 'error');
                return;
            }

            // إنشاء المعاملة
            const transaction = {
                id: Date.now(),
                studentId: currentStudent.id,
                studentName: currentStudent.name,
                items: [...currentOrder],
                subtotal: subtotal,
                tax: tax,
                total: total,
                timestamp: new Date(),
                cashier: currentUser.name,
                paymentMethod: 'بطاقة طالب',
                studentBalance: currentStudent.balance - total
            };

            // تحديث رصيد الطالب
            currentStudent.balance -= total;
            currentStudent.lastPurchase = new Date();
            currentStudent.totalPurchases += 1;

            // حفظ المعاملة
            saveTransaction(transaction);

            // عرض الفاتورة
            showReceipt(transaction);

            // مسح الطلب الحالي
            clearOrder();

            // إغلاق نافذة الطالب
            closeStudentModal();

            // تحديث العروض
            updateStudentsList();

            showNotification('تم إتمام البيع بنجاح', 'success');
        }

        // مسح الطلب الحالي
        function clearOrder() {
            // إعادة المخزون للمنتجات
            currentOrder.forEach(orderItem => {
                if (orderItem.isProduct) {
                    const product = products.find(p => p.id === orderItem.productId);
                    if (product) {
                        product.stock += 1;
                    }
                }
            });

            currentOrder = [];
            totalAmount = 0;
            recentlyClickedAmounts = [];
            updateOrderSummary();
            updateQuickAmountsDisplay();
            updateMiniProductsDisplay();
            updateExternalProductsDisplay();
            displayProducts(); // تحديث الشبكة الرئيسية
            showNotification('تم مسح الطلب', 'info');
        }

        // إغلاق نافذة معلومات الطالب
        function closeStudentModal() {
            document.getElementById('student-info-modal').style.display = 'none';
            currentStudent = null;
            clearOrder();
        }

        // إتمام المعاملة
        function completeTransaction(paymentMethod, amountPaid, change) {
            const transaction = {
                id: Date.now(),
                items: [...cart],
                subtotal: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                tax: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 0.15,
                total: calculateTotal(),
                paymentMethod: paymentMethod,
                amountPaid: amountPaid,
                change: change,
                timestamp: new Date(),
                cashier: currentUser.name
            };

            // تحديث المخزون
            cart.forEach(cartItem => {
                const product = products.find(p => p.id === cartItem.id);
                if (product) {
                    product.stock -= cartItem.quantity;
                }
            });

            // حفظ المعاملة
            saveTransaction(transaction);

            // عرض الفاتورة
            showReceipt(transaction);

            // مسح السلة
            cart = [];
            updateCartDisplay();
            saveCart();
            displayProducts(); // تحديث عرض المنتجات لإظهار المخزون الجديد

            showNotification('تم إتمام المعاملة بنجاح', 'success');
        }

        // حساب الإجمالي
        function calculateTotal() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.15;
            return subtotal + tax;
        }

        // حفظ المعاملة
        function saveTransaction(transaction) {
            const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
            transactions.push(transaction);
            localStorage.setItem('transactions', JSON.stringify(transactions));
        }

        // عرض الفاتورة الحرارية المتقدمة
        function showReceipt(transaction) {
            const receiptModal = document.getElementById('receipt-modal');
            const receipt = document.getElementById('receipt');

            const currentDate = new Date();
            const hijriDate = getHijriDate(currentDate);
            const receiptNumber = String(transaction.id).padStart(8, '0');

            receipt.innerHTML = `
                <div class="thermal-receipt">
                    <!-- رأس الفاتورة -->
                    <div class="receipt-header">
                        <div class="logo-section">
                            <div class="receipt-logo">🍽️</div>
                            <div class="company-name">سمارت مقصف</div>
                            <div class="company-subtitle">Smart Canteen</div>
                        </div>

                        <div class="separator-line">═══════════════════════════════</div>

                        <div class="receipt-info">
                            <div class="info-row">
                                <span>رقم الفاتورة:</span>
                                <span class="receipt-number">#${receiptNumber}</span>
                            </div>
                            <div class="info-row">
                                <span>التاريخ الميلادي:</span>
                                <span>${currentDate.toLocaleDateString('ar-SA')}</span>
                            </div>
                            <div class="info-row">
                                <span>التاريخ الهجري:</span>
                                <span>${hijriDate}</span>
                            </div>
                            <div class="info-row">
                                <span>الوقت:</span>
                                <span>${currentDate.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}</span>
                            </div>
                            <div class="info-row">
                                <span>الكاشير:</span>
                                <span>${transaction.cashier}</span>
                            </div>
                            ${transaction.studentName ? `
                                <div class="info-row">
                                    <span>الطالب:</span>
                                    <span>${transaction.studentName}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="separator-line">═══════════════════════════════</div>

                    <!-- عناصر الفاتورة -->
                    <div class="receipt-items">
                        <div class="items-header">
                            <span>الصنف</span>
                            <span>الكمية</span>
                            <span>السعر</span>
                            <span>المجموع</span>
                        </div>
                        <div class="items-separator">───────────────────────────────</div>

                        ${transaction.items.map(item => {
                            const itemTotal = item.quantity ? (item.price * item.quantity) : item.amount;
                            const quantity = item.quantity || 1;
                            return `
                                <div class="receipt-item">
                                    <div class="item-name">${item.name}</div>
                                    <div class="item-details">
                                        <span class="item-qty">${quantity}</span>
                                        <span class="item-price">${item.price ? item.price.toFixed(2) : item.amount.toFixed(2)}</span>
                                        <span class="item-total">${itemTotal.toFixed(2)}</span>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>

                    <div class="separator-line">═══════════════════════════════</div>

                    <!-- مجاميع الفاتورة -->
                    <div class="receipt-totals">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span>${transaction.subtotal.toFixed(2)} ريال</span>
                        </div>
                        <div class="total-row">
                            <span>الضريبة المضافة (15%):</span>
                            <span>${transaction.tax.toFixed(2)} ريال</span>
                        </div>
                        <div class="separator-line">───────────────────────────────</div>
                        <div class="total-row final-total">
                            <span>الإجمالي النهائي:</span>
                            <span>${transaction.total.toFixed(2)} ريال</span>
                        </div>

                        <div class="payment-info">
                            <div class="total-row">
                                <span>طريقة الدفع:</span>
                                <span class="payment-method">${getPaymentMethodArabic(transaction.paymentMethod || 'بطاقة طالب')}</span>
                            </div>
                            ${transaction.studentBalance !== undefined ? `
                                <div class="total-row">
                                    <span>الرصيد السابق:</span>
                                    <span>${(transaction.studentBalance + transaction.total).toFixed(2)} ريال</span>
                                </div>
                                <div class="total-row">
                                    <span>الرصيد الحالي:</span>
                                    <span class="remaining-balance">${transaction.studentBalance.toFixed(2)} ريال</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="separator-line">═══════════════════════════════</div>

                    <!-- تذييل الفاتورة -->
                    <div class="receipt-footer">
                        <div class="qr-section">
                            <div class="qr-code">📱</div>
                            <div class="qr-text">امسح الكود للتقييم</div>
                        </div>

                        <div class="footer-messages">
                            <div class="thank-message">شكراً لزيارتكم 🌟</div>
                            <div class="wish-message">نتمنى لكم يوماً سعيداً</div>
                            <div class="return-policy">سياسة الإرجاع: خلال 24 ساعة</div>
                        </div>

                        <div class="contact-info">
                            <div>📞 الدعم الفني: 920000000</div>
                            <div>🌐 www.smartcanteen.sa</div>
                            <div>📧 <EMAIL></div>
                        </div>

                        <div class="receipt-end">
                            <div class="cut-line">✂ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ✂</div>
                            <div class="print-time">طُبعت في: ${currentDate.toLocaleString('ar-SA')}</div>
                        </div>
                    </div>
                </div>
            `;

            receiptModal.style.display = 'flex';
        }

        // دالة للحصول على التاريخ الهجري
        function getHijriDate(date) {
            // تحويل تقريبي للتاريخ الهجري
            const hijriYear = Math.floor((date.getFullYear() - 622) * 1.030684);
            const hijriMonth = date.getMonth() + 1;
            const hijriDay = date.getDate();

            const hijriMonths = [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ];

            return `${hijriDay} ${hijriMonths[hijriMonth - 1]} ${hijriYear + 622}هـ`;
        }

        // دالة لترجمة طريقة الدفع
        function getPaymentMethodArabic(method) {
            const methods = {
                'cash': 'نقداً',
                'card': 'بطاقة ائتمان',
                'student_card': 'بطاقة طالب',
                'بطاقة طالب': 'بطاقة طالب',
                'نقداً': 'نقداً'
            };
            return methods[method] || method;
        }

        // طباعة الفاتورة
        function printReceipt() {
            const receiptContent = document.getElementById('receipt').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>فاتورة</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            .receipt { max-width: 300px; margin: 0 auto; }
                            .receipt-item { display: flex; justify-content: space-between; margin: 5px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="receipt">${receiptContent}</div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // إغلاق نافذة الفاتورة
        function closeReceiptModal() {
            document.getElementById('receipt-modal').style.display = 'none';
        }

        // متغيرات للطلب المسبق
        let currentPreorderProduct = null;
        let currentPreorderStudent = null;

        // فتح نافذة تمرير البطاقة للطلب المسبق
        function openCardSwipeModal(productId) {
            currentPreorderProduct = products.find(p => p.id === productId);
            if (!currentPreorderProduct) return;

            document.getElementById('card-swipe-modal').style.display = 'flex';
            document.getElementById('manual-card-input').value = '';
            document.getElementById('preorder-summary').style.display = 'none';

            // تشغيل محاكاة تمرير البطاقة
            simulateCardSwipe();
        }

        // إغلاق نافذة تمرير البطاقة
        function closeCardSwipeModal() {
            document.getElementById('card-swipe-modal').style.display = 'none';
            currentPreorderProduct = null;
            currentPreorderStudent = null;
        }

        // محاكاة تمرير البطاقة (للعرض فقط - سيتم استخدام البحث اليدوي للبحث الفعلي)
        function simulateCardSwipe() {
            // تحديث حالة القارئ
            const readerLight = document.querySelector('.reader-light');
            if (readerLight) {
                readerLight.style.background = '#f39c12'; // أصفر للانتظار
            }

            // عرض رسالة توضيحية
            showNotification('يرجى استخدام البحث اليدوي أدناه لإدخال اسم أو رقم الطالب', 'info');

            // تغيير لون القارئ إلى الأزرق للمعلومات
            setTimeout(() => {
                if (readerLight) {
                    readerLight.style.background = '#3498db'; // أزرق للمعلومات
                }
            }, 1000);
        }

        // معالجة تمرير البطاقة
        function processCardSwipe(student) {
            currentPreorderStudent = student;
            showPreorderSummary();
        }

        // معالجة الإدخال اليدوي
        function handleManualCardInput(event) {
            if (event.key === 'Enter') {
                searchForPreorder();
            }
        }

        // البحث عن الطالب للطلب المسبق
        function searchForPreorder() {
            const searchInput = document.getElementById('manual-card-input');
            const searchTerm = searchInput.value.trim();

            if (!searchTerm) {
                showNotification('يرجى إدخال رقم البطاقة أو رقم نور أو اسم الطالب', 'warning');
                return;
            }

            console.log('البحث عن الطالب للطلب المسبق:', searchTerm);

            // تحديث حالة القارئ للبحث
            const readerLight = document.querySelector('.reader-light');
            if (readerLight) {
                readerLight.style.background = '#f39c12'; // أصفر للبحث
            }

            // استخدام نفس منطق البحث المتقدم
            const results = searchStudentsAdvanced(searchTerm);

            // إذا لم توجد نتائج، البحث في قاعدة البيانات
            const finalResults = results.length > 0 ? results : searchInDatabase(searchTerm);

            if (finalResults.length > 0) {
                const foundStudent = finalResults[0];
                console.log('تم العثور على الطالب للطلب المسبق:', foundStudent);

                // تحديث حالة القارئ للنجاح
                if (readerLight) {
                    readerLight.style.background = '#27ae60'; // أخضر للنجاح
                }

                currentPreorderStudent = foundStudent;
                showPreorderSummary();

                // عرض رسالة نجاح مع نوع التطابق
                const matchInfo = foundStudent.matchType ? ` (${foundStudent.matchType})` : '';
                showNotification(`تم العثور على الطالب: ${foundStudent.name}${matchInfo}`, 'success');

                // إذا كان هناك أكثر من نتيجة، اعرض تحذير
                if (finalResults.length > 1) {
                    setTimeout(() => {
                        showNotification(`تم العثور على ${finalResults.length} نتائج، تم اختيار الأنسب`, 'info');
                    }, 1000);
                }

                // مسح حقل البحث
                searchInput.value = '';
            } else {
                console.log('لم يتم العثور على الطالب نهائياً');

                // تحديث حالة القارئ للخطأ
                if (readerLight) {
                    readerLight.style.background = '#e74c3c'; // أحمر للخطأ
                }

                showNotification(`لم يتم العثور على طالب باسم أو رقم: "${searchTerm}"`, 'error');

                // اقتراح إنشاء حساب جديد
                setTimeout(() => {
                    if (confirm(`لم يتم العثور على طالب باسم "${searchTerm}". هل تريد إنشاء حساب جديد؟`)) {
                        showCreateStudentModal(searchTerm);
                    }
                }, 1500);
            }
        }

        // البحث المباشر للطلبات المسبقة (أثناء الكتابة)
        function searchForPreorderLive(query) {
            if (!query || query.length < 3) return;

            console.log('🔍 بحث مباشر للطلبات المسبقة:', query);

            const results = searchStudentsAdvanced(query);

            if (results.length > 0) {
                // عرض اقتراحات سريعة
                showPreorderSuggestions(results.slice(0, 5), query);
            }
        }

        // عرض اقتراحات الطلبات المسبقة
        function showPreorderSuggestions(results, query) {
            // إزالة الاقتراحات السابقة
            const existingSuggestions = document.getElementById('preorder-suggestions');
            if (existingSuggestions) {
                existingSuggestions.remove();
            }

            const manualCardInput = document.getElementById('manual-card-input');
            if (!manualCardInput) return;

            const suggestions = document.createElement('div');
            suggestions.id = 'preorder-suggestions';
            suggestions.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                max-height: 200px;
                overflow-y: auto;
            `;

            suggestions.innerHTML = results.map(student => `
                <div class="suggestion-item" onclick="selectPreorderStudent(${student.id})" style="
                    padding: 12px;
                    border-bottom: 1px solid #eee;
                    cursor: pointer;
                    transition: background 0.2s;
                " onmouseover="this.style.background='#f8f9fa'"
                   onmouseout="this.style.background='white'">
                    <div style="font-weight: 600; color: #333;">${student.name}</div>
                    <div style="font-size: 0.85rem; color: #666;">
                        رقم نور: ${student.studentId} | الصف: ${student.grade}
                    </div>
                    <div style="font-size: 0.8rem; color: #4caf50;">
                        الرصيد: ${student.balance.toFixed(2)} ريال
                    </div>
                </div>
            `).join('');

            // إضافة الاقتراحات بعد حقل الإدخال
            manualCardInput.parentNode.style.position = 'relative';
            manualCardInput.parentNode.appendChild(suggestions);

            // إخفاء الاقتراحات عند النقر خارجها
            setTimeout(() => {
                document.addEventListener('click', function hideOnClickOutside(e) {
                    if (!e.target.closest('#preorder-suggestions') &&
                        !e.target.closest('#manual-card-input')) {
                        suggestions.remove();
                        document.removeEventListener('click', hideOnClickOutside);
                    }
                });
            }, 100);
        }

        // اختيار طالب من الاقتراحات
        function selectPreorderStudent(studentId) {
            const student = students.find(s => s.id === studentId);
            if (student) {
                currentPreorderStudent = student;
                showPreorderSummary();

                // إزالة الاقتراحات
                const suggestions = document.getElementById('preorder-suggestions');
                if (suggestions) suggestions.remove();

                // مسح حقل البحث
                const manualCardInput = document.getElementById('manual-card-input');
                if (manualCardInput) manualCardInput.value = '';

                showNotification(`تم اختيار الطالب: ${student.name}`, 'success');
            }
        }

        // عرض ملخص الطلب المسبق
        function showPreorderSummary() {
            if (!currentPreorderProduct || !currentPreorderStudent) return;

            const summaryContent = document.getElementById('summary-content');
            const preorderSummary = document.getElementById('preorder-summary');

            summaryContent.innerHTML = `
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>الطالب:</strong>
                    <span>${currentPreorderStudent.name}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>رقم نور:</strong>
                    <span>${currentPreorderStudent.studentId}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>الصف:</strong>
                    <span>${currentPreorderStudent.grade}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>الرصيد:</strong>
                    <span style="color: ${currentPreorderStudent.balance > 20 ? 'green' : 'red'}">${currentPreorderStudent.balance.toFixed(2)} ريال</span>
                </div>
                <hr style="margin: 15px 0;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>المنتج:</strong>
                    <span>${currentPreorderProduct.icon} ${currentPreorderProduct.name}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>السعر:</strong>
                    <span>${currentPreorderProduct.price.toFixed(2)} ريال</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <strong>الكمية:</strong>
                    <span>1</span>
                </div>
                <hr style="margin: 15px 0;">
                <div style="display: flex; justify-content: space-between; font-size: 1.1rem; font-weight: bold;">
                    <strong>الإجمالي:</strong>
                    <span style="color: var(--primary-color)">${currentPreorderProduct.price.toFixed(2)} ريال</span>
                </div>
            `;

            preorderSummary.style.display = 'block';
        }

        // تأكيد الطلب المسبق
        function confirmPreorder() {
            if (!currentPreorderProduct || !currentPreorderStudent) return;

            // التحقق من الرصيد
            if (currentPreorderStudent.balance < currentPreorderProduct.price) {
                showNotification('رصيد الطالب غير كافي للطلب المسبق', 'error');
                return;
            }

            // إنشاء الطلب المسبق
            const preorder = {
                id: Date.now(),
                studentId: currentPreorderStudent.studentId,
                studentName: currentPreorderStudent.name,
                product: {
                    id: currentPreorderProduct.id,
                    name: currentPreorderProduct.name,
                    emoji: currentPreorderProduct.icon,
                    price: currentPreorderProduct.price
                },
                quantity: 1,
                totalPrice: currentPreorderProduct.price,
                status: 'pending',
                timestamp: new Date().toISOString(),
                estimatedTime: new Date(Date.now() + 15 * 60 * 1000).toISOString()
            };

            // حفظ الطلب المسبق
            savePreorder(preorder);

            // إغلاق النافذة
            closeCardSwipeModal();

            // عرض رسالة نجاح
            showNotification(`تم إنشاء طلب مسبق لـ ${currentPreorderStudent.name} - ${currentPreorderProduct.name}`, 'success');

            // تحديث عرض الطلبات المسبقة
            updatePrepaidOrdersDisplay();
        }

        // إلغاء إنشاء الطلب المسبق
        function cancelPreorderCreation() {
            closeCardSwipeModal();
        }

        // حفظ الطلب المسبق
        function savePreorder(preorder) {
            let preorders = JSON.parse(localStorage.getItem('preorders')) || [];
            preorders.push(preorder);
            localStorage.setItem('preorders', JSON.stringify(preorders));
        }

        // عرض نافذة إنشاء حساب طالب جديد
        function showCreateStudentModal(searchTerm = '') {
            // تحديد ما إذا كان البحث برقم أم باسم
            const isNumeric = /^\d+$/.test(searchTerm);
            const nameValue = isNumeric ? '' : searchTerm;
            const noorValue = isNumeric && searchTerm.length === 10 ? searchTerm : '';

            const modalHtml = `
                <div id="create-student-modal" class="card-swipe-modal" style="display: flex;">
                    <div class="card-swipe-content" style="max-width: 500px;">
                        <button class="close-modal-btn" onclick="closeCreateStudentModal()">×</button>

                        <div class="swipe-header">
                            <div class="swipe-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h2 class="swipe-title">إنشاء حساب طالب جديد</h2>
                            <p class="swipe-subtitle">إدخال بيانات الطالب</p>
                            ${searchTerm ? `<p class="swipe-note">البحث عن: "${searchTerm}"</p>` : ''}
                        </div>

                        <form id="create-student-form" class="edit-form">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user"></i>
                                    اسم الطالب
                                </label>
                                <input type="text" id="new-student-name" class="form-input"
                                       placeholder="أدخل اسم الطالب الكامل" value="${nameValue}" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-id-card"></i>
                                    رقم نور
                                </label>
                                <input type="text" id="new-student-noor" class="form-input"
                                       placeholder="أدخل رقم نور (10 أرقام)" maxlength="10" value="${noorValue}" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-graduation-cap"></i>
                                    الصف الدراسي
                                </label>
                                <select id="new-student-grade" class="form-input" required>
                                    <option value="">اختر الصف</option>
                                    <option value="الأول الابتدائي">الأول الابتدائي</option>
                                    <option value="الثاني الابتدائي">الثاني الابتدائي</option>
                                    <option value="الثالث الابتدائي">الثالث الابتدائي</option>
                                    <option value="الرابع الابتدائي">الرابع الابتدائي</option>
                                    <option value="الخامس الابتدائي">الخامس الابتدائي</option>
                                    <option value="السادس الابتدائي">السادس الابتدائي</option>
                                    <option value="الأول المتوسط">الأول المتوسط</option>
                                    <option value="الثاني المتوسط">الثاني المتوسط</option>
                                    <option value="الثالث المتوسط">الثالث المتوسط</option>
                                    <option value="الأول الثانوي">الأول الثانوي</option>
                                    <option value="الثاني الثانوي">الثاني الثانوي</option>
                                    <option value="الثالث الثانوي">الثالث الثانوي</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-wallet"></i>
                                    الرصيد الابتدائي
                                </label>
                                <input type="number" id="new-student-balance" class="form-input"
                                       placeholder="0.00" min="0" step="0.01" value="0">
                            </div>

                            <div class="summary-actions">
                                <button type="submit" class="confirm-preorder-btn">
                                    <i class="fas fa-save"></i>
                                    إنشاء الحساب
                                </button>
                                <button type="button" class="cancel-preorder-btn" onclick="closeCreateStudentModal()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // إضافة النافذة إلى الصفحة
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إضافة مستمع للنموذج
            document.getElementById('create-student-form').addEventListener('submit', function(e) {
                e.preventDefault();
                createNewStudent();
            });
        }

        // إغلاق نافذة إنشاء الطالب
        function closeCreateStudentModal() {
            const modal = document.getElementById('create-student-modal');
            if (modal) {
                modal.remove();
            }
        }

        // إنشاء طالب جديد
        function createNewStudent() {
            const name = document.getElementById('new-student-name').value.trim();
            const noorId = document.getElementById('new-student-noor').value.trim();
            const grade = document.getElementById('new-student-grade').value;
            const balance = parseFloat(document.getElementById('new-student-balance').value) || 0;

            // التحقق من البيانات
            if (!name || !noorId || !grade) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            if (noorId.length !== 10 || !/^\d+$/.test(noorId)) {
                showNotification('رقم نور يجب أن يكون 10 أرقام', 'error');
                return;
            }

            try {
                // الحصول على قاعدة البيانات
                const db = getDatabase();

                // التحقق من عدم تكرار رقم نور
                const existingStudent = db.users.find(user =>
                    user.role === 'student' && user.noorId === noorId
                );

                if (existingStudent) {
                    showNotification('رقم نور موجود بالفعل', 'error');
                    return;
                }

                // إنشاء الطالب الجديد
                const newStudentId = Math.max(...db.users.map(u => u.id), 0) + 1;
                const newStudent = {
                    id: newStudentId,
                    username: `student_${noorId}`,
                    password: 'student123', // كلمة مرور افتراضية
                    role: 'student',
                    name: name,
                    noorId: noorId,
                    grade: grade,
                    balance: balance,
                    schoolId: currentUser.schoolId || 1,
                    allergies: [],
                    createdAt: new Date().toISOString(),
                    createdBy: currentUser.name
                };

                // إضافة الطالب إلى قاعدة البيانات
                db.users.push(newStudent);
                saveDatabase(db);

                // تحويل إلى التنسيق المطلوب وإضافة للقائمة المحلية
                const formattedStudent = {
                    id: newStudent.id,
                    name: newStudent.name,
                    studentId: newStudent.noorId,
                    cardId: `CARD${newStudent.id.toString().padStart(3, '0')}`,
                    grade: newStudent.grade,
                    balance: newStudent.balance,
                    allergies: newStudent.allergies || [],
                    schoolId: newStudent.schoolId
                };

                students.push(formattedStudent);

                // تحديد الطالب الجديد للطلب المسبق
                currentPreorderStudent = formattedStudent;

                // إغلاق النافذة وعرض ملخص الطلب
                closeCreateStudentModal();
                showPreorderSummary();

                showNotification(`تم إنشاء حساب الطالب: ${name} بنجاح`, 'success');
                console.log('تم إنشاء طالب جديد:', formattedStudent);

            } catch (error) {
                console.error('خطأ في إنشاء الطالب:', error);
                showNotification('حدث خطأ في إنشاء الحساب', 'error');
            }
        }

        // تحميل الطلبات مسبقة الدفع
        function loadPrepaidOrders() {
            // محاولة تحميل الطلبات من localStorage أولاً
            const savedPreorders = localStorage.getItem('preorders');

            if (savedPreorders) {
                prepaidOrders = JSON.parse(savedPreorders);
                console.log('تم تحميل الطلبات المسبقة من التخزين المحلي:', prepaidOrders.length);
            } else {
                // بيانات تجريبية للطلبات مسبقة الدفع
                prepaidOrders = [
                    {
                        id: 1,
                        studentId: 1001,
                        studentName: 'سارة أحمد الخالد',
                        product: {
                            id: 1,
                            name: 'ساندويش فلافل',
                            price: 8.50,
                            emoji: '🥙'
                        },
                        quantity: 2,
                        totalPrice: 17.00,
                        status: 'pending',
                        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString() // قبل 15 دقيقة
                    },
                    {
                        id: 2,
                        studentId: 1002,
                        studentName: 'فاطمة سعد الأحمد',
                        product: {
                            id: 7,
                            name: 'عصير برتقال',
                            price: 5.00,
                            emoji: '🍊'
                        },
                        quantity: 1,
                        totalPrice: 5.00,
                        status: 'pending',
                        timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString() // قبل 8 دقائق
                    },
                    {
                        id: 3,
                        studentId: 1003,
                        studentName: 'خالد عبدالله الزهراني',
                        product: {
                            id: 14,
                            name: 'مرامي',
                            price: 2.50,
                            emoji: '🟡'
                        },
                        quantity: 3,
                        totalPrice: 7.50,
                        status: 'pending',
                        timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString() // قبل 25 دقيقة
                    }
                ];

                // حفظ البيانات التجريبية في localStorage
                localStorage.setItem('preorders', JSON.stringify(prepaidOrders));
                console.log('تم إنشاء وحفظ بيانات تجريبية للطلبات المسبقة');
            }

            updatePrepaidOrdersDisplay();
        }

        // ===== نظام إدارة قسم الطلبات المسبقة المتطور =====

        class PreordersManager {
            constructor() {
                this.preordersVisible = true;
                this.elements = {
                    preordersSection: null,
                    mainContainer: null,
                    toggleBtn: null,
                    rightSidebar: null
                };
                this.init();
            }

            init() {
                // تحديد العناصر
                this.elements.preordersSection = document.querySelector('.prepaid-orders');
                this.elements.mainContainer = document.querySelector('.main-container');
                this.elements.toggleBtn = document.getElementById('toggle-preorders-btn');
                this.elements.rightSidebar = document.querySelector('.student-card-section');

                console.log('🚀 تم تهيئة نظام إدارة الطلبات المسبقة:', this.elements);

                // التحقق من وجود العناصر
                if (!this.validateElements()) {
                    console.error('❌ فشل في العثور على العناصر المطلوبة');
                    return;
                }

                // تطبيق الحالة الافتراضية
                this.applyInitialState();

                // ربط الأحداث
                this.bindEvents();
            }

            validateElements() {
                const required = ['rightSidebar', 'leftSidebar', 'mainContainer', 'toggleBtn'];
                return required.every(key => {
                    const exists = this.elements[key] !== null;
                    if (!exists) {
                        console.error(`❌ لم يتم العثور على: ${key}`);
                    }
                    return exists;
                });
            }

            applyInitialState() {
                // العمود الأيسر مرئي دائماً
                this.elements.leftSidebar.classList.add('sidebar-visible');

                // العمود الأيمن مرئي افتراضياً
                this.elements.rightSidebar.classList.add('sidebar-visible');
                this.elements.toggleBtn.classList.add('active');

                // تطبيق التخطيط الافتراضي
                this.updateLayout();

                console.log('✅ تم تطبيق الحالة الافتراضية');
            }

            bindEvents() {
                // ربط زر التبديل
                this.elements.toggleBtn.addEventListener('click', () => {
                    this.toggleRightSidebar();
                });

                console.log('✅ تم ربط الأحداث');
            }

            toggleRightSidebar() {
                console.log('🔄 تبديل العمود الأيمن (الطلبات المسبقة)...');

                this.rightSidebarVisible = !this.rightSidebarVisible;

                if (this.rightSidebarVisible) {
                    this.showRightSidebar();
                } else {
                    this.hideRightSidebar();
                }

                this.updateLayout();
                this.updateToggleButton();
            }

            showRightSidebar() {
                console.log('👁️ إظهار العمود الأيمن (الطلبات المسبقة)');

                this.elements.rightSidebar.classList.remove('sidebar-hidden');
                this.elements.rightSidebar.classList.add('sidebar-visible');

                // تحديث الطلبات
                if (typeof updatePrepaidOrdersDisplay === 'function') {
                    updatePrepaidOrdersDisplay();
                }
            }

            hideRightSidebar() {
                console.log('🙈 إخفاء العمود الأيمن (الطلبات المسبقة)');

                this.elements.rightSidebar.classList.remove('sidebar-visible');
                this.elements.rightSidebar.classList.add('sidebar-hidden');
            }

            updateLayout() {
                // تحديث تخطيط الشبكة
                const leftVisible = this.leftSidebarVisible;
                const rightVisible = this.rightSidebarVisible;

                // إزالة جميع classes التخطيط
                this.elements.mainContainer.classList.remove(
                    'layout-left-only',
                    'layout-right-only',
                    'layout-center-only',
                    'layout-full'
                );

                // تطبيق التخطيط المناسب
                if (leftVisible && rightVisible) {
                    this.elements.mainContainer.classList.add('layout-full');
                    console.log('📐 تخطيط كامل: يسار + وسط + يمين');
                } else if (leftVisible && !rightVisible) {
                    this.elements.mainContainer.classList.add('layout-left-only');
                    console.log('📐 تخطيط يسار: يسار + وسط');
                } else if (!leftVisible && rightVisible) {
                    this.elements.mainContainer.classList.add('layout-right-only');
                    console.log('📐 تخطيط يمين: وسط + يمين');
                } else {
                    this.elements.mainContainer.classList.add('layout-center-only');
                    console.log('📐 تخطيط وسط: وسط فقط');
                }
            }

            updateToggleButton() {
                if (this.rightSidebarVisible) {
                    this.elements.toggleBtn.classList.add('active');
                    console.log('🟡 زر التحكم: نشط');
                } else {
                    this.elements.toggleBtn.classList.remove('active');
                    console.log('⚪ زر التحكم: غير نشط');
                }
            }

            // واجهة برمجية عامة
            getRightSidebarState() {
                return this.rightSidebarVisible;
            }

            setRightSidebarState(visible) {
                if (this.rightSidebarVisible !== visible) {
                    this.toggleRightSidebar();
                }
            }

            autoShowOnNewOrders() {
                if (!this.rightSidebarVisible) {
                    console.log('🔔 إظهار تلقائي للعمود الأيمن بسبب طلبات جديدة');
                    this.setRightSidebarState(true);

                    // تأثير النبضة
                    this.elements.rightSidebar.classList.add('new-orders-pulse');
                    setTimeout(() => {
                        this.elements.rightSidebar.classList.remove('new-orders-pulse');
                    }, 4000);
                }
            }
        }

        // إنشاء مثيل عام لإدارة الأعمدة
        let sidebarManager = null;

        // ===== نظام البحث المتقدم والسريع =====

        let searchTimeout = null;
        let isSearching = false;

        function initAdvancedSearch() {
            console.log('🔍 تهيئة نظام البحث المتقدم...');

            const searchInput = document.getElementById('student-search-input');
            const searchIndicator = document.getElementById('search-indicator');
            const quickResults = document.getElementById('quick-results');
            const scanBtn = document.getElementById('scan-btn');
            const quickScanBtn = document.getElementById('quick-scan-btn');
            const manualCardInput = document.getElementById('manual-card-input');
            const manualSearchBtn = document.getElementById('manual-search-btn');

            // التحقق من وجود العناصر الأساسية
            if (!searchInput) {
                console.error('❌ لم يتم العثور على حقل البحث الرئيسي');
                return;
            }

            console.log('✅ تم العثور على حقل البحث الرئيسي');

            // البحث أثناء الكتابة (Live Search) للحقل الرئيسي
            searchInput.addEventListener('input', function(e) {
                const query = e.target.value.trim();

                // إلغاء البحث السابق
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                if (query.length < 2) {
                    if (quickResults) hideQuickResults();
                    return;
                }

                // تأخير البحث لتحسين الأداء
                searchTimeout = setTimeout(() => {
                    performQuickSearch(query);
                }, 300);
            });

            // البحث عند الضغط على Enter في الحقل الرئيسي
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    if (query) {
                        performAdvancedSearch(query);
                    }
                }
            });

            // إخفاء النتائج عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.advanced-search-container')) {
                    if (quickResults) hideQuickResults();
                }
            });

            // زر البحث الرئيسي
            if (scanBtn) {
                scanBtn.addEventListener('click', function() {
                    const query = searchInput.value.trim();
                    if (query) {
                        performAdvancedSearch(query);
                    } else {
                        showNotification('يرجى إدخال نص للبحث', 'warning');
                    }
                });
                console.log('✅ تم ربط زر البحث الرئيسي');
            }

            // زر المسح السريع
            if (quickScanBtn) {
                quickScanBtn.addEventListener('click', function() {
                    console.log('🔄 تم النقر على زر المسح السريع');
                    simulateQuickScan();
                });
                console.log('✅ تم ربط زر المسح السريع');
            }

            // مؤشر البحث التفاعلي
            if (searchIndicator) {
                searchIndicator.addEventListener('click', function() {
                    const query = searchInput.value.trim();
                    if (query) {
                        performAdvancedSearch(query);
                    }
                });
                console.log('✅ تم ربط مؤشر البحث');
            }

            // الإدخال اليدوي للطلبات المسبقة
            if (manualCardInput) {
                manualCardInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchForPreorder();
                    }
                });

                manualCardInput.addEventListener('input', function(e) {
                    const query = e.target.value.trim();
                    if (query.length >= 3) {
                        // بحث فوري للطلبات المسبقة
                        setTimeout(() => {
                            if (this.value.trim() === query) {
                                searchForPreorderLive(query);
                            }
                        }, 500);
                    }
                });
                console.log('✅ تم ربط حقل الإدخال اليدوي');
            }

            // زر البحث اليدوي
            if (manualSearchBtn) {
                manualSearchBtn.addEventListener('click', function() {
                    searchForPreorder();
                });
                console.log('✅ تم ربط زر البحث اليدوي');
            }

            console.log('✅ تم تهيئة نظام البحث المتقدم بنجاح');
        }

        function performQuickSearch(query) {
            console.log('🔍 بحث سريع:', query);

            const searchIndicator = document.getElementById('search-indicator');
            const quickResults = document.getElementById('quick-results');

            // تأثير البحث
            searchIndicator.classList.add('searching');

            // محاكاة البحث في قاعدة البيانات
            setTimeout(() => {
                const results = searchStudents(query);
                displayQuickResults(results);
                searchIndicator.classList.remove('searching');
            }, 200);
        }

        function performAdvancedSearch(query) {
            console.log('🔍 بحث متقدم:', query);

            const scanBtn = document.getElementById('scan-btn');
            const searchIndicator = document.getElementById('search-indicator');

            // تأثير التحميل
            if (scanBtn) scanBtn.classList.add('searching');
            if (searchIndicator) searchIndicator.classList.add('searching');
            isSearching = true;

            // البحث الفعلي
            setTimeout(() => {
                try {
                    // البحث في القائمة المحلية أولاً
                    let results = searchStudentsAdvanced(query);

                    // إذا لم توجد نتائج، البحث في قاعدة البيانات
                    if (results.length === 0) {
                        results = searchInDatabase(query);
                    }

                    if (results.length > 0) {
                        // عرض أول نتيجة
                        selectStudent(results[0]);
                        showNotification(`تم العثور على ${results.length} طالب`, 'success');

                        // إذا كان هناك أكثر من نتيجة، اعرض قائمة للاختيار
                        if (results.length > 1) {
                            showMultipleResultsModal(results, query);
                        }
                    } else {
                        showNotification('لم يتم العثور على أي طالب', 'warning');

                        // اقتراح إنشاء حساب جديد
                        setTimeout(() => {
                            if (confirm(`لم يتم العثور على طالب باسم "${query}". هل تريد إنشاء حساب جديد؟`)) {
                                showCreateStudentModal(query);
                            }
                        }, 1500);
                    }
                } catch (error) {
                    console.error('خطأ في البحث المتقدم:', error);
                    showNotification('حدث خطأ في البحث', 'error');
                }

                // إنهاء التحميل
                if (scanBtn) scanBtn.classList.remove('searching');
                if (searchIndicator) searchIndicator.classList.remove('searching');
                isSearching = false;
                if (typeof hideQuickResults === 'function') hideQuickResults();

            }, 800);
        }

        // البحث المتقدم في قائمة الطلاب
        function searchStudentsAdvanced(query) {
            if (!students || students.length === 0) {
                console.log('⚠️ لا توجد قائمة طلاب محلية');
                return [];
            }

            const results = [];
            const queryLower = query.toLowerCase();

            students.forEach(student => {
                let score = 0;
                let matchType = '';

                // البحث الدقيق (نقاط عالية)
                if (student.studentId && student.studentId.toString() === query) {
                    score = 100;
                    matchType = 'رقم نور دقيق';
                } else if (student.name && student.name.toLowerCase() === queryLower) {
                    score = 95;
                    matchType = 'اسم دقيق';
                } else if (student.cardId && student.cardId.toLowerCase() === queryLower) {
                    score = 90;
                    matchType = 'رقم بطاقة دقيق';
                }
                // البحث الجزئي (نقاط متوسطة)
                else if (student.name && student.name.toLowerCase().includes(queryLower)) {
                    score = 70;
                    matchType = 'اسم جزئي';
                } else if (student.studentId && student.studentId.toString().includes(query)) {
                    score = 60;
                    matchType = 'رقم نور جزئي';
                } else if (student.cardId && student.cardId.toLowerCase().includes(queryLower)) {
                    score = 50;
                    matchType = 'رقم بطاقة جزئي';
                }

                if (score > 0) {
                    results.push({
                        ...student,
                        matchScore: score,
                        matchType: matchType
                    });
                }
            });

            // ترتيب النتائج حسب النقاط
            results.sort((a, b) => b.matchScore - a.matchScore);

            console.log(`🔍 نتائج البحث المحلي: ${results.length} طالب`);
            return results;
        }

        // البحث في قاعدة البيانات
        function searchInDatabase(query) {
            try {
                const db = getDatabase();
                if (!db.users) return [];

                const results = [];
                const queryLower = query.toLowerCase();

                db.users.forEach(user => {
                    if (user.role !== 'student') return;

                    let score = 0;
                    let matchType = '';

                    // البحث الدقيق
                    if (user.noorId && user.noorId.toString() === query) {
                        score = 100;
                        matchType = 'رقم نور دقيق';
                    } else if (user.name && user.name.toLowerCase() === queryLower) {
                        score = 95;
                        matchType = 'اسم دقيق';
                    } else if (user.username && user.username.toLowerCase() === queryLower) {
                        score = 85;
                        matchType = 'اسم مستخدم دقيق';
                    }
                    // البحث الجزئي
                    else if (user.name && user.name.toLowerCase().includes(queryLower)) {
                        score = 70;
                        matchType = 'اسم جزئي';
                    } else if (user.noorId && user.noorId.toString().includes(query)) {
                        score = 60;
                        matchType = 'رقم نور جزئي';
                    }

                    if (score > 0) {
                        // تحويل إلى التنسيق المطلوب
                        const formattedStudent = {
                            id: user.id,
                            name: user.name,
                            studentId: user.noorId || user.id.toString(),
                            cardId: user.cardId || `CARD${user.id.toString().padStart(3, '0')}`,
                            grade: user.grade || 'غير محدد',
                            balance: user.balance || 0,
                            allergies: user.allergies || [],
                            schoolId: user.schoolId,
                            matchScore: score,
                            matchType: matchType
                        };

                        results.push(formattedStudent);

                        // إضافة للقائمة المحلية
                        if (!students.find(s => s.id === user.id)) {
                            students.push(formattedStudent);
                        }
                    }
                });

                results.sort((a, b) => b.matchScore - a.matchScore);
                console.log(`🔍 نتائج البحث في قاعدة البيانات: ${results.length} طالب`);
                return results;

            } catch (error) {
                console.error('خطأ في البحث في قاعدة البيانات:', error);
                return [];
            }
        }

        function searchStudents(query) {
            // البحث في قائمة الطلاب
            return students.filter(student => {
                const searchTerms = [
                    student.name.toLowerCase(),
                    student.id.toString(),
                    student.noorId?.toString() || '',
                    student.cardId?.toString() || ''
                ];

                return searchTerms.some(term =>
                    term.includes(query.toLowerCase())
                );
            }).slice(0, 5); // أول 5 نتائج
        }

        function displayQuickResults(results) {
            const quickResults = document.getElementById('quick-results');

            if (results.length === 0) {
                quickResults.innerHTML = `
                    <div class="quick-result-item">
                        <div class="result-avatar">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="result-info">
                            <div class="result-name">لا توجد نتائج</div>
                            <div class="result-details">جرب البحث بطريقة أخرى</div>
                        </div>
                    </div>
                `;
            } else {
                quickResults.innerHTML = results.map(student => `
                    <div class="quick-result-item" onclick="selectStudentFromResults(${student.id})">
                        <div class="result-avatar">
                            ${student.name.charAt(0).toUpperCase()}
                        </div>
                        <div class="result-info">
                            <div class="result-name">${student.name}</div>
                            <div class="result-details">
                                رقم نور: ${student.noorId || 'غير محدد'} •
                                الرصيد: ${student.balance.toFixed(2)} ريال
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            quickResults.style.display = 'block';
        }

        function hideQuickResults() {
            const quickResults = document.getElementById('quick-results');
            quickResults.style.display = 'none';
        }

        function selectStudentFromResults(studentId) {
            const student = students.find(s => s.id === studentId);
            if (student) {
                selectStudent(student);
                hideQuickResults();

                // مسح حقل البحث
                const searchInput = document.getElementById('student-search-input');
                searchInput.value = student.name;
            }
        }

        function simulateQuickScan() {
            console.log('📱 تفعيل المسح السريع...');

            const quickScanBtn = document.getElementById('quick-scan-btn');
            const searchInput = document.getElementById('student-search-input');

            // تأثير بصري للمسح
            quickScanBtn.classList.add('searching');
            searchInput.focus();

            // تفعيل وضع المسح السريع
            activateQuickScanMode();

            // محاكاة مسح البطاقة (للاختبار)
            setTimeout(() => {
                // اختيار طالب عشوائي للمحاكاة
                if (students.length > 0) {
                    const randomStudent = students[Math.floor(Math.random() * students.length)];

                    // محاكاة إدخال رقم البطاقة
                    const cardNumber = randomStudent.cardId || randomStudent.id;
                    searchInput.value = cardNumber;

                    // تنفيذ البحث
                    selectStudent(randomStudent);
                    showNotification('تم مسح البطاقة بنجاح! (محاكاة)', 'success');

                    console.log('✅ تم مسح بطاقة الطالب:', randomStudent.name);
                } else {
                    showNotification('لا توجد بيانات طلاب للاختبار', 'warning');
                }

                quickScanBtn.classList.remove('searching');
            }, 1500);
        }

        function activateQuickScanMode() {
            console.log('🔄 تفعيل وضع المسح السريع...');

            const searchInput = document.getElementById('student-search-input');
            const cardInputArea = document.querySelector('.card-input-area');

            // تغيير مظهر منطقة الإدخال
            cardInputArea.classList.add('scan-mode');
            searchInput.placeholder = 'امسح البطاقة الآن...';

            // تفعيل الاستماع للباركود
            searchInput.addEventListener('input', handleBarcodeInput);

            // إضافة مؤشر بصري
            showScanIndicator();

            // إلغاء الوضع بعد 30 ثانية
            setTimeout(() => {
                deactivateQuickScanMode();
            }, 30000);
        }

        function handleBarcodeInput(event) {
            const input = event.target.value.trim();
            console.log('📝 إدخال جديد:', input);

            // التحقق من الإدخال الفوري (أي رقم أو نص)
            if (input.length >= 3) {
                console.log('🔍 بحث فوري عن:', input);

                // البحث الشامل
                const foundStudent = findStudentByAnyMethod(input);

                if (foundStudent) {
                    console.log('✅ تم العثور على الطالب:', foundStudent.name);
                    selectStudent(foundStudent);
                    showNotification(`تم العثور على: ${foundStudent.name}`, 'success');
                    deactivateQuickScanMode();
                    return;
                }
            }

            // التحقق من الباركود الطويل
            if (input.length >= 8 && /^\d+$/.test(input)) {
                console.log('🔍 تم اكتشاف باركود محتمل:', input);

                const foundStudent = findStudentByBarcode(input);

                if (foundStudent) {
                    selectStudent(foundStudent);
                    showNotification('تم مسح الباركود بنجاح!', 'success');
                    deactivateQuickScanMode();
                }
            }
        }

        function findStudentByAnyMethod(query) {
            console.log('🔍 البحث الشامل عن:', query);

            // البحث في جميع الحقول
            return students.find(student => {
                const searchFields = [
                    student.name?.toLowerCase() || '',
                    student.id?.toString() || '',
                    student.cardId?.toString() || '',
                    student.noorId?.toString() || '',
                    student.barcode?.toString() || ''
                ];

                const queryLower = query.toLowerCase();

                return searchFields.some(field => {
                    return field === query || // مطابقة تامة
                           field === queryLower || // مطابقة تامة (حروف صغيرة)
                           field.includes(queryLower) || // يحتوي على النص
                           queryLower.includes(field); // النص يحتوي على الحقل
                });
            });
        }

        function findStudentByBarcode(barcode) {
            // البحث في قائمة الطلاب بالباركود
            return students.find(student => {
                return student.cardId === barcode ||
                       student.id.toString() === barcode ||
                       student.noorId === barcode ||
                       student.barcode === barcode;
            });
        }

        function deactivateQuickScanMode() {
            console.log('⏹️ إلغاء وضع المسح السريع...');

            const searchInput = document.getElementById('student-search-input');
            const cardInputArea = document.querySelector('.card-input-area');

            // إعادة المظهر الطبيعي
            cardInputArea.classList.remove('scan-mode');
            searchInput.placeholder = 'رقم البطاقة، اسم الطالب، أو رقم نور...';

            // إزالة مستمع الباركود
            searchInput.removeEventListener('input', handleBarcodeInput);

            // إخفاء المؤشر
            hideScanIndicator();
        }

        function showScanIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'scan-indicator-overlay';
            indicator.innerHTML = `
                <div class="scan-overlay">
                    <div class="scan-line"></div>
                    <div class="scan-text">
                        <i class="fas fa-qrcode"></i>
                        جاهز لمسح البطاقة
                    </div>
                </div>
            `;

            indicator.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(46, 125, 50, 0.1);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
                pointer-events: none;
            `;

            const cardInputArea = document.querySelector('.card-input-area');
            cardInputArea.style.position = 'relative';
            cardInputArea.appendChild(indicator);

            // تأثير الخط المتحرك
            const style = document.createElement('style');
            style.textContent = `
                .scan-line {
                    width: 100%;
                    height: 2px;
                    background: linear-gradient(90deg, transparent, #4caf50, transparent);
                    animation: scanLine 2s ease-in-out infinite;
                }

                @keyframes scanLine {
                    0%, 100% { transform: translateY(-20px); opacity: 0; }
                    50% { transform: translateY(20px); opacity: 1; }
                }

                .scan-text {
                    position: absolute;
                    bottom: 10px;
                    color: #4caf50;
                    font-size: 0.8rem;
                    font-weight: 600;
                }

                .card-input-area.scan-mode {
                    border-color: #4caf50;
                    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
                }
            `;
            document.head.appendChild(style);
        }

        function hideScanIndicator() {
            const indicator = document.getElementById('scan-indicator-overlay');
            if (indicator) {
                indicator.remove();
            }
        }

        // وظيفة مباشرة لتفعيل المسح السريع (للاستخدام من onclick)
        function activateQuickScanDirect() {
            console.log('🚀 تفعيل المسح السريع المباشر...');

            const quickScanBtn = document.getElementById('quick-scan-btn');
            const searchInput = document.getElementById('student-search-input');

            if (!quickScanBtn || !searchInput) {
                console.error('❌ لم يتم العثور على العناصر المطلوبة');
                showNotification('خطأ في النظام', 'error');
                return;
            }

            // تأثير بصري فوري
            quickScanBtn.classList.add('searching');
            quickScanBtn.style.background = '#ff9800';
            quickScanBtn.style.color = 'white';

            // تركيز على حقل الإدخال
            searchInput.focus();
            searchInput.select();

            // تغيير النص
            const btnText = quickScanBtn.querySelector('.btn-text');
            const originalText = btnText.textContent;
            btnText.textContent = 'جاري التفعيل...';

            // تفعيل وضع المسح
            setTimeout(() => {
                activateQuickScanMode();

                // إعادة تعيين الزر
                quickScanBtn.classList.remove('searching');
                quickScanBtn.style.background = '';
                quickScanBtn.style.color = '';
                btnText.textContent = originalText;

                showNotification('تم تفعيل وضع المسح السريع', 'success');
            }, 500);
        }

        // وظيفة مساعدة للاختبار السريع
        function testQuickScan() {
            console.log('🧪 اختبار المسح السريع...');

            if (students.length === 0) {
                showNotification('لا توجد بيانات طلاب للاختبار', 'warning');
                return;
            }

            // اختيار طالب عشوائي
            const randomStudent = students[Math.floor(Math.random() * students.length)];
            const searchInput = document.getElementById('student-search-input');

            // محاكاة إدخال رقم البطاقة
            searchInput.value = randomStudent.cardId || randomStudent.id;

            // تنفيذ البحث
            setTimeout(() => {
                selectStudent(randomStudent);
                showNotification(`تم اختبار المسح: ${randomStudent.name}`, 'success');
            }, 300);
        }

        // دالة التبديل المتقدمة لقسم الطلبات المسبقة فقط
        function togglePreordersSidebar() {
            console.log('🔄 تبديل قسم الطلبات المسبقة في العمود الأيمن...');

            const preordersSection = document.querySelector('.prepaid-orders');
            const toggleBtn = document.getElementById('toggle-preorders-btn');

            if (!preordersSection || !toggleBtn) {
                console.error('❌ لم يتم العثور على قسم الطلبات المسبقة');
                return;
            }

            // فحص الحالة الحالية
            const isVisible = !preordersSection.classList.contains('section-hidden');

            if (isVisible) {
                // إخفاء قسم الطلبات المسبقة
                console.log('🙈 إخفاء قسم الطلبات المسبقة');
                preordersSection.classList.add('section-hidden');
                toggleBtn.classList.remove('active');

                // تأثير الانزلاق للأعلى
                preordersSection.style.transform = 'translateY(-20px)';
                preordersSection.style.opacity = '0';

                setTimeout(() => {
                    preordersSection.style.display = 'none';
                }, 300);

            } else {
                // إظهار قسم الطلبات المسبقة
                console.log('👁️ إظهار قسم الطلبات المسبقة');
                preordersSection.classList.remove('section-hidden');
                toggleBtn.classList.add('active');

                // تأثير الانزلاق للأسفل
                preordersSection.style.display = 'block';
                preordersSection.style.transform = 'translateY(-20px)';
                preordersSection.style.opacity = '0';

                setTimeout(() => {
                    preordersSection.style.transform = 'translateY(0)';
                    preordersSection.style.opacity = '1';
                }, 50);

                // تحديث الطلبات
                if (typeof updatePrepaidOrdersDisplay === 'function') {
                    updatePrepaidOrdersDisplay();
                }
            }
        }

        // ===== التحكم في قسم الطلبات المسبقة في العمود الأيسر =====

        function toggleLeftPreordersSidebar() {
            console.log('🔄 تبديل قسم الطلبات المسبقة في العمود الأيسر...');

            const preordersList = document.querySelector('.preorders-list');
            const toggleBtn = document.getElementById('toggle-left-preorders-btn');

            if (!preordersList || !toggleBtn) {
                console.error('❌ لم يتم العثور على قسم الطلبات المسبقة في العمود الأيسر');
                return;
            }

            // فحص الحالة الحالية
            const isVisible = !preordersList.classList.contains('section-hidden');

            if (isVisible) {
                // إخفاء قسم الطلبات المسبقة
                console.log('🙈 إخفاء قسم الطلبات المسبقة في العمود الأيسر');
                preordersList.classList.add('section-hidden');
                toggleBtn.classList.add('hidden');
                toggleBtn.title = 'إظهار الطلبات المسبقة';

                // تغيير الأيقونة
                const icon = toggleBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-eye-slash';
                }

            } else {
                // إظهار قسم الطلبات المسبقة
                console.log('👁️ إظهار قسم الطلبات المسبقة في العمود الأيسر');
                preordersList.classList.remove('section-hidden');
                toggleBtn.classList.remove('hidden');
                toggleBtn.title = 'إخفاء الطلبات المسبقة';

                // تغيير الأيقونة
                const icon = toggleBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-eye';
                }

                // تحديث الطلبات عند الإظهار
                if (typeof updatePrepaidOrdersDisplay === 'function') {
                    updatePrepaidOrdersDisplay();
                }
            }
        }

        // إظهار أقسام الطلبات المسبقة تلقائياً عند وجود طلبات جديدة
        function autoShowRightSidebarOnNewOrders() {
            const preorders = JSON.parse(localStorage.getItem('preorders')) || [];
            const pendingPreorders = preorders.filter(order => order.status === 'pending');

            if (pendingPreorders.length > 0) {
                console.log('🔔 إظهار تلقائي لأقسام الطلبات المسبقة بسبب طلبات جديدة');

                // إظهار قسم الطلبات المسبقة في العمود الأيمن
                const rightPreordersSection = document.querySelector('.prepaid-orders');
                const rightToggleBtn = document.getElementById('toggle-preorders-btn');

                if (rightPreordersSection && rightPreordersSection.classList.contains('section-hidden')) {
                    rightPreordersSection.classList.remove('section-hidden');
                    rightPreordersSection.style.display = 'block';
                    rightPreordersSection.style.transform = 'translateY(0)';
                    rightPreordersSection.style.opacity = '1';

                    if (rightToggleBtn) {
                        rightToggleBtn.classList.add('active');
                    }

                    // تأثير النبضة للقسم الأيمن
                    rightPreordersSection.classList.add('new-orders-pulse');
                    setTimeout(() => {
                        rightPreordersSection.classList.remove('new-orders-pulse');
                    }, 4000);
                }

                // إظهار قسم الطلبات المسبقة في العمود الأيسر
                const leftPreordersList = document.querySelector('.preorders-list');
                const leftToggleBtn = document.getElementById('toggle-left-preorders-btn');

                if (leftPreordersList && leftPreordersList.classList.contains('section-hidden')) {
                    leftPreordersList.classList.remove('section-hidden');
                    leftToggleBtn.classList.remove('hidden');
                    leftToggleBtn.title = 'إخفاء الطلبات المسبقة';

                    // تغيير الأيقونة
                    const icon = leftToggleBtn.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-eye';
                    }

                    // تأثير النبضة للقسم الأيسر
                    leftPreordersList.classList.add('new-orders-pulse');
                    setTimeout(() => {
                        leftPreordersList.classList.remove('new-orders-pulse');
                    }, 4000);
                }

                // إشعار للمستخدم
                showNotification(`تم استلام ${pendingPreorders.length} طلب مسبق جديد!`, 'info');
            }
        }

        // تحديث عرض الطلبات المسبقة
        function updatePrepaidOrdersDisplay() {
            // تحديث العمود الأيسر
            updateSidebarPreorders();

            // تحديث عداد الهيدر
            updateHeaderPreordersCount();

            // فحص الإظهار التلقائي للعمود الأيمن
            autoShowRightSidebarOnNewOrders();

            // تحديث القسم في العمود الأيمن (إذا وجد)
            const prepaidOrdersList = document.getElementById('prepaid-orders-list');
            const prepaidCount = document.getElementById('prepaid-count');

            if (prepaidOrdersList && prepaidCount) {
                // جلب الطلبات المسبقة من قاعدة البيانات
                const db = getDatabase();
                const preorders = (db.orders || []).filter(order =>
                    order.type === 'preorder' && order.status === 'pending'
                );

                prepaidCount.textContent = preorders.length;

                if (preorders.length === 0) {
                    prepaidOrdersList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-clock"></i>
                            <h3>لا توجد طلبات</h3>
                            <p>لا توجد طلبات مسبقة حالياً</p>
                        </div>
                    `;
                    return;
                }

                console.log(`📊 عرض ${preorders.length} طلب مسبق في القائمة الرئيسية`);

                prepaidOrdersList.innerHTML = preorders.map(order => {
                    console.log('📋 معالجة طلب للعرض:', {
                        id: order.id,
                        studentName: order.studentName,
                        userId: order.userId,
                        timestamp: order.timestamp || order.date,
                        totalPrice: order.totalPrice
                    });

                    const orderTime = new Date(order.time || order.timestamp || order.date).toLocaleTimeString('ar-SA', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // الحصول على اسم الطالب
                    const student = db.users.find(u => u.id === order.userId);
                    const studentName = student ? student.name : (order.studentName || 'طالب غير معروف');

                    // تجميع المنتجات - محسن للتعامل مع طلبات الطلاب وأولياء الأمور
                    let productsList;
                    if (order.products && order.products.length > 0) {
                        productsList = order.products.map(p => `${p.quantity}× ${p.name}`).join(', ');
                    } else if (order.product) {
                        productsList = `${order.quantity}× ${order.product.name}`;
                    } else {
                        productsList = 'منتجات غير محددة';
                    }

                    // تحديد نوع الطلب ومصدره
                    const orderType = order.parentId ? 'من ولي الأمر' : 'من الطالب';
                    const orderIcon = order.parentId ? 'fas fa-user-tie' : 'fas fa-user-graduate';
                    const requesterName = order.parentId ?
                        `${order.parentName} (ولي أمر ${studentName})` :
                        studentName;

                    return `
                        <div class="prepaid-order" data-order-id="${order.id}" onclick="showPrepaidOrderDetails(${order.id})">
                            <div class="prepaid-order-header">
                                <div class="prepaid-student-name">
                                    <i class="${orderIcon}"></i>
                                    ${requesterName}
                                </div>
                                <div class="prepaid-order-total">${order.totalPrice} ريال</div>
                            </div>
                            <div class="prepaid-order-items">
                                ${productsList}
                            </div>
                            <div class="prepaid-order-info">
                                <div class="prepaid-order-time">${orderTime}</div>
                                <div class="prepaid-order-type">
                                    <span class="order-type-badge ${order.parentId ? 'parent' : 'student'}">
                                        ${orderType}
                                    </span>
                                </div>
                            </div>
                            <div class="prepaid-order-actions">
                                <button class="complete-btn" onclick="event.stopPropagation(); completePrepaidOrder(${order.id})" title="تسليم الطلب">
                                    <i class="fas fa-check"></i> تسليم
                                </button>
                                <button class="cancel-btn" onclick="event.stopPropagation(); cancelPrepaidOrder(${order.id})" title="إلغاء الطلب">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        }

        // تحديث عداد الطلبات في الهيدر
        function updateHeaderPreordersCount() {
            const headerCount = document.getElementById('header-preorders-count');
            if (!headerCount) return;

            const db = getDatabase();
            const preorders = (db.orders || []).filter(order =>
                order.type === 'preorder' && order.status === 'pending'
            );

            headerCount.textContent = preorders.length;

            if (preorders.length === 0) {
                headerCount.classList.add('zero');
            } else {
                headerCount.classList.remove('zero');
            }
        }

        // تحديث العمود الأيسر للطلبات المسبقة - محسن
        function updateSidebarPreorders() {
            console.log('🔄 تحديث الشريط الجانبي للطلبات المسبقة...');

            const sidebarPreordersList = document.getElementById('sidebar-preorders-list');
            const sidebarPreordersCount = document.getElementById('sidebar-preorders-count');

            if (!sidebarPreordersList || !sidebarPreordersCount) {
                console.error('❌ لم يتم العثور على عناصر الشريط الجانبي');
                return;
            }

            // جلب الطلبات المسبقة من قاعدة البيانات
            const db = getDatabase();
            const allOrders = db.orders || [];
            const preorders = allOrders.filter(order =>
                order.type === 'preorder' && order.status === 'pending'
            );

            console.log(`📊 تم العثور على ${preorders.length} طلب مسبق معلق في الشريط الجانبي`);
            console.log('📋 تفاصيل الطلبات:', preorders.map(o => ({
                id: o.id,
                studentName: o.studentName,
                totalPrice: o.totalPrice,
                timestamp: o.timestamp || o.date
            })));

            sidebarPreordersCount.textContent = preorders.length;

            if (preorders.length === 0) {
                sidebarPreordersList.innerHTML = `
                    <div class="empty-preorders">
                        <i class="fas fa-clock"></i>
                        <p>لا توجد طلبات مسبقة</p>
                    </div>
                `;
                console.log('ℹ️ لا توجد طلبات مسبقة لعرضها في الشريط الجانبي');
                return;
            }

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            preorders.sort((a, b) => {
                const timeA = new Date(a.createdAt || a.timestamp || a.date).getTime();
                const timeB = new Date(b.createdAt || b.timestamp || b.date).getTime();
                return timeB - timeA;
            });

            console.log('✅ بدء عرض الطلبات في الشريط الجانبي...');

            sidebarPreordersList.innerHTML = preorders.map(order => {
                console.log(`📋 عرض طلب ${order.id} في الشريط الجانبي`);

                const orderTime = new Date(order.time || order.timestamp || order.date).toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // الحصول على اسم الطالب ومعلوماته
                const student = db.users.find(u => u.id === order.userId);
                const studentName = student ? student.name : (order.studentName || 'طالب غير معروف');

                // عرض المنتجات - محسن للتعامل مع طلبات الطلاب وأولياء الأمور
                let productDisplay;
                if (order.products && order.products.length > 0) {
                    const firstProduct = order.products[0];
                    const totalProducts = order.products.length;
                    productDisplay = totalProducts > 1
                        ? `${firstProduct.quantity}× ${firstProduct.name} +${totalProducts - 1} أخرى`
                        : `${firstProduct.quantity}× ${firstProduct.name}`;
                } else if (order.product) {
                    productDisplay = `${order.quantity}× ${order.product.name}`;
                } else {
                    productDisplay = 'منتجات غير محددة';
                }

                // تحديد نوع الطلب ومصدره
                const orderType = order.parentId ? 'من ولي الأمر' : 'من الطالب';
                const orderIcon = order.parentId ? '👨‍💼' : '🎓';
                const requesterName = order.parentId ?
                    `${order.parentName} (ولي أمر ${studentName})` :
                    studentName;

                // تحديد أولوية الطلب (حسب الوقت والمصدر)
                const orderAge = Date.now() - new Date(order.createdAt || order.timestamp || order.date).getTime();
                const isUrgent = orderAge > 600000; // أكثر من 10 دقائق
                const priorityClass = isUrgent ? 'urgent' : (order.parentId ? 'parent-priority' : 'normal');

                // معلومات إضافية عن الطالب
                const studentBalance = student ? student.balance : 0;
                const hasAllergies = student && student.allergies;
                const allergyWarning = hasAllergies ? `<div class="allergy-warning" title="تحذير: لديه حساسية من ${student.allergies}">⚠️ حساسية</div>` : '';

                return `
                    <div class="sidebar-preorder-item smart-order-card ${priorityClass}" data-order-id="${order.id}" onclick="viewOrderDetails(${order.id})">
                        <div class="sidebar-preorder-header">
                            <div class="sidebar-preorder-student">
                                <div class="student-info">
                                    <span class="student-icon">${orderIcon}</span>
                                    <div class="student-details">
                                        <div class="student-name">${studentName}</div>
                                        <div class="requester-info">${order.parentId ? `ولي الأمر: ${order.parentName}` : 'طلب مباشر'}</div>
                                    </div>
                                </div>
                                <div class="order-time-info">
                                    <div class="order-time">${orderTime}</div>
                                    ${isUrgent ? '<div class="urgent-badge">عاجل!</div>' : ''}
                                </div>
                            </div>
                        </div>

                        <div class="sidebar-preorder-content">
                            <div class="sidebar-preorder-product">
                                <span class="product-icon">🍽️</span>
                                <span class="product-text">${productDisplay}</span>
                            </div>

                            <div class="order-details-row">
                                <div class="sidebar-preorder-total">
                                    <span class="price-label">المبلغ:</span>
                                    <span class="price-value">${order.totalPrice.toFixed(2)} ريال</span>
                                </div>
                                <div class="student-balance">
                                    <span class="balance-label">الرصيد:</span>
                                    <span class="balance-value ${studentBalance < order.totalPrice ? 'insufficient' : 'sufficient'}">${studentBalance.toFixed(2)} ريال</span>
                                </div>
                            </div>

                            ${allergyWarning}

                            <div class="sidebar-preorder-type">
                                <span class="order-type-badge ${order.parentId ? 'parent' : 'student'}">
                                    ${orderType}
                                </span>
                                ${isUrgent ? '<span class="urgent-indicator">🔥 متأخر</span>' : ''}
                            </div>
                        </div>

                        <div class="sidebar-preorder-actions">
                            <button class="sidebar-preorder-btn complete-btn" onclick="event.stopPropagation(); quickCompleteOrder(${order.id})" title="تسليم الطلب">
                                <i class="fas fa-check"></i> تسليم
                            </button>
                            <button class="sidebar-preorder-btn details-btn" onclick="event.stopPropagation(); viewOrderDetails(${order.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i> تفاصيل
                            </button>
                            <button class="sidebar-preorder-btn cancel-btn" onclick="event.stopPropagation(); cancelPrepaidOrder(${order.id})" title="إلغاء الطلب">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            console.log(`✅ تم عرض ${preorders.length} طلب مسبق في الشريط الجانبي بنجاح`);
        }

        // عرض تفاصيل الطلب مسبق الدفع
        function showPrepaidOrderDetails(orderId) {
            // جلب الطلبات المسبقة من localStorage
            const preorders = JSON.parse(localStorage.getItem('preorders')) || [];
            const order = preorders.find(o => o.id === orderId);

            if (!order) return;

            const prepaidModal = document.getElementById('prepaid-modal');
            const orderDetails = document.getElementById('prepaid-order-details');

            orderDetails.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <h4>${order.studentName}</h4>
                    <p>رقم الطالب: ${order.studentId}</p>
                    <p>وقت الطلب: ${new Date(order.timestamp).toLocaleString('ar-SA')}</p>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                    <h5>تفاصيل الطلب:</h5>
                    <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                        <span>${order.product.emoji} ${order.product.name} × ${order.quantity}</span>
                        <span>${order.totalPrice} ريال</span>
                    </div>
                    <hr style="margin: 10px 0;">
                    <div style="display: flex; justify-content: space-between; font-weight: bold;">
                        <span>الإجمالي:</span>
                        <span>${order.totalPrice} ريال</span>
                    </div>
                </div>
            `;

            // إعداد زر التأكيد
            document.getElementById('confirm-prepaid-btn').onclick = () => confirmPrepaidOrder(orderId);

            prepaidModal.style.display = 'flex';
        }

        // تأكيد تسليم الطلب المسبق
        function confirmPrepaidOrder(orderId) {
            // جلب الطلبات المسبقة من localStorage
            let preorders = JSON.parse(localStorage.getItem('preorders')) || [];
            const order = preorders.find(o => o.id === orderId);

            if (!order) return;

            // تحديث حالة الطلب إلى مكتمل
            order.status = 'completed';
            order.completedAt = new Date().toISOString();
            order.completedBy = currentUser.name;

            // حفظ التحديث في localStorage
            localStorage.setItem('preorders', JSON.stringify(preorders));

            // تحديث المخزون
            const product = products.find(p => p.id === order.product.id);
            if (product) {
                product.stock -= order.quantity;
                localStorage.setItem('canteenProducts', JSON.stringify(products));
            }

            // حفظ المعاملة
            const transaction = {
                id: Date.now(),
                studentId: order.studentId,
                studentName: order.studentName,
                items: [{
                    name: order.product.name,
                    quantity: order.quantity,
                    price: order.product.price
                }],
                subtotal: order.totalPrice,
                tax: 0,
                total: order.totalPrice,
                paymentMethod: 'طلب مسبق',
                timestamp: new Date(),
                cashier: currentUser.name,
                isPrepaid: true
            };

            saveTransaction(transaction);
            updatePrepaidOrdersDisplay();
            displayProducts(); // تحديث عرض المنتجات
            closePrepaidModal();

            showNotification(`تم تسليم طلب ${order.studentName} بنجاح`, 'success');
        }

        // إغلاق نافذة الطلب مسبق الدفع
        function closePrepaidModal() {
            document.getElementById('prepaid-modal').style.display = 'none';
        }

        // حساب الوقت المنقضي
        function getTimeAgo(date) {
            const now = new Date();
            const diffInMinutes = Math.floor((now - date) / (1000 * 60));

            if (diffInMinutes < 1) return 'الآن';
            if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

            const diffInDays = Math.floor(diffInHours / 24);
            return `منذ ${diffInDays} يوم`;
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196f3'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: 600;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // حفظ المعاملة
        function saveTransaction(transaction) {
            const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
            transactions.push(transaction);
            localStorage.setItem('transactions', JSON.stringify(transactions));
        }

        // عرض الفاتورة
        function showReceipt(transaction) {
            const receiptModal = document.getElementById('receipt-modal');
            const receipt = document.getElementById('receipt');

            receipt.innerHTML = `
                <div class="receipt-header">
                    <div class="receipt-title">🏫 مقصف المدرسة</div>
                    <div class="receipt-info">
                        <div>فاتورة رقم: ${transaction.id}</div>
                        <div>التاريخ: ${transaction.timestamp.toLocaleDateString('ar-SA')}</div>
                        <div>الوقت: ${transaction.timestamp.toLocaleTimeString('ar-SA')}</div>
                        <div>الكاشير: ${transaction.cashier}</div>
                        <div>الطالب: ${transaction.studentName}</div>
                    </div>
                </div>

                <div class="receipt-items">
                    ${transaction.items.map(item => `
                        <div class="receipt-item">
                            <span>${item.name} × ${item.quantity}</span>
                            <span>${(item.price * item.quantity).toFixed(2)} ريال</span>
                        </div>
                    `).join('')}
                </div>

                <div class="receipt-total">
                    <div class="receipt-item">
                        <span>المجموع الفرعي:</span>
                        <span>${transaction.subtotal.toFixed(2)} ريال</span>
                    </div>
                    <div class="receipt-item">
                        <span>الضريبة (15%):</span>
                        <span>${transaction.tax.toFixed(2)} ريال</span>
                    </div>
                    <div class="receipt-item" style="font-weight: bold; font-size: 1.2rem;">
                        <span>الإجمالي:</span>
                        <span>${transaction.total.toFixed(2)} ريال</span>
                    </div>
                    <div class="receipt-item">
                        <span>طريقة الدفع:</span>
                        <span>رصيد الطالب</span>
                    </div>
                </div>

                <div class="receipt-footer">
                    <div>شكراً لزيارتكم</div>
                    <div>نتمنى لكم يوماً سعيداً</div>
                </div>
            `;

            receiptModal.style.display = 'flex';
        }

        // طباعة الفاتورة
        function printReceipt() {
            const receiptContent = document.getElementById('receipt').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>فاتورة</title>
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            .receipt { max-width: 300px; margin: 0 auto; }
                            .receipt-item { display: flex; justify-content: space-between; margin: 5px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="receipt">${receiptContent}</div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // إغلاق نافذة الفاتورة
        function closeReceiptModal() {
            document.getElementById('receipt-modal').style.display = 'none';
        }



        // ===== وظائف تعديل المنتجات =====

        // فتح نافذة تعديل المنتج
        function openEditModal(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            currentEditingProduct = product;

            // ملء البيانات في النموذج
            document.getElementById('edit-product-name').value = product.name;
            document.getElementById('edit-product-price').value = product.price.toFixed(2);

            // إزالة رسائل الخطأ السابقة
            clearEditErrors();

            // عرض النافذة
            document.getElementById('edit-product-modal').style.display = 'flex';

            // التركيز على حقل الاسم
            setTimeout(() => {
                document.getElementById('edit-product-name').focus();
                document.getElementById('edit-product-name').select();
            }, 100);
        }

        // إغلاق نافذة التعديل
        function closeEditModal() {
            document.getElementById('edit-product-modal').style.display = 'none';
            currentEditingProduct = null;
            clearEditErrors();
            document.getElementById('edit-product-form').reset();
        }

        // مسح رسائل الخطأ
        function clearEditErrors() {
            document.getElementById('edit-product-name').classList.remove('error');
            document.getElementById('edit-product-price').classList.remove('error');
            document.getElementById('name-error').style.display = 'none';
            document.getElementById('price-error').style.display = 'none';
        }

        // التحقق من صحة البيانات
        function validateEditForm() {
            const name = document.getElementById('edit-product-name').value.trim();
            const price = parseFloat(document.getElementById('edit-product-price').value);
            let isValid = true;

            // التحقق من الاسم
            if (!name || name.length < 2) {
                document.getElementById('edit-product-name').classList.add('error');
                document.getElementById('name-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('edit-product-name').classList.remove('error');
                document.getElementById('name-error').style.display = 'none';
            }

            // التحقق من السعر
            if (!price || price <= 0 || isNaN(price)) {
                document.getElementById('edit-product-price').classList.add('error');
                document.getElementById('price-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('edit-product-price').classList.remove('error');
                document.getElementById('price-error').style.display = 'none';
            }

            return isValid;
        }

        // حفظ تعديلات المنتج
        function saveProductEdit() {
            if (!validateEditForm() || !currentEditingProduct) return;

            const newName = document.getElementById('edit-product-name').value.trim();
            const newPrice = parseFloat(document.getElementById('edit-product-price').value);

            // التحقق من عدم تكرار الاسم
            const existingProduct = products.find(p =>
                p.id !== currentEditingProduct.id &&
                p.name.toLowerCase() === newName.toLowerCase()
            );

            if (existingProduct) {
                document.getElementById('edit-product-name').classList.add('error');
                document.getElementById('name-error').textContent = 'هذا الاسم موجود بالفعل';
                document.getElementById('name-error').style.display = 'block';
                return;
            }

            // حفظ البيانات القديمة للتراجع
            const oldName = currentEditingProduct.name;
            const oldPrice = currentEditingProduct.price;

            // تحديث المنتج
            currentEditingProduct.name = newName;
            currentEditingProduct.price = newPrice;

            // حفظ في localStorage
            localStorage.setItem('canteenProducts', JSON.stringify(products));

            // تحديث العروض
            displayProducts();
            updateMiniProductsDisplay();
            updateExternalProductsDisplay();

            // إغلاق النافذة
            closeEditModal();

            // عرض رسالة نجاح
            showNotification(`تم تحديث ${newName} بنجاح! السعر الجديد: ${newPrice.toFixed(2)} ريال`, 'success');

            console.log(`تم تعديل المنتج: ${oldName} (${oldPrice}) → ${newName} (${newPrice})`);
        }

        // إعداد مستمعي أحداث نافذة التعديل
        function setupEditModalListeners() {
            // إرسال النموذج
            document.getElementById('edit-product-form').addEventListener('submit', function(e) {
                e.preventDefault();
                saveProductEdit();
            });

            // إغلاق النافذة بالضغط على الخلفية
            document.getElementById('edit-product-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditModal();
                }
            });

            // إغلاق النافذة بمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.getElementById('edit-product-modal').style.display === 'flex') {
                    closeEditModal();
                }
            });

            // التحقق الفوري من البيانات
            document.getElementById('edit-product-name').addEventListener('input', function() {
                if (this.value.trim().length >= 2) {
                    this.classList.remove('error');
                    document.getElementById('name-error').style.display = 'none';
                }
            });

            document.getElementById('edit-product-price').addEventListener('input', function() {
                const price = parseFloat(this.value);
                if (price > 0 && !isNaN(price)) {
                    this.classList.remove('error');
                    document.getElementById('price-error').style.display = 'none';
                }
            });
        }

        // تحميل البيانات الأولية
        loadProducts();
        loadStudents();
        loadPrepaidOrders();
        updatePrepaidOrdersDisplay();
        watchForProductUpdates();

        // تحديث الطلبات المسبقة كل 3 ثوان (أسرع للاستجابة الفورية)
        setInterval(() => {
            console.log('⏰ تحديث دوري للطلبات المسبقة...');
            updateSidebarPreorders();
            updatePrepaidOrdersDisplay();
            updateHeaderPreordersCount();
        }, 3000);

        // تحديث إضافي للشريط الجانبي كل ثانيتين
        setInterval(() => {
            console.log('⏰ تحديث دوري للشريط الجانبي...');
            updateSidebarPreorders();
        }, 2000);

        // إضافة مستمع لزر التحديث
        document.getElementById('refresh-orders-btn').addEventListener('click', () => {
            updatePrepaidOrdersDisplay();
            updateHeaderPreordersCount();
        });

        // إعداد استقبال الطلبات المسبقة التلقائي
        setupRealTimeOrderReceiver();

        // تحديث العداد عند تحميل الصفحة
        updateHeaderPreordersCount();

        // فحص فوري للطلبات المسبقة عند تحميل الصفحة
        immediateOrderCheck();

        // تحديث حالة زر الصوت
        updateSoundButtonState();

        // دالة التحديث اليدوي للشريط الجانبي
        function manualRefreshSidebar() {
            console.log('🔄 تحديث يدوي للشريط الجانبي...');

            // إظهار مؤشر التحميل
            showNotification('جاري تحديث الشريط الجانبي...', 'info');

            // تحديث الشريط الجانبي
            updateSidebarPreorders();

            // تحديث العداد
            updateHeaderPreordersCount();

            // فحص فوري للطلبات الجديدة
            immediateOrderCheck();

            // إظهار رسالة النجاح
            setTimeout(() => {
                showNotification('تم تحديث الشريط الجانبي بنجاح', 'success');
            }, 500);
        }

        // دالة التحديث اليدوي للطلبات
        function manualRefreshOrders() {
            console.log('🔄 تحديث يدوي للطلبات المسبقة...');

            // إظهار مؤشر التحميل
            showNotification('جاري تحديث الطلبات...', 'info');

            // تحديث جميع العروض
            updatePrepaidOrdersDisplay();
            updateSidebarPreorders();
            updateHeaderPreordersCount();

            // فحص فوري للطلبات الجديدة
            immediateOrderCheck();

            // إظهار رسالة النجاح
            setTimeout(() => {
                showNotification('تم تحديث الطلبات بنجاح', 'success');
            }, 500);
        }

        // دالة الفحص الفوري للطلبات المسبقة
        function immediateOrderCheck() {
            console.log('🚀 بدء الفحص الفوري للطلبات المسبقة...');

            try {
                const db = getDatabase();
                const allOrders = db.orders || [];
                const pendingPreorders = allOrders.filter(order =>
                    order.type === 'preorder' && order.status === 'pending'
                );

                console.log(`🔍 تم العثور على ${pendingPreorders.length} طلب مسبق معلق في قاعدة البيانات`);

                if (pendingPreorders.length > 0) {
                    console.log('📋 تفاصيل الطلبات المعلقة:');
                    pendingPreorders.forEach((order, index) => {
                        console.log(`${index + 1}. طلب ${order.id} - ${order.studentName} - ${order.totalPrice} ريال - ${new Date(order.timestamp || order.date).toLocaleString()}`);
                    });

                    // تحديث جميع العروض بالترتيب الصحيح
                    console.log('🔄 تحديث جميع عروض الطلبات المسبقة...');

                    // تحديث الشريط الجانبي أولاً
                    updateSidebarPreorders();

                    // ثم تحديث العرض الرئيسي
                    updatePrepaidOrdersDisplay();

                    // وأخيراً تحديث العداد
                    updateHeaderPreordersCount();

                    // إظهار إشعار
                    showNotification(`تم العثور على ${pendingPreorders.length} طلب مسبق معلق`, 'info');
                } else {
                    console.log('ℹ️ لا توجد طلبات مسبقة معلقة حالياً');
                }
            } catch (error) {
                console.error('❌ خطأ في الفحص الفوري للطلبات:', error);
            }
        }

        // دالة إعداد استقبال الطلبات المسبقة في الوقت الفعلي
        function setupRealTimeOrderReceiver() {
            console.log('🔄 إعداد استقبال الطلبات المسبقة التلقائي...');

            // متغير لتتبع آخر فحص للطلبات
            let lastOrderCheck = localStorage.getItem('lastOrderCheck') || Date.now();

            // 1. استقبال الأحداث المخصصة
            window.addEventListener('newPreOrder', function(event) {
                console.log('📨 تم استقبال طلب مسبق جديد:', event.detail);
                handleNewPreOrder(event.detail.order);
            });

            // 2. استقبال عبر BroadcastChannel
            if (typeof BroadcastChannel !== 'undefined') {
                const channel = new BroadcastChannel('canteen-orders');
                channel.addEventListener('message', function(event) {
                    if (event.data.type === 'NEW_PREORDER') {
                        console.log('📡 تم استقبال طلب مسبق عبر BroadcastChannel:', event.data.order);
                        handleNewPreOrder(event.data.order);
                    }
                });

                // إرسال إشارة أن صفحة العامل متصلة
                channel.postMessage({
                    type: 'STAFF_ONLINE',
                    staffId: currentUser.id,
                    timestamp: new Date().toISOString()
                });
            }

            // 3. مراقبة localStorage للطلبات الجديدة
            window.addEventListener('storage', function(event) {
                if (event.key === 'orderBroadcast' && event.newValue) {
                    try {
                        const broadcastData = JSON.parse(event.newValue);
                        if (broadcastData.type === 'NEW_PREORDER') {
                            console.log('💾 تم استقبال طلب مسبق عبر localStorage:', broadcastData.order);
                            handleNewPreOrder(broadcastData.order);
                        }
                    } catch (error) {
                        console.error('خطأ في معالجة بيانات localStorage:', error);
                    }
                }
            });

            // 4. فحص دوري للطلبات الجديدة في قاعدة البيانات
            setInterval(() => {
                checkForNewOrders(lastOrderCheck);
                lastOrderCheck = Date.now();
                localStorage.setItem('lastOrderCheck', lastOrderCheck);
            }, 1000); // كل ثانية واحدة للاستجابة الفورية

            // 5. فحص الطلبات المعلقة في localStorage عند تحميل الصفحة
            checkPendingOrdersInStorage();

            console.log('✅ تم إعداد استقبال الطلبات المسبقة التلقائي بنجاح');
        }

        // دالة معالجة الطلب المسبق الجديد
        function handleNewPreOrder(order) {
            try {
                // التحقق من عدم معالجة نفس الطلب مرتين
                if (window.processedOrders && window.processedOrders.includes(order.id)) {
                    console.log('⚠️ تم تجاهل الطلب المكرر:', order.id);
                    return;
                }

                // إضافة الطلب لقائمة المعالجة
                if (!window.processedOrders) {
                    window.processedOrders = [];
                }
                window.processedOrders.push(order.id);

                // تنظيف قائمة المعالجة كل 10 دقائق
                setTimeout(() => {
                    const index = window.processedOrders.indexOf(order.id);
                    if (index > -1) {
                        window.processedOrders.splice(index, 1);
                    }
                }, 600000);

                // تحديث العرض فوراً بالترتيب الصحيح
                console.log('🔄 تحديث العرض بعد استلام طلب جديد...');

                // تحديث الشريط الجانبي أولاً
                updateSidebarPreorders();

                // ثم تحديث العرض الرئيسي
                updatePrepaidOrdersDisplay();

                // وأخيراً تحديث العداد
                updateHeaderPreordersCount();

                // تحديد نوع الطلب للإشعار
                const orderSource = order.parentId ? 'ولي الأمر' : 'الطالب';
                const requesterName = order.parentId ?
                    `${order.parentName} (ولي أمر ${order.studentName})` :
                    order.studentName;

                // إظهار إشعار ذكي محسن للعامل
                showSmartOrderNotification(order, orderSource, requesterName);

                // تشغيل صوت تنبيه متقدم
                playAdvancedNotificationSound(order);

                // إضافة تأثيرات بصرية ذكية للطلب الجديد
                addSmartVisualEffects(order.id);

                // إضافة شارة "جديد" مع تأثيرات متقدمة
                markOrderAsNewAdvanced(order.id);

                // إظهار نافذة تفاصيل سريعة للطلب الجديد
                showQuickOrderPreview(order);

                // تحديث إحصائيات الطلبات في الوقت الفعلي
                updateRealTimeOrderStats();

                console.log('✅ تم معالجة الطلب المسبق الجديد:', order.id, 'من:', orderSource);
            } catch (error) {
                console.error('❌ خطأ في معالجة الطلب المسبق الجديد:', error);
            }
        }

        // دالة فحص الطلبات الجديدة في قاعدة البيانات - محسنة
        function checkForNewOrders(lastCheck) {
            try {
                const db = getDatabase();

                // فحص جميع الطلبات المسبقة المعلقة
                const allPendingOrders = (db.orders || []).filter(order =>
                    order.type === 'preorder' &&
                    order.status === 'pending'
                );

                // فحص الطلبات الجديدة منذ آخر فحص
                const newOrders = allPendingOrders.filter(order => {
                    const orderTime = new Date(order.createdAt || order.timestamp || order.date).getTime();
                    return orderTime > lastCheck;
                });

                if (newOrders.length > 0) {
                    console.log(`🔍 تم العثور على ${newOrders.length} طلب مسبق جديد من أصل ${allPendingOrders.length} طلب معلق`);
                    newOrders.forEach(order => {
                        console.log('📋 معالجة طلب جديد:', {
                            id: order.id,
                            studentName: order.studentName,
                            timestamp: order.timestamp || order.date,
                            source: order.parentId ? 'parent' : 'student'
                        });
                        handleNewPreOrder(order);
                    });
                } else if (allPendingOrders.length > 0) {
                    // تحديث العرض حتى لو لم توجد طلبات جديدة
                    console.log(`📊 يوجد ${allPendingOrders.length} طلب معلق، تحديث العرض...`);
                    updatePrepaidOrdersDisplay();
                    updateSidebarPreorders();
                    updateHeaderPreordersCount();
                }
            } catch (error) {
                console.error('خطأ في فحص الطلبات الجديدة:', error);
            }
        }

        // دالة تشغيل صوت التنبيه
        function playNotificationSound() {
            try {
                // التحقق من إعدادات الصوت
                const soundEnabled = localStorage.getItem('notificationSoundEnabled') !== 'false';
                if (!soundEnabled) return;

                // إنشاء صوت تنبيه محسن
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // تشغيل نغمة مزدوجة للفت الانتباه
                playTone(audioContext, 800, 0.1, 0.3);
                setTimeout(() => playTone(audioContext, 1000, 0.1, 0.3), 200);
                setTimeout(() => playTone(audioContext, 800, 0.1, 0.3), 400);

            } catch (error) {
                console.log('لا يمكن تشغيل صوت التنبيه:', error);
                // بديل باستخدام beep بسيط
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.play().catch(() => {});
                } catch (e) {
                    console.log('فشل في تشغيل الصوت البديل');
                }
            }
        }

        // دالة مساعدة لتشغيل نغمة واحدة
        function playTone(audioContext, frequency, duration, volume) {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // دالة التحكم في صوت التنبيه
        function toggleNotificationSound() {
            const soundEnabled = localStorage.getItem('notificationSoundEnabled') !== 'false';
            const newState = !soundEnabled;

            localStorage.setItem('notificationSoundEnabled', newState.toString());

            const soundIcon = document.getElementById('sound-icon');
            const soundLabel = document.getElementById('sound-label');
            const soundBtn = document.getElementById('sound-toggle-btn');

            if (newState) {
                soundIcon.className = 'fas fa-volume-up';
                soundLabel.textContent = 'صوت التنبيه';
                soundBtn.classList.remove('muted');
                showNotification('تم تفعيل صوت التنبيه', 'success');
            } else {
                soundIcon.className = 'fas fa-volume-mute';
                soundLabel.textContent = 'صوت مكتوم';
                soundBtn.classList.add('muted');
                showNotification('تم كتم صوت التنبيه', 'info');
            }
        }

        // تحديث حالة زر الصوت عند تحميل الصفحة
        function updateSoundButtonState() {
            const soundEnabled = localStorage.getItem('notificationSoundEnabled') !== 'false';
            const soundIcon = document.getElementById('sound-icon');
            const soundLabel = document.getElementById('sound-label');
            const soundBtn = document.getElementById('sound-toggle-btn');

            if (soundEnabled) {
                soundIcon.className = 'fas fa-volume-up';
                soundLabel.textContent = 'صوت التنبيه';
                soundBtn.classList.remove('muted');
            } else {
                soundIcon.className = 'fas fa-volume-mute';
                soundLabel.textContent = 'صوت مكتوم';
                soundBtn.classList.add('muted');
            }
        }

        // دالة إبراز الطلب الجديد
        function highlightNewOrder(orderId) {
            setTimeout(() => {
                const orderElements = document.querySelectorAll(`[data-order-id="${orderId}"]`);
                orderElements.forEach(orderElement => {
                    if (orderElement) {
                        orderElement.classList.add('new-order-highlight');
                        setTimeout(() => {
                            orderElement.classList.remove('new-order-highlight');
                        }, 5000); // زيادة المدة إلى 5 ثوان
                    }
                });
            }, 500);
        }

        // دالة وضع شارة "جديد" للطلب
        function markOrderAsNew(orderId) {
            setTimeout(() => {
                const orderElements = document.querySelectorAll(`[data-order-id="${orderId}"]`);
                orderElements.forEach(orderElement => {
                    if (orderElement && !orderElement.querySelector('.new-order-badge')) {
                        const badge = document.createElement('div');
                        badge.className = 'new-order-badge';
                        badge.innerHTML = '<i class="fas fa-star"></i> جديد';
                        badge.style.cssText = `
                            position: absolute;
                            top: 5px;
                            right: 5px;
                            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                            color: white;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.7rem;
                            font-weight: bold;
                            z-index: 10;
                            animation: newBadgePulse 2s infinite;
                        `;

                        orderElement.style.position = 'relative';
                        orderElement.appendChild(badge);

                        // إزالة الشارة بعد دقيقتين
                        setTimeout(() => {
                            if (badge && badge.parentNode) {
                                badge.parentNode.removeChild(badge);
                            }
                        }, 120000);
                    }
                });
            }, 600);
        }

        // دالة فحص الطلبات المعلقة في localStorage
        function checkPendingOrdersInStorage() {
            try {
                const staffOrders = JSON.parse(localStorage.getItem('staffPendingOrders')) || [];
                const recentOrders = staffOrders.filter(order => {
                    const orderTime = new Date(order.receivedAt).getTime();
                    const now = Date.now();
                    return (now - orderTime) < 300000; // آخر 5 دقائق
                });

                if (recentOrders.length > 0) {
                    console.log(`🔍 تم العثور على ${recentOrders.length} طلب معلق في التخزين المحلي`);
                    recentOrders.forEach(order => {
                        // التحقق من أن الطلب ليس موجود بالفعل في قاعدة البيانات
                        const db = getDatabase();
                        const existingOrder = (db.orders || []).find(o => o.id === order.id);
                        if (!existingOrder) {
                            console.log('📋 معالجة طلب معلق:', order.id);
                            handleNewPreOrder(order);
                        }
                    });

                    // تنظيف الطلبات القديمة
                    const cleanedOrders = staffOrders.filter(order => {
                        const orderTime = new Date(order.receivedAt).getTime();
                        const now = Date.now();
                        return (now - orderTime) < 300000;
                    });
                    localStorage.setItem('staffPendingOrders', JSON.stringify(cleanedOrders));
                }
            } catch (error) {
                console.error('خطأ في فحص الطلبات المعلقة:', error);
            }
        }

        // دالة تحديث عداد الطلبات في الرأس
        function updateHeaderPreordersCount() {
            try {
                const db = getDatabase();
                const pendingOrders = (db.orders || []).filter(order =>
                    order.type === 'preorder' && order.status === 'pending'
                );

                const countElement = document.getElementById('prepaid-count');
                if (countElement) {
                    countElement.textContent = pendingOrders.length;
                }

                // تحديث عداد الشريط الجانبي أيضاً
                const sidebarCountElement = document.querySelector('.preorders-count');
                if (sidebarCountElement) {
                    sidebarCountElement.textContent = pendingOrders.length;
                }

                console.log(`تم تحديث عداد الطلبات: ${pendingOrders.length} طلب معلق`);
            } catch (error) {
                console.error('خطأ في تحديث عداد الطلبات:', error);
            }
        }

        // إكمال طلب مسبق الدفع - محسن للتعامل مع طلبات الطلاب وأولياء الأمور
        function completePrepaidOrder(orderId) {
            try {
                console.log('محاولة إكمال الطلب المسبق:', orderId);

                // جلب الطلبات المسبقة من قاعدة البيانات
                const db = getDatabase();
                const orderIndex = (db.orders || []).findIndex(order => order.id === orderId);

                if (orderIndex === -1) {
                    showNotification('لم يتم العثور على الطلب', 'error');
                    return;
                }

                const order = db.orders[orderIndex];

                // تحديث حالة الطلب إلى مكتمل
                db.orders[orderIndex].status = 'completed';
                db.orders[orderIndex].completedAt = new Date().toISOString();
                db.orders[orderIndex].completedBy = currentUser.name;

                // الحصول على اسم الطالب
                const student = db.users.find(u => u.id === order.userId);
                const studentName = student ? student.name : 'طالب غير معروف';

                // تحديد نوع الطلب ومصدره
                const orderType = order.parentId ? 'من ولي الأمر' : 'من الطالب';
                const requesterName = order.parentId ?
                    `${order.parentName} (ولي أمر ${studentName})` :
                    studentName;

                // حفظ التحديث في قاعدة البيانات
                saveDatabase(db);

                // إرسال إشعار للطالب بتسليم الطلب
                sendOrderCompletionNotification(order, student);

                // عرض رسالة نجاح
                showNotification(`تم تسليم الطلب ${orderType} لـ ${studentName} بنجاح`, 'success');

                // تحديث العرض
                updatePrepaidOrdersDisplay();
                updateSidebarPreorders();
                updateHeaderPreordersCount();

                console.log('تم إكمال الطلب المسبق:', order);
            } catch (error) {
                console.error('خطأ في إكمال الطلب المسبق:', error);
                showNotification('حدث خطأ في تسليم الطلب', 'error');
            }
        }

        // إرسال إشعار إكمال الطلب
        function sendOrderCompletionNotification(order, student) {
            try {
                const db = getDatabase();

                if (!db.notifications) {
                    db.notifications = [];
                }

                const now = new Date();
                const productsList = order.products ?
                    order.products.map(p => `${p.name} (${p.quantity})`).join(', ') :
                    `${order.product.name} (${order.quantity})`;

                const completionNotification = {
                    id: Date.now() + Math.random() * 1000,
                    userId: student.id,
                    title: '✅ تم تسليم طلبك المسبق',
                    message: `تم تسليم طلبك المسبق بنجاح. المنتجات: ${productsList}. المبلغ: ${order.totalPrice} ريال.`,
                    type: 'order_completed',
                    status: 'unread',
                    createdAt: now.toISOString(),
                    metadata: {
                        orderId: order.id,
                        totalPrice: order.totalPrice,
                        completedBy: currentUser.name
                    }
                };

                db.notifications.push(completionNotification);
                saveDatabase(db);

                console.log('تم إرسال إشعار إكمال الطلب للطالب');
            } catch (error) {
                console.error('خطأ في إرسال إشعار إكمال الطلب:', error);
            }
        }

        // إلغاء طلب مسبق الدفع
        function cancelPrepaidOrder(orderId) {
            if (!confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                return;
            }

            try {
                // جلب الطلبات المسبقة من قاعدة البيانات
                const db = getDatabase();
                const orderIndex = (db.orders || []).findIndex(order => order.id === orderId);

                if (orderIndex === -1) {
                    showNotification('لم يتم العثور على الطلب', 'error');
                    return;
                }

                const order = db.orders[orderIndex];

                // الحصول على اسم الطالب
                const student = db.users.find(u => u.id === order.userId);
                const studentName = student ? student.name : 'طالب غير معروف';

                // إرجاع المبلغ لرصيد الطالب
                if (student) {
                    student.balance += order.totalPrice;
                }

                // إرجاع المخزون - محسن للتعامل مع كلا النوعين
                if (order.products && order.products.length > 0) {
                    order.products.forEach(product => {
                        const dbProduct = db.products.find(p => p.id === product.productId);
                        if (dbProduct) {
                            dbProduct.stock += product.quantity;
                        }
                    });
                } else if (order.product) {
                    const dbProduct = db.products.find(p => p.id === order.product.id);
                    if (dbProduct) {
                        dbProduct.stock += order.quantity;
                    }
                }

                // حذف الطلب من القائمة
                db.orders.splice(orderIndex, 1);

                // حفظ التحديث في قاعدة البيانات
                saveDatabase(db);

                // تحديد نوع الطلب ومصدره
                const orderType = order.parentId ? 'من ولي الأمر' : 'من الطالب';

                // عرض رسالة نجاح
                showNotification(`تم إلغاء الطلب ${orderType} لـ ${studentName} وإرجاع المبلغ`, 'info');

                // تحديث العرض
                updatePrepaidOrdersDisplay();
                updateSidebarPreorders();
                updateHeaderPreordersCount();

                console.log('تم إلغاء الطلب المسبق:', order);
            } catch (error) {
                console.error('خطأ في إلغاء الطلب المسبق:', error);
                showNotification('حدث خطأ في إلغاء الطلب', 'error');
            }
        }

        // ===== الوظائف الذكية الجديدة للطلبات المسبقة =====

        // إظهار إشعار ذكي محسن للطلب الجديد
        function showSmartOrderNotification(order, orderSource, requesterName) {
            // إنشاء إشعار متقدم مع تفاصيل شاملة
            const notification = document.createElement('div');
            notification.className = 'smart-order-notification';
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #4caf50, #66bb6a);
                color: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
                z-index: 10001;
                font-weight: 600;
                max-width: 350px;
                min-width: 300px;
                animation: smartSlideIn 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
            `;

            // محتوى الإشعار الذكي
            const productsList = order.products ?
                order.products.map(p => `${p.quantity}× ${p.name}`).join(', ') :
                `${order.quantity}× ${order.product.name}`;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                    <div style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 50%; font-size: 1.5rem;">
                        ${order.parentId ? '👨‍💼' : '🎓'}
                    </div>
                    <div>
                        <div style="font-size: 1.1rem; font-weight: 700;">طلب مسبق جديد!</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">من ${orderSource}</div>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 10px; margin-bottom: 12px;">
                    <div style="font-size: 0.95rem; margin-bottom: 8px;">
                        <strong>الطالب:</strong> ${order.studentName}
                    </div>
                    <div style="font-size: 0.9rem; margin-bottom: 8px;">
                        <strong>المنتجات:</strong> ${productsList}
                    </div>
                    <div style="font-size: 1rem; font-weight: 700; color: #ffeb3b;">
                        <strong>المبلغ:</strong> ${order.totalPrice} ريال
                    </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: space-between;">
                    <button onclick="viewOrderDetails(${order.id})" style="
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 8px 16px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.85rem;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        <i class="fas fa-eye"></i> عرض التفاصيل
                    </button>
                    <button onclick="quickCompleteOrder(${order.id})" style="
                        background: #ffeb3b;
                        border: none;
                        color: #333;
                        padding: 8px 16px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 0.85rem;
                        font-weight: 600;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='#fdd835'"
                       onmouseout="this.style.background='#ffeb3b'">
                        <i class="fas fa-check"></i> تسليم سريع
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 8 ثوان
            setTimeout(() => {
                notification.style.animation = 'smartSlideOut 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 8000);
        }

        // تشغيل صوت تنبيه متقدم
        function playAdvancedNotificationSound(order) {
            try {
                // التحقق من إعدادات الصوت
                const soundEnabled = localStorage.getItem('notificationSoundEnabled') !== 'false';
                if (!soundEnabled) return;

                // إنشاء صوت تنبيه متقدم
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // تحديد نوع الصوت حسب مصدر الطلب
                const frequency1 = order.parentId ? 800 : 600; // تردد أعلى لطلبات أولياء الأمور
                const frequency2 = order.parentId ? 1000 : 800;

                // إنشاء نغمة مزدوجة
                const oscillator1 = audioContext.createOscillator();
                const oscillator2 = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator1.connect(gainNode);
                oscillator2.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator1.frequency.setValueAtTime(frequency1, audioContext.currentTime);
                oscillator2.frequency.setValueAtTime(frequency2, audioContext.currentTime);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator1.start(audioContext.currentTime);
                oscillator2.start(audioContext.currentTime);
                oscillator1.stop(audioContext.currentTime + 0.5);
                oscillator2.stop(audioContext.currentTime + 0.5);

                console.log('🔊 تم تشغيل صوت التنبيه المتقدم للطلب:', order.id);
            } catch (error) {
                console.log('⚠️ لا يمكن تشغيل الصوت:', error);
            }
        }

        // إضافة تأثيرات بصرية ذكية
        function addSmartVisualEffects(orderId) {
            setTimeout(() => {
                const orderElements = document.querySelectorAll(`[data-order-id="${orderId}"]`);
                orderElements.forEach(orderElement => {
                    if (orderElement) {
                        // إضافة تأثير النبض المتقدم
                        orderElement.classList.add('smart-order-highlight');

                        // إضافة تأثير الإضاءة
                        orderElement.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.6)';
                        orderElement.style.border = '2px solid #4caf50';

                        setTimeout(() => {
                            orderElement.classList.remove('smart-order-highlight');
                            orderElement.style.boxShadow = '';
                            orderElement.style.border = '';
                        }, 6000);
                    }
                });
            }, 500);
        }

        // إضافة أنماط CSS للإشعارات والتأثيرات الذكية
        const smartNotificationStyles = document.createElement('style');
        smartNotificationStyles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            @keyframes smartSlideIn {
                0% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                50% {
                    transform: translateX(-10px) scale(1.05);
                    opacity: 0.8;
                }
                100% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }
            @keyframes smartSlideOut {
                0% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
            }
            @keyframes smartOrderHighlight {
                0%, 100% {
                    transform: scale(1);
                    background: var(--card-bg);
                }
                25% {
                    transform: scale(1.02);
                    background: rgba(76, 175, 80, 0.1);
                }
                50% {
                    transform: scale(1.05);
                    background: rgba(76, 175, 80, 0.2);
                }
                75% {
                    transform: scale(1.02);
                    background: rgba(76, 175, 80, 0.1);
                }
            }
            .smart-order-highlight {
                animation: smartOrderHighlight 2s ease-in-out infinite;
            }
            @keyframes advancedBadgePulse {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
                }
                50% {
                    transform: scale(1.1) rotate(5deg);
                    box-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
                }
            }
            .advanced-new-badge {
                animation: advancedBadgePulse 1.5s ease-in-out infinite;
            }
        `;
        document.head.appendChild(smartNotificationStyles);

        // إضافة شارة "جديد" مع تأثيرات متقدمة
        function markOrderAsNewAdvanced(orderId) {
            setTimeout(() => {
                const orderElements = document.querySelectorAll(`[data-order-id="${orderId}"]`);
                orderElements.forEach(orderElement => {
                    if (orderElement && !orderElement.querySelector('.advanced-new-badge')) {
                        const badge = document.createElement('div');
                        badge.className = 'advanced-new-badge';
                        badge.innerHTML = '<i class="fas fa-star"></i> جديد';
                        badge.style.cssText = `
                            position: absolute;
                            top: 5px;
                            right: 5px;
                            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                            color: white;
                            padding: 6px 12px;
                            border-radius: 15px;
                            font-size: 0.75rem;
                            font-weight: bold;
                            z-index: 10;
                            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
                            border: 2px solid rgba(255, 255, 255, 0.3);
                        `;

                        orderElement.style.position = 'relative';
                        orderElement.appendChild(badge);

                        // إزالة الشارة بعد 3 دقائق
                        setTimeout(() => {
                            if (badge && badge.parentNode) {
                                badge.style.animation = 'fadeOut 0.5s ease';
                                setTimeout(() => {
                                    if (badge.parentNode) {
                                        badge.parentNode.removeChild(badge);
                                    }
                                }, 500);
                            }
                        }, 180000);
                    }
                });
            }, 600);
        }

        // إظهار نافذة تفاصيل سريعة للطلب الجديد
        function showQuickOrderPreview(order) {
            // إنشاء نافذة معاينة سريعة
            const preview = document.createElement('div');
            preview.className = 'quick-order-preview';
            preview.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 2px solid #4caf50;
                border-radius: 15px;
                padding: 15px;
                max-width: 300px;
                z-index: 10002;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                animation: quickPreviewSlideIn 0.4s ease;
                color: #333;
            `;

            const productsList = order.products ?
                order.products.map(p => `${p.quantity}× ${p.name}`).join(', ') :
                `${order.quantity}× ${order.product.name}`;

            preview.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: #4caf50;">معاينة سريعة</h4>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        font-size: 1.2rem;
                        cursor: pointer;
                        color: #666;
                    ">×</button>
                </div>
                <div style="font-size: 0.9rem; margin-bottom: 8px;">
                    <strong>الطالب:</strong> ${order.studentName}
                </div>
                <div style="font-size: 0.85rem; margin-bottom: 8px;">
                    <strong>المنتجات:</strong> ${productsList}
                </div>
                <div style="font-size: 0.9rem; font-weight: bold; color: #4caf50;">
                    <strong>المبلغ:</strong> ${order.totalPrice} ريال
                </div>
            `;

            document.body.appendChild(preview);

            // إزالة المعاينة بعد 5 ثوان
            setTimeout(() => {
                if (preview.parentNode) {
                    preview.style.animation = 'quickPreviewSlideOut 0.4s ease';
                    setTimeout(() => {
                        if (preview.parentNode) {
                            preview.parentNode.removeChild(preview);
                        }
                    }, 400);
                }
            }, 5000);
        }

        // تحديث إحصائيات الطلبات في الوقت الفعلي
        function updateRealTimeOrderStats() {
            try {
                const db = getDatabase();
                const pendingOrders = (db.orders || []).filter(order =>
                    order.type === 'preorder' && order.status === 'pending'
                );

                // تحديث العداد في الهيدر
                const headerCount = document.getElementById('sidebar-preorders-count');
                if (headerCount) {
                    headerCount.textContent = pendingOrders.length;

                    // إضافة تأثير نبض للعداد عند وجود طلبات جديدة
                    if (pendingOrders.length > 0) {
                        headerCount.style.animation = 'pulse 1s ease-in-out 3';
                    }
                }

                // تحديث إحصائيات إضافية
                const todayOrders = pendingOrders.filter(order => {
                    const orderDate = new Date(order.createdAt || order.timestamp || order.date);
                    const today = new Date();
                    return orderDate.toDateString() === today.toDateString();
                });

                console.log(`📊 إحصائيات الطلبات المسبقة: ${pendingOrders.length} إجمالي، ${todayOrders.length} اليوم`);
            } catch (error) {
                console.error('خطأ في تحديث إحصائيات الطلبات:', error);
            }
        }

        // عرض تفاصيل الطلب (للاستخدام من الإشعار الذكي)
        function viewOrderDetails(orderId) {
            try {
                const db = getDatabase();
                const order = (db.orders || []).find(o => o.id === orderId);

                if (!order) {
                    showNotification('لم يتم العثور على الطلب', 'error');
                    return;
                }

                // إنشاء نافذة تفاصيل متقدمة
                const modal = document.createElement('div');
                modal.className = 'order-details-modal';
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10003;
                    animation: fadeIn 0.3s ease;
                `;

                const student = db.users.find(u => u.id === order.userId);
                const productsList = order.products ?
                    order.products.map(p => `${p.quantity}× ${p.name} (${p.price} ريال)`).join('<br>') :
                    `${order.quantity}× ${order.product.name} (${order.product.price} ريال)`;

                modal.innerHTML = `
                    <div style="
                        background: white;
                        border-radius: 15px;
                        padding: 25px;
                        max-width: 400px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        position: relative;
                    ">
                        <button onclick="this.closest('.order-details-modal').remove()" style="
                            position: absolute;
                            top: 15px;
                            left: 15px;
                            background: none;
                            border: none;
                            font-size: 1.5rem;
                            cursor: pointer;
                            color: #666;
                        ">×</button>

                        <h3 style="color: #4caf50; margin-bottom: 20px; text-align: center;">
                            تفاصيل الطلب المسبق
                        </h3>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                            <h4 style="margin-bottom: 10px; color: #333;">معلومات الطالب:</h4>
                            <p><strong>الاسم:</strong> ${order.studentName}</p>
                            <p><strong>الرصيد:</strong> ${student ? student.balance : 'غير متوفر'} ريال</p>
                            ${student && student.allergies ? `<p><strong>الحساسية:</strong> ${student.allergies}</p>` : ''}
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                            <h4 style="margin-bottom: 10px; color: #333;">تفاصيل الطلب:</h4>
                            <p><strong>المنتجات:</strong></p>
                            <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                                ${productsList}
                            </div>
                            <p><strong>المبلغ الإجمالي:</strong> ${order.totalPrice} ريال</p>
                            <p><strong>وقت الطلب:</strong> ${new Date(order.createdAt || order.timestamp || order.date).toLocaleString('ar-SA')}</p>
                            <p><strong>مصدر الطلب:</strong> ${order.parentId ? 'ولي الأمر' : 'الطالب'}</p>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button onclick="quickCompleteOrder(${order.id}); this.closest('.order-details-modal').remove();" style="
                                background: #4caf50;
                                color: white;
                                border: none;
                                padding: 12px 20px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-weight: 600;
                            ">
                                <i class="fas fa-check"></i> تسليم الطلب
                            </button>
                            <button onclick="this.closest('.order-details-modal').remove()" style="
                                background: #666;
                                color: white;
                                border: none;
                                padding: 12px 20px;
                                border-radius: 8px;
                                cursor: pointer;
                            ">
                                إغلاق
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            } catch (error) {
                console.error('خطأ في عرض تفاصيل الطلب:', error);
                showNotification('حدث خطأ في عرض التفاصيل', 'error');
            }
        }

        // تسليم سريع للطلب (للاستخدام من الإشعار الذكي)
        function quickCompleteOrder(orderId) {
            if (confirm('هل تريد تسليم هذا الطلب؟')) {
                completePrepaidOrder(orderId);
            }
        }

        // إضافة أنماط CSS إضافية للمعاينة السريعة
        const quickPreviewStyles = document.createElement('style');
        quickPreviewStyles.textContent = `
            @keyframes quickPreviewSlideIn {
                from {
                    transform: translateY(100%) scale(0.8);
                    opacity: 0;
                }
                to {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                }
            }
            @keyframes quickPreviewSlideOut {
                from {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                }
                to {
                    transform: translateY(100%) scale(0.8);
                    opacity: 0;
                }
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(quickPreviewStyles);

        // إضافة أنماط CSS للبطاقات الذكية
        const smartOrderCardStyles = document.createElement('style');
        smartOrderCardStyles.textContent = `
            /* أنماط البطاقات الذكية للطلبات المسبقة */
            .smart-order-card {
                background: var(--card-bg);
                border: 2px solid var(--border-color);
                border-radius: 12px;
                padding: 15px;
                margin-bottom: 12px;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .smart-order-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                border-color: var(--primary-color);
            }

            .smart-order-card.urgent {
                border-color: #ff5722;
                background: linear-gradient(135deg, rgba(255, 87, 34, 0.05), var(--card-bg));
                animation: urgentPulse 2s ease-in-out infinite;
            }

            .smart-order-card.parent-priority {
                border-color: #2196f3;
                background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), var(--card-bg));
            }

            @keyframes urgentPulse {
                0%, 100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
                50% { box-shadow: 0 0 0 8px rgba(255, 87, 34, 0); }
            }

            .sidebar-preorder-header {
                margin-bottom: 12px;
            }

            .sidebar-preorder-student {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .student-info {
                display: flex;
                align-items: center;
                gap: 10px;
                flex: 1;
            }

            .student-icon {
                font-size: 1.2rem;
                background: rgba(76, 175, 80, 0.1);
                padding: 8px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 35px;
                height: 35px;
            }

            .student-details {
                flex: 1;
            }

            .student-name {
                font-weight: 600;
                font-size: 0.9rem;
                color: var(--text-color);
                margin-bottom: 2px;
            }

            .requester-info {
                font-size: 0.75rem;
                color: var(--text-muted);
            }

            .order-time-info {
                text-align: left;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 4px;
            }

            .order-time {
                font-size: 0.8rem;
                color: var(--text-muted);
                background: var(--bg-light);
                padding: 4px 8px;
                border-radius: 12px;
            }

            .urgent-badge {
                background: linear-gradient(45deg, #ff5722, #ff7043);
                color: white;
                font-size: 0.7rem;
                padding: 2px 6px;
                border-radius: 8px;
                font-weight: bold;
                animation: urgentBlink 1s ease-in-out infinite alternate;
            }

            @keyframes urgentBlink {
                from { opacity: 1; }
                to { opacity: 0.7; }
            }

            .sidebar-preorder-content {
                margin-bottom: 12px;
            }

            .sidebar-preorder-product {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 10px;
                padding: 8px;
                background: var(--bg-light);
                border-radius: 8px;
            }

            .product-icon {
                font-size: 1rem;
            }

            .product-text {
                font-size: 0.85rem;
                color: var(--text-color);
                font-weight: 500;
            }

            .order-details-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .sidebar-preorder-total, .student-balance {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 6px;
                border-radius: 6px;
                min-width: 80px;
            }

            .sidebar-preorder-total {
                background: rgba(76, 175, 80, 0.1);
            }

            .student-balance {
                background: rgba(33, 150, 243, 0.1);
            }

            .price-label, .balance-label {
                font-size: 0.7rem;
                color: var(--text-muted);
                margin-bottom: 2px;
            }

            .price-value {
                font-size: 0.85rem;
                font-weight: 600;
                color: var(--primary-color);
            }

            .balance-value {
                font-size: 0.8rem;
                font-weight: 600;
            }

            .balance-value.sufficient {
                color: var(--success-color);
            }

            .balance-value.insufficient {
                color: var(--danger-color);
                animation: warningPulse 1.5s ease-in-out infinite;
            }

            @keyframes warningPulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.6; }
            }

            .allergy-warning {
                background: linear-gradient(45deg, #ff9800, #ffb74d);
                color: white;
                font-size: 0.75rem;
                padding: 4px 8px;
                border-radius: 12px;
                text-align: center;
                margin-bottom: 8px;
                font-weight: 600;
                animation: allergyAlert 2s ease-in-out infinite;
            }

            @keyframes allergyAlert {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.02); }
            }

            .sidebar-preorder-type {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .order-type-badge {
                font-size: 0.7rem;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: 600;
            }

            .order-type-badge.parent {
                background: linear-gradient(45deg, #2196f3, #42a5f5);
                color: white;
            }

            .order-type-badge.student {
                background: linear-gradient(45deg, #4caf50, #66bb6a);
                color: white;
            }

            .urgent-indicator {
                font-size: 0.7rem;
                color: #ff5722;
                font-weight: bold;
                animation: urgentFlash 1s ease-in-out infinite;
            }

            @keyframes urgentFlash {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .sidebar-preorder-actions {
                display: flex;
                gap: 6px;
                justify-content: space-between;
            }

            .sidebar-preorder-btn {
                flex: 1;
                padding: 8px 6px;
                border: none;
                border-radius: 8px;
                font-size: 0.75rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;
            }

            .complete-btn {
                background: linear-gradient(45deg, #4caf50, #66bb6a);
                color: white;
            }

            .complete-btn:hover {
                background: linear-gradient(45deg, #388e3c, #4caf50);
                transform: translateY(-1px);
            }

            .details-btn {
                background: linear-gradient(45deg, #2196f3, #42a5f5);
                color: white;
            }

            .details-btn:hover {
                background: linear-gradient(45deg, #1976d2, #2196f3);
                transform: translateY(-1px);
            }

            .cancel-btn {
                background: linear-gradient(45deg, #f44336, #ef5350);
                color: white;
            }

            .cancel-btn:hover {
                background: linear-gradient(45deg, #d32f2f, #f44336);
                transform: translateY(-1px);
            }

            /* تأثيرات إضافية للبطاقات الجديدة */
            .smart-order-card.new-order {
                animation: newOrderAppear 0.5s ease-out;
            }

            @keyframes newOrderAppear {
                0% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                50% {
                    transform: translateX(-10px) scale(1.05);
                }
                100% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(smartOrderCardStyles);

        // ===== نظام الوضع الليلي/النهاري المتقدم =====

        class ThemeManager {
            constructor() {
                this.currentTheme = 'light';
                this.toggleBtn = null;
                this.themeIcon = null;
                this.themeLabel = null;
                this.init();
            }

            init() {
                console.log('🎨 تهيئة نظام الوضع الليلي/النهاري...');

                // العثور على العناصر
                this.toggleBtn = document.getElementById('theme-toggle');
                this.themeIcon = this.toggleBtn?.querySelector('.theme-icon');
                this.themeLabel = this.toggleBtn?.querySelector('.theme-label');

                if (!this.toggleBtn) {
                    console.error('❌ لم يتم العثور على زر تبديل الوضع');
                    return;
                }

                // تحميل الوضع المحفوظ
                this.loadSavedTheme();

                // ربط الأحداث
                this.bindEvents();

                console.log('✅ تم تهيئة نظام الوضع بنجاح');
            }

            loadSavedTheme() {
                const savedTheme = localStorage.getItem('canteen-theme') || 'light';
                console.log('📂 تحميل الوضع المحفوظ:', savedTheme);

                if (savedTheme === 'dark') {
                    this.setTheme('dark', false);
                } else {
                    this.setTheme('light', false);
                }
            }

            bindEvents() {
                this.toggleBtn.addEventListener('click', () => {
                    this.toggleTheme();
                });

                // اختصار لوحة المفاتيح (Ctrl + Shift + T)
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                        e.preventDefault();
                        this.toggleTheme();
                    }
                });
            }

            toggleTheme() {
                console.log('🔄 تبديل الوضع...');

                const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                this.setTheme(newTheme, true);
            }

            setTheme(theme, animate = true) {
                console.log('🎨 تطبيق الوضع:', theme);

                this.currentTheme = theme;

                // إضافة تأثير التبديل
                if (animate) {
                    this.toggleBtn.classList.add('switching');
                    setTimeout(() => {
                        this.toggleBtn.classList.remove('switching');
                    }, 600);
                }

                // تطبيق الوضع على الصفحة
                if (theme === 'dark') {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    document.body.setAttribute('data-theme', 'dark');
                    this.updateButtonForDarkMode();
                } else {
                    document.documentElement.removeAttribute('data-theme');
                    document.body.removeAttribute('data-theme');
                    this.updateButtonForLightMode();
                }

                // تحديث جميع العناصر
                this.updateAllElements(theme);

                // حفظ الوضع
                localStorage.setItem('canteen-theme', theme);

                // إشعار المستخدم
                this.showThemeNotification(theme);

                console.log('✅ تم تطبيق الوضع بنجاح');
            }

            updateAllElements(theme) {
                // إجبار إعادة حساب المتغيرات
                const root = document.documentElement;
                const computedStyle = getComputedStyle(root);

                // تحديث العناصر التي قد تحتاج تحديث يدوي
                const elementsToUpdate = [
                    '.product-card',
                    '.student-card-section',
                    '.preorders-sidebar',
                    '.products-section',
                    '.modal-content',
                    '.student-info-content',
                    '.card-swipe-content'
                ];

                elementsToUpdate.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        // إجبار إعادة رسم العنصر
                        element.style.display = 'none';
                        element.offsetHeight; // trigger reflow
                        element.style.display = '';
                    });
                });

                console.log('🔄 تم تحديث جميع العناصر للوضع:', theme);
            }

            updateButtonForDarkMode() {
                if (this.themeIcon) {
                    this.themeIcon.className = 'theme-icon fas fa-moon';
                }
                if (this.themeLabel) {
                    this.themeLabel.textContent = 'الوضع الليلي';
                }
            }

            updateButtonForLightMode() {
                if (this.themeIcon) {
                    this.themeIcon.className = 'theme-icon fas fa-sun';
                }
                if (this.themeLabel) {
                    this.themeLabel.textContent = 'الوضع النهاري';
                }
            }

            showThemeNotification(theme) {
                const message = theme === 'dark' ?
                    '🌙 تم تفعيل الوضع الليلي' :
                    '☀️ تم تفعيل الوضع النهاري';

                // إنشاء إشعار مؤقت
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    background: var(--card-bg);
                    color: var(--text-color);
                    padding: 12px 20px;
                    border-radius: 10px;
                    box-shadow: var(--shadow);
                    z-index: 10000;
                    font-size: 0.9rem;
                    font-weight: 500;
                    border: 2px solid var(--primary-color);
                    animation: slideInRight 0.3s ease;
                `;
                notification.textContent = message;

                document.body.appendChild(notification);

                // إزالة الإشعار بعد 3 ثوان
                setTimeout(() => {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            // واجهة برمجية عامة
            getCurrentTheme() {
                return this.currentTheme;
            }

            isDarkMode() {
                return this.currentTheme === 'dark';
            }

            setDarkMode() {
                this.setTheme('dark', true);
            }

            setLightMode() {
                this.setTheme('light', true);
            }
        }

        // إنشاء مدير الوضع
        let themeManager;

        // تهيئة نظام الوضع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            themeManager = new ThemeManager();
        });

        // وظيفة عامة للتبديل (للاستخدام من HTML)
        function toggleTheme() {
            if (themeManager) {
                themeManager.toggleTheme();
            }
        }
    </script>
</body>
</html>
