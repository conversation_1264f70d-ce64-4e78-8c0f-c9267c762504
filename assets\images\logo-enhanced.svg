<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="240" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer Glow Effect -->
  <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
    <feGaussianBlur stdDeviation="6" result="blur" />
    <feComposite in="SourceGraphic" in2="blur" operator="over" />
  </filter>

  <!-- Gradient Definitions -->
  <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#43a047" />
    <stop offset="100%" stop-color="#1b5e20" />
  </linearGradient>

  <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#ffd54f" />
    <stop offset="100%" stop-color="#f57f17" />
  </linearGradient>

  <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" stop-color="#ff8a65" />
    <stop offset="100%" stop-color="#d84315" />
  </linearGradient>

  <!-- Background Circle with Shadow -->
  <circle cx="120" cy="120" r="115" fill="url(#greenGradient)" filter="url(#glow)" />
  <circle cx="120" cy="120" r="105" fill="#ffffff" />

  <!-- Inner Design - Stylized Plate and Food -->
  <circle cx="120" cy="120" r="90" fill="#e8f5e9" />

  <!-- Plate Border with Gradient -->
  <path d="M120 40C80.2944 40 48 72.2944 48 112C48 151.706 80.2944 184 120 184C159.706 184 192 151.706 192 112C192 72.2944 159.706 40 120 40ZM120 176C84.6538 176 56 147.346 56 112C56 76.6538 84.6538 48 120 48C155.346 48 184 76.6538 184 112C184 147.346 155.346 176 120 176Z" fill="url(#greenGradient)" />

  <!-- Food Items with Gradients and Shadows -->
  <path d="M100 80C100 80 110 65 120 65C130 65 140 80 140 80L145 100H95L100 80Z" fill="url(#yellowGradient)" filter="url(#glow)" />
  <path d="M85 105C85 105 95 90 115 90C135 90 145 105 145 105L150 125H80L85 105Z" fill="#4caf50" filter="url(#glow)" />
  <path d="M100 130C100 130 110 115 130 115C150 115 160 130 160 130L165 150H95L100 130Z" fill="url(#orangeGradient)" filter="url(#glow)" />

  <!-- Utensils with Enhanced Style -->
  <path d="M75 75C75 75 70 100 75 125C80 150 85 155 85 155" stroke="#1b5e20" stroke-width="5" stroke-linecap="round" filter="url(#glow)" />
  <path d="M165 75C165 75 170 100 165 125C160 150 155 155 155 155" stroke="#1b5e20" stroke-width="5" stroke-linecap="round" filter="url(#glow)" />

  <!-- Arabic Text for "Success Intervals" with Enhanced Style -->
  <path d="M80 200C80 200 85 195 90 200C95 205 100 200 100 200" stroke="url(#greenGradient)" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />
  <path d="M110 200C110 200 115 195 120 200C125 205 130 200 130 200" stroke="url(#greenGradient)" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />
  <path d="M140 200C140 200 145 195 150 200C155 205 160 200 160 200" stroke="url(#greenGradient)" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />

  <!-- Decorative Elements with Glow -->
  <circle cx="80" cy="90" r="5" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="160" cy="90" r="5" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="90" cy="150" r="5" fill="url(#yellowGradient)" filter="url(#glow)" />
  <circle cx="150" cy="150" r="5" fill="url(#yellowGradient)" filter="url(#glow)" />

  <!-- Steam Effect with Enhanced Style -->
  <path d="M105 60C105 60 100 50 105 45C110 40 115 45 115 45" stroke="#1b5e20" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />
  <path d="M120 60C120 60 125 50 120 45C115 40 120 35 125 35" stroke="#1b5e20" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />
  <path d="M135 60C135 60 140 50 135 45C130 40 135 35 140 35" stroke="#1b5e20" stroke-width="4" stroke-linecap="round" filter="url(#glow)" />

  <!-- Shine Effect -->
  <circle cx="90" cy="80" r="3" fill="white" opacity="0.8" />
  <circle cx="150" cy="80" r="3" fill="white" opacity="0.8" />
  <circle cx="120" cy="70" r="2" fill="white" opacity="0.8" />
  <circle cx="100" cy="140" r="2" fill="white" opacity="0.8" />
  <circle cx="140" cy="140" r="2" fill="white" opacity="0.8" />
</svg>
