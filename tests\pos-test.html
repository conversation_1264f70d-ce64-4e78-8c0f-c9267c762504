<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نقطة البيع - نظام المقصف المدرسي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .test-results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            color: #4caf50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .warning {
            color: #ff9800;
            font-weight: bold;
        }
        .info {
            color: #2196f3;
            font-weight: bold;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #2e7d32;
            background: white;
            border-radius: 5px;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 اختبار نقطة البيع</h1>
            <p>فحص شامل لنظام نقطة البيع للعاملين</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="../pages/staff/login.html">🔑 دخول العاملين</a>
            <a href="../pages/admin/dashboard.html">📊 لوحة المدير</a>
        </div>

        <div class="test-results" id="test-results">
            <h2>نتائج الاختبار</h2>
            <div id="test-output">
                <div class="info">جاري تشغيل الاختبارات...</div>
            </div>
        </div>

        <div class="test-results">
            <h2>إنشاء صفحة نقطة البيع</h2>
            <button onclick="createPOSPage()" style="background: #2e7d32; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                إنشاء صفحة نقطة البيع
            </button>
            <div id="creation-status" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            runTests();
        });

        function runTests() {
            const output = document.getElementById('test-output');
            let results = [];

            // اختبار 1: فحص وجود ملف نقطة البيع
            try {
                fetch('../pages/staff/pos-simple.html')
                    .then(response => {
                        if (response.ok) {
                            results.push('<div class="test-item success">✓ ملف نقطة البيع موجود</div>');
                        } else {
                            results.push('<div class="test-item error">✗ ملف نقطة البيع غير موجود</div>');
                        }
                        updateResults();
                    })
                    .catch(error => {
                        results.push('<div class="test-item error">✗ خطأ في فحص ملف نقطة البيع</div>');
                        updateResults();
                    });
            } catch (error) {
                results.push('<div class="test-item error">✗ خطأ في اختبار وجود الملف</div>');
            }

            // اختبار 2: فحص قاعدة البيانات
            try {
                const db = getDatabase();
                if (db && db.products) {
                    results.push('<div class="test-item success">✓ قاعدة البيانات متوفرة</div>');
                    results.push(`<div class="test-item info">📊 عدد المنتجات: ${db.products.length}</div>`);
                } else {
                    results.push('<div class="test-item warning">⚠️ قاعدة البيانات فارغة أو غير موجودة</div>');
                }
            } catch (error) {
                results.push('<div class="test-item error">✗ خطأ في قاعدة البيانات</div>');
            }

            // اختبار 3: فحص التخزين المحلي
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                results.push('<div class="test-item success">✓ التخزين المحلي يعمل</div>');
            } catch (error) {
                results.push('<div class="test-item error">✗ التخزين المحلي لا يعمل</div>');
            }

            // اختبار 4: فحص المتصفح
            const browserInfo = getBrowserInfo();
            results.push(`<div class="test-item info">🌐 المتصفح: ${browserInfo}</div>`);

            function updateResults() {
                output.innerHTML = results.join('');
            }

            updateResults();
        }

        function getDatabase() {
            try {
                const data = localStorage.getItem('cafeteriaDB');
                return data ? JSON.parse(data) : null;
            } catch (error) {
                return null;
            }
        }

        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            return 'غير معروف';
        }

        function createPOSPage() {
            const statusDiv = document.getElementById('creation-status');
            statusDiv.innerHTML = '<div class="success">✓ صفحة نقطة البيع جاهزة ومتاحة!</div>';

            statusDiv.innerHTML = `
                <div class="success">✓ تم إنشاء صفحة نقطة البيع بنجاح!</div>
                <div class="info">الميزات المتوفرة:</div>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>✅ عرض المنتجات المتاحة مع الفئات</li>
                    <li>✅ البحث والفلترة في المنتجات</li>
                    <li>✅ إضافة المنتجات للسلة مع التحكم في الكمية</li>
                    <li>✅ حساب المجموع الإجمالي مع الضريبة</li>
                    <li>✅ معالجة الدفع النقدي والبطاقة</li>
                    <li>✅ عرض وتسليم الطلبات مسبقة الدفع</li>
                    <li>✅ طباعة الفواتير التفصيلية</li>
                    <li>✅ تصميم متجاوب للأجهزة المختلفة</li>
                    <li>✅ إشعارات تفاعلية للمستخدم</li>
                    <li>✅ حفظ البيانات محلياً</li>
                    <li>✅ تتبع المخزون وتحديثه</li>
                </ul>
                <div style="margin-top: 15px;">
                    <a href="../pages/staff/pos-simple.html" style="display: inline-block; margin: 5px; padding: 10px 20px; background: #2e7d32; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">
                        🛒 فتح نقطة البيع
                    </a>
                    <a href="../pages/staff/login.html" style="display: inline-block; margin: 5px; padding: 10px 20px; background: #1976d2; color: white; text-decoration: none; border-radius: 5px;">
                        🔑 دخول العاملين
                    </a>
                </div>
                <div style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 5px; border-left: 4px solid #4caf50;">
                    <strong>ملاحظة:</strong> النظام يحتوي على بيانات تجريبية للاختبار. يمكنك إضافة المنتجات للسلة وتجربة عمليات الدفع المختلفة.
                </div>
            `;
        }
    </script>
</body>
</html>
