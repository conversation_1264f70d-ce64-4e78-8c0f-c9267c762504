<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات الصيانة والإصلاح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .tool-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .tool-card h3 {
            color: #2e7d32;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .tool-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        button:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        button.warning {
            background: #ff9800;
        }
        button.warning:hover {
            background: #f57c00;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2e7d32, #4caf50);
            width: 0%;
            transition: width 0.3s ease;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2e7d32;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .log-viewer {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-error {
            color: #ff6b6b;
        }
        .log-warning {
            color: #ffd93d;
        }
        .log-info {
            color: #74c0fc;
        }
        .log-success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 أدوات الصيانة والإصلاح</h1>
            <p>مجموعة شاملة من أدوات الصيانة والإصلاح لنظام المقاصف</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="database-test.html">🗄️ اختبار قاعدة البيانات</a>
            <a href="compatibility-test.html">🌐 اختبار التوافق</a>
            <a href="../pages/admin/dashboard.html">📊 لوحة المدير</a>
        </div>

        <div class="tools-grid">
            <!-- أداة فحص النظام -->
            <div class="tool-card">
                <h3><i class="fas fa-stethoscope"></i> فحص النظام</h3>
                <p>فحص شامل لحالة النظام والتحقق من سلامة جميع المكونات</p>
                <button onclick="runSystemCheck()">تشغيل فحص النظام</button>
                <div id="system-check-result" class="result"></div>
            </div>

            <!-- أداة إصلاح قاعدة البيانات -->
            <div class="tool-card">
                <h3><i class="fas fa-database"></i> إصلاح قاعدة البيانات</h3>
                <p>إصلاح الأخطاء في قاعدة البيانات وتنظيف البيانات التالفة</p>
                <button onclick="repairDatabase()">إصلاح قاعدة البيانات</button>
                <button onclick="optimizeDatabase()" class="warning">تحسين الأداء</button>
                <div id="database-repair-result" class="result"></div>
            </div>

            <!-- أداة تنظيف التخزين -->
            <div class="tool-card">
                <h3><i class="fas fa-broom"></i> تنظيف التخزين</h3>
                <p>تنظيف الملفات المؤقتة والبيانات غير المستخدمة</p>
                <button onclick="cleanStorage()">تنظيف التخزين</button>
                <button onclick="clearCache()" class="warning">مسح الذاكرة المؤقتة</button>
                <div id="storage-clean-result" class="result"></div>
            </div>

            <!-- أداة إعادة تعيين النظام -->
            <div class="tool-card">
                <h3><i class="fas fa-redo"></i> إعادة تعيين النظام</h3>
                <p>إعادة تعيين النظام إلى الإعدادات الافتراضية</p>
                <button onclick="resetSystem()" class="danger">إعادة تعيين كاملة</button>
                <button onclick="resetSettings()" class="warning">إعادة تعيين الإعدادات</button>
                <div id="reset-result" class="result"></div>
            </div>

            <!-- أداة مراقبة الأداء -->
            <div class="tool-card">
                <h3><i class="fas fa-chart-line"></i> مراقبة الأداء</h3>
                <p>مراقبة أداء النظام وعرض الإحصائيات المفصلة</p>
                <button onclick="monitorPerformance()">بدء المراقبة</button>
                <button onclick="generateReport()">إنشاء تقرير</button>
                <div id="performance-result" class="result"></div>
            </div>

            <!-- أداة إدارة المستخدمين -->
            <div class="tool-card">
                <h3><i class="fas fa-users-cog"></i> إدارة المستخدمين</h3>
                <p>أدوات متقدمة لإدارة المستخدمين وحل المشاكل</p>
                <button onclick="fixUserAccounts()">إصلاح حسابات المستخدمين</button>
                <button onclick="resetPasswords()" class="warning">إعادة تعيين كلمات المرور</button>
                <div id="user-management-result" class="result"></div>
            </div>
        </div>

        <!-- إحصائيات النظام -->
        <div class="tool-card">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات النظام</h3>
            <div class="stats-grid" id="system-stats">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
            <button onclick="updateStats()">تحديث الإحصائيات</button>
        </div>

        <!-- عارض السجلات -->
        <div class="tool-card">
            <h3><i class="fas fa-file-alt"></i> عارض السجلات</h3>
            <p>عرض سجلات النظام والأخطاء</p>
            <button onclick="showLogs()">عرض السجلات</button>
            <button onclick="clearLogs()" class="danger">مسح السجلات</button>
            <div id="log-viewer" class="log-viewer"></div>
        </div>
    </div>

    <!-- تحميل Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../assets/js/main.js"></script>

    <script>
        // متغيرات عامة
        let systemLogs = [];
        let performanceData = [];
        let isMonitoring = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            initializeLogs();
        });

        // فحص النظام
        function runSystemCheck() {
            const resultDiv = document.getElementById('system-check-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري فحص النظام...</div>';

            let progress = 0;
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = '<div class="progress-fill"></div>';
            resultDiv.appendChild(progressBar);

            const checks = [
                { name: 'فحص قاعدة البيانات', test: checkDatabase },
                { name: 'فحص التخزين المحلي', test: checkLocalStorage },
                { name: 'فحص الذاكرة', test: checkMemory },
                { name: 'فحص الأداء', test: checkPerformance },
                { name: 'فحص التوافق', test: checkCompatibility }
            ];

            let results = [];
            let currentCheck = 0;

            function runNextCheck() {
                if (currentCheck >= checks.length) {
                    showFinalResults();
                    return;
                }

                const check = checks[currentCheck];
                addLog(`بدء ${check.name}`, 'info');

                setTimeout(() => {
                    try {
                        const result = check.test();
                        results.push({ name: check.name, result, success: true });
                        addLog(`✓ ${check.name} - نجح`, 'success');
                    } catch (error) {
                        results.push({ name: check.name, result: error.message, success: false });
                        addLog(`✗ ${check.name} - فشل: ${error.message}`, 'error');
                    }

                    currentCheck++;
                    progress = (currentCheck / checks.length) * 100;
                    progressBar.querySelector('.progress-fill').style.width = progress + '%';

                    runNextCheck();
                }, 500);
            }

            function showFinalResults() {
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;
                const successRate = Math.round((successCount / totalCount) * 100);

                let html = `<div class="info">تم الانتهاء من فحص النظام</div>`;
                html += `<div class="success">معدل النجاح: ${successRate}% (${successCount}/${totalCount})</div>`;

                results.forEach(result => {
                    const className = result.success ? 'success' : 'error';
                    const icon = result.success ? '✓' : '✗';
                    html += `<div class="${className}">${icon} ${result.name}</div>`;
                });

                if (successRate >= 80) {
                    html += '<div class="success">🎉 النظام يعمل بشكل ممتاز!</div>';
                } else if (successRate >= 60) {
                    html += '<div class="warning">⚠️ النظام يعمل بشكل جيد مع بعض المشاكل الطفيفة</div>';
                } else {
                    html += '<div class="error">❌ النظام يحتاج إلى صيانة عاجلة</div>';
                }

                resultDiv.innerHTML = html;
                addLog(`فحص النظام مكتمل - معدل النجاح: ${successRate}%`, 'info');
            }

            runNextCheck();
        }

        // دوال الفحص
        function checkDatabase() {
            const db = getDatabase();
            if (!db) throw new Error('قاعدة البيانات غير متوفرة');

            const requiredTables = ['users', 'schools', 'products', 'orders'];
            for (const table of requiredTables) {
                if (!db[table] || !Array.isArray(db[table])) {
                    throw new Error(`جدول ${table} مفقود أو تالف`);
                }
            }

            return 'قاعدة البيانات سليمة';
        }

        function checkLocalStorage() {
            try {
                const testKey = 'maintenance_test';
                localStorage.setItem(testKey, 'test');
                const value = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);

                if (value !== 'test') {
                    throw new Error('التخزين المحلي لا يعمل بشكل صحيح');
                }

                return 'التخزين المحلي يعمل بشكل صحيح';
            } catch (error) {
                throw new Error('التخزين المحلي غير متوفر');
            }
        }

        function checkMemory() {
            if (performance.memory) {
                const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);

                if (used / total > 0.9) {
                    throw new Error('استخدام الذاكرة مرتفع جداً');
                }

                return `استخدام الذاكرة: ${used}MB / ${total}MB`;
            }

            return 'معلومات الذاكرة غير متوفرة';
        }

        function checkPerformance() {
            const start = performance.now();

            // اختبار أداء بسيط
            let sum = 0;
            for (let i = 0; i < 100000; i++) {
                sum += i;
            }

            const end = performance.now();
            const duration = end - start;

            if (duration > 100) {
                throw new Error('الأداء بطيء جداً');
            }

            return `اختبار الأداء: ${duration.toFixed(2)}ms`;
        }

        function checkCompatibility() {
            const features = ['localStorage', 'JSON', 'fetch', 'Promise'];
            const missing = [];

            features.forEach(feature => {
                if (typeof window[feature] === 'undefined') {
                    missing.push(feature);
                }
            });

            if (missing.length > 0) {
                throw new Error(`ميزات مفقودة: ${missing.join(', ')}`);
            }

            return 'جميع الميزات المطلوبة متوفرة';
        }

        // إصلاح قاعدة البيانات
        function repairDatabase() {
            const resultDiv = document.getElementById('database-repair-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري إصلاح قاعدة البيانات...</div>';

            try {
                const db = getDatabase();
                let repaired = 0;
                let issues = [];

                // إصلاح المستخدمين
                if (db.users) {
                    const originalCount = db.users.length;
                    db.users = db.users.filter(user => user.id && user.username && user.role);
                    const removedUsers = originalCount - db.users.length;
                    if (removedUsers > 0) {
                        repaired += removedUsers;
                        issues.push(`تم حذف ${removedUsers} مستخدم تالف`);
                    }

                    // إصلاح أسماء المستخدمين المكررة
                    const usernames = new Set();
                    const duplicates = [];
                    db.users.forEach((user, index) => {
                        if (usernames.has(user.username)) {
                            duplicates.push(index);
                        } else {
                            usernames.add(user.username);
                        }
                    });

                    duplicates.reverse().forEach(index => {
                        db.users.splice(index, 1);
                        repaired++;
                    });

                    if (duplicates.length > 0) {
                        issues.push(`تم حذف ${duplicates.length} مستخدم مكرر`);
                    }
                }

                // إصلاح المدارس
                if (db.schools) {
                    const originalCount = db.schools.length;
                    db.schools = db.schools.filter(school => school.id && school.name);
                    const removedSchools = originalCount - db.schools.length;
                    if (removedSchools > 0) {
                        repaired += removedSchools;
                        issues.push(`تم حذف ${removedSchools} مدرسة تالفة`);
                    }
                }

                // إصلاح المنتجات
                if (db.products) {
                    const originalCount = db.products.length;
                    db.products = db.products.filter(product => product.id && product.name && product.price >= 0);
                    const removedProducts = originalCount - db.products.length;
                    if (removedProducts > 0) {
                        repaired += removedProducts;
                        issues.push(`تم حذف ${removedProducts} منتج تالف`);
                    }
                }

                // حفظ التغييرات
                if (repaired > 0) {
                    saveDatabase(db);
                    addLog(`تم إصلاح ${repaired} عنصر في قاعدة البيانات`, 'success');
                }

                let html = '';
                if (repaired === 0) {
                    html = '<div class="success">✓ قاعدة البيانات سليمة ولا تحتاج إصلاح</div>';
                } else {
                    html = `<div class="success">✓ تم إصلاح ${repaired} مشكلة في قاعدة البيانات</div>`;
                    issues.forEach(issue => {
                        html += `<div class="info">• ${issue}</div>`;
                    });
                }

                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إصلاح قاعدة البيانات: ${error.message}</div>`;
                addLog(`خطأ في إصلاح قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        // تحسين قاعدة البيانات
        function optimizeDatabase() {
            const resultDiv = document.getElementById('database-repair-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري تحسين قاعدة البيانات...</div>';

            try {
                const db = getDatabase();
                let optimizations = [];

                // ضغط البيانات
                const originalSize = JSON.stringify(db).length;

                // إزالة الخصائص غير الضرورية
                if (db.users) {
                    db.users.forEach(user => {
                        delete user.lastLogin;
                        delete user.tempData;
                    });
                }

                // ترتيب البيانات
                if (db.users) db.users.sort((a, b) => a.id - b.id);
                if (db.schools) db.schools.sort((a, b) => a.id - b.id);
                if (db.products) db.products.sort((a, b) => a.id - b.id);

                const newSize = JSON.stringify(db).length;
                const savedSpace = originalSize - newSize;
                const compressionRatio = ((savedSpace / originalSize) * 100).toFixed(2);

                saveDatabase(db);

                optimizations.push(`تم توفير ${savedSpace} حرف (${compressionRatio}%)`);
                optimizations.push('تم ترتيب البيانات');
                optimizations.push('تم إزالة البيانات غير الضرورية');

                let html = '<div class="success">✓ تم تحسين قاعدة البيانات بنجاح</div>';
                optimizations.forEach(opt => {
                    html += `<div class="info">• ${opt}</div>`;
                });

                resultDiv.innerHTML = html;
                addLog('تم تحسين قاعدة البيانات', 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تحسين قاعدة البيانات: ${error.message}</div>`;
                addLog(`خطأ في تحسين قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        // تنظيف التخزين
        function cleanStorage() {
            const resultDiv = document.getElementById('storage-clean-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري تنظيف التخزين...</div>';

            try {
                let cleaned = [];
                let totalSize = 0;

                // تنظيف localStorage
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith('temp_') || key.startsWith('cache_') || key.includes('_old'))) {
                        const size = localStorage.getItem(key).length;
                        totalSize += size;
                        keysToRemove.push(key);
                    }
                }

                keysToRemove.forEach(key => localStorage.removeItem(key));
                if (keysToRemove.length > 0) {
                    cleaned.push(`تم حذف ${keysToRemove.length} ملف مؤقت`);
                }

                // تنظيف sessionStorage
                const sessionKeysToRemove = [];
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && (key.startsWith('temp_') || key.startsWith('cache_'))) {
                        sessionKeysToRemove.push(key);
                    }
                }

                sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
                if (sessionKeysToRemove.length > 0) {
                    cleaned.push(`تم حذف ${sessionKeysToRemove.length} ملف جلسة مؤقت`);
                }

                let html = '';
                if (cleaned.length === 0) {
                    html = '<div class="success">✓ التخزين نظيف بالفعل</div>';
                } else {
                    html = '<div class="success">✓ تم تنظيف التخزين بنجاح</div>';
                    cleaned.forEach(item => {
                        html += `<div class="info">• ${item}</div>`;
                    });
                    html += `<div class="info">• تم توفير ${totalSize} حرف من المساحة</div>`;
                }

                resultDiv.innerHTML = html;
                addLog(`تم تنظيف التخزين - توفير ${totalSize} حرف`, 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تنظيف التخزين: ${error.message}</div>`;
                addLog(`خطأ في تنظيف التخزين: ${error.message}`, 'error');
            }
        }

        // مسح الذاكرة المؤقتة
        function clearCache() {
            const resultDiv = document.getElementById('storage-clean-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري مسح الذاكرة المؤقتة...</div>';

            try {
                // مسح ذاكرة التخزين المؤقت للمتصفح
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }

                // إعادة تحميل الصفحة لمسح ذاكرة JavaScript
                setTimeout(() => {
                    resultDiv.innerHTML = '<div class="success">✓ تم مسح الذاكرة المؤقتة. سيتم إعادة تحميل الصفحة...</div>';
                    addLog('تم مسح الذاكرة المؤقتة', 'success');

                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }, 1000);

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في مسح الذاكرة المؤقتة: ${error.message}</div>`;
                addLog(`خطأ في مسح الذاكرة المؤقتة: ${error.message}`, 'error');
            }
        }

        // إعادة تعيين النظام
        function resetSystem() {
            if (!confirm('هل أنت متأكد من إعادة تعيين النظام بالكامل؟ سيتم حذف جميع البيانات!')) {
                return;
            }

            const resultDiv = document.getElementById('reset-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري إعادة تعيين النظام...</div>';

            try {
                // حذف جميع البيانات
                localStorage.clear();
                sessionStorage.clear();

                // إنشاء قاعدة بيانات جديدة
                const newDB = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            name: 'مدير النظام'
                        }
                    ],
                    schools: [],
                    products: [],
                    orders: []
                };

                localStorage.setItem('cafeteriaDB', JSON.stringify(newDB));

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إعادة تعيين النظام بنجاح</div>
                    <div class="info">تم إنشاء حساب مدير افتراضي:</div>
                    <div class="info">اسم المستخدم: admin</div>
                    <div class="info">كلمة المرور: admin123</div>
                    <div class="warning">سيتم إعادة تحميل الصفحة خلال 5 ثوان...</div>
                `;

                addLog('تم إعادة تعيين النظام بالكامل', 'warning');

                setTimeout(() => {
                    location.reload();
                }, 5000);

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين النظام: ${error.message}</div>`;
                addLog(`خطأ في إعادة تعيين النظام: ${error.message}`, 'error');
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            const resultDiv = document.getElementById('reset-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري إعادة تعيين الإعدادات...</div>';

            try {
                // إزالة إعدادات المستخدم
                const settingsKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('settings') || key.includes('preferences') || key.includes('config'))) {
                        settingsKeys.push(key);
                    }
                }

                settingsKeys.forEach(key => localStorage.removeItem(key));

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إعادة تعيين الإعدادات بنجاح</div>
                    <div class="info">تم حذف ${settingsKeys.length} إعداد</div>
                `;

                addLog(`تم إعادة تعيين ${settingsKeys.length} إعداد`, 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين الإعدادات: ${error.message}</div>`;
                addLog(`خطأ في إعادة تعيين الإعدادات: ${error.message}`, 'error');
            }
        }

        // مراقبة الأداء
        function monitorPerformance() {
            const resultDiv = document.getElementById('performance-result');

            if (isMonitoring) {
                isMonitoring = false;
                resultDiv.innerHTML = '<div class="warning">تم إيقاف مراقبة الأداء</div>';
                addLog('تم إيقاف مراقبة الأداء', 'info');
                return;
            }

            isMonitoring = true;
            resultDiv.innerHTML = '<div class="info">🔄 بدء مراقبة الأداء...</div>';
            addLog('بدء مراقبة الأداء', 'info');

            const startTime = performance.now();
            let sampleCount = 0;

            const monitor = setInterval(() => {
                if (!isMonitoring) {
                    clearInterval(monitor);
                    return;
                }

                sampleCount++;
                const currentTime = performance.now();
                const memoryInfo = performance.memory ? {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
                } : null;

                performanceData.push({
                    time: currentTime,
                    memory: memoryInfo,
                    sample: sampleCount
                });

                let html = '<div class="success">مراقبة الأداء نشطة</div>';
                html += `<div class="info">العينات المجمعة: ${sampleCount}</div>`;
                html += `<div class="info">وقت التشغيل: ${Math.round((currentTime - startTime) / 1000)} ثانية</div>`;

                if (memoryInfo) {
                    html += `<div class="info">استخدام الذاكرة: ${memoryInfo.used}MB / ${memoryInfo.total}MB</div>`;
                    const memoryUsage = (memoryInfo.used / memoryInfo.total) * 100;
                    html += `<div class="progress-bar"><div class="progress-fill" style="width: ${memoryUsage}%"></div></div>`;
                }

                html += '<button onclick="monitorPerformance()" class="warning">إيقاف المراقبة</button>';
                resultDiv.innerHTML = html;

                // الاحتفاظ بآخر 100 عينة فقط
                if (performanceData.length > 100) {
                    performanceData.shift();
                }
            }, 1000);
        }

        // إنشاء تقرير الأداء
        function generateReport() {
            const resultDiv = document.getElementById('performance-result');

            if (performanceData.length === 0) {
                resultDiv.innerHTML = '<div class="warning">لا توجد بيانات أداء. يرجى بدء المراقبة أولاً.</div>';
                return;
            }

            try {
                const report = {
                    timestamp: new Date().toISOString(),
                    samples: performanceData.length,
                    duration: performanceData.length > 0 ?
                        Math.round((performanceData[performanceData.length - 1].time - performanceData[0].time) / 1000) : 0,
                    averageMemory: performanceData
                        .filter(d => d.memory)
                        .reduce((sum, d) => sum + d.memory.used, 0) /
                        performanceData.filter(d => d.memory).length || 0
                };

                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `performance_report_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إنشاء تقرير الأداء</div>
                    <div class="info">العينات: ${report.samples}</div>
                    <div class="info">المدة: ${report.duration} ثانية</div>
                    <div class="info">متوسط استخدام الذاكرة: ${Math.round(report.averageMemory)}MB</div>
                `;

                addLog(`تم إنشاء تقرير أداء - ${report.samples} عينة`, 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إنشاء التقرير: ${error.message}</div>`;
                addLog(`خطأ في إنشاء تقرير الأداء: ${error.message}`, 'error');
            }
        }

        // إصلاح حسابات المستخدمين
        function fixUserAccounts() {
            const resultDiv = document.getElementById('user-management-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري إصلاح حسابات المستخدمين...</div>';

            try {
                const db = getDatabase();
                let fixed = 0;
                let issues = [];

                if (db.users) {
                    db.users.forEach(user => {
                        // إصلاح الحقول المفقودة
                        if (!user.status) {
                            user.status = 'active';
                            fixed++;
                        }

                        if (user.role === 'staff' && !user.staffType) {
                            user.staffType = 'cashier';
                            fixed++;
                        }

                        if (user.role === 'staff' && !user.permissions) {
                            user.permissions = ['sell', 'view_products'];
                            fixed++;
                        }

                        if (!user.name && user.username) {
                            user.name = user.username;
                            fixed++;
                        }
                    });

                    // التحقق من وجود مدير واحد على الأقل
                    const admins = db.users.filter(u => u.role === 'admin');
                    if (admins.length === 0) {
                        const newAdmin = {
                            id: Math.max(...db.users.map(u => u.id), 0) + 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            name: 'مدير النظام',
                            status: 'active'
                        };
                        db.users.push(newAdmin);
                        fixed++;
                        issues.push('تم إنشاء حساب مدير افتراضي');
                    }
                }

                if (fixed > 0) {
                    saveDatabase(db);
                    issues.push(`تم إصلاح ${fixed} مشكلة في حسابات المستخدمين`);
                }

                let html = '';
                if (fixed === 0) {
                    html = '<div class="success">✓ جميع حسابات المستخدمين سليمة</div>';
                } else {
                    html = '<div class="success">✓ تم إصلاح حسابات المستخدمين</div>';
                    issues.forEach(issue => {
                        html += `<div class="info">• ${issue}</div>`;
                    });
                }

                resultDiv.innerHTML = html;
                addLog(`تم إصلاح ${fixed} مشكلة في حسابات المستخدمين`, 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إصلاح حسابات المستخدمين: ${error.message}</div>`;
                addLog(`خطأ في إصلاح حسابات المستخدمين: ${error.message}`, 'error');
            }
        }

        // إعادة تعيين كلمات المرور
        function resetPasswords() {
            if (!confirm('هل أنت متأكد من إعادة تعيين كلمات المرور؟')) {
                return;
            }

            const resultDiv = document.getElementById('user-management-result');
            resultDiv.innerHTML = '<div class="info">🔄 جاري إعادة تعيين كلمات المرور...</div>';

            try {
                const db = getDatabase();
                let reset = 0;

                if (db.users) {
                    db.users.forEach(user => {
                        if (user.role === 'admin') {
                            user.password = 'admin123';
                        } else if (user.role === 'staff') {
                            user.password = 'staff123';
                        } else {
                            user.password = '123456';
                        }
                        reset++;
                    });

                    saveDatabase(db);
                }

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إعادة تعيين ${reset} كلمة مرور</div>
                    <div class="info">كلمات المرور الافتراضية:</div>
                    <div class="info">• المدراء: admin123</div>
                    <div class="info">• العاملين: staff123</div>
                    <div class="info">• الآخرين: 123456</div>
                `;

                addLog(`تم إعادة تعيين ${reset} كلمة مرور`, 'warning');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين كلمات المرور: ${error.message}</div>`;
                addLog(`خطأ في إعادة تعيين كلمات المرور: ${error.message}`, 'error');
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            try {
                const db = getDatabase();
                const stats = [
                    { label: 'المستخدمين', value: db.users ? db.users.length : 0 },
                    { label: 'المدارس', value: db.schools ? db.schools.length : 0 },
                    { label: 'المنتجات', value: db.products ? db.products.length : 0 },
                    { label: 'الطلبات', value: db.orders ? db.orders.length : 0 },
                    { label: 'العاملين', value: db.users ? db.users.filter(u => u.role === 'staff').length : 0 },
                    { label: 'المدراء', value: db.users ? db.users.filter(u => u.role === 'admin').length : 0 }
                ];

                const statsContainer = document.getElementById('system-stats');
                statsContainer.innerHTML = stats.map(stat => `
                    <div class="stat-item">
                        <div class="stat-number">${stat.value}</div>
                        <div class="stat-label">${stat.label}</div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // تهيئة السجلات
        function initializeLogs() {
            systemLogs = [];
            addLog('تم تهيئة نظام السجلات', 'info');
        }

        // إضافة سجل
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleString('ar-SA');
            systemLogs.push({
                timestamp,
                message,
                type
            });

            // الاحتفاظ بآخر 100 سجل فقط
            if (systemLogs.length > 100) {
                systemLogs.shift();
            }
        }

        // عرض السجلات
        function showLogs() {
            const logViewer = document.getElementById('log-viewer');

            if (systemLogs.length === 0) {
                logViewer.innerHTML = '<div class="log-info">لا توجد سجلات</div>';
                return;
            }

            const logsHtml = systemLogs.map(log =>
                `<div class="log-entry log-${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');

            logViewer.innerHTML = logsHtml;
            logViewer.scrollTop = logViewer.scrollHeight;
        }

        // مسح السجلات
        function clearLogs() {
            systemLogs = [];
            document.getElementById('log-viewer').innerHTML = '<div class="log-info">تم مسح جميع السجلات</div>';
            addLog('تم مسح السجلات', 'warning');
        }
    </script>
</body>
</html>
