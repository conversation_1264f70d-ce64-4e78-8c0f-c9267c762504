<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة تسجيل دخول المدير</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1b5e20;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .step {
            background: #f0f0f0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص مشكلة تسجيل دخول المدير</h1>
            <p>أداة شاملة لتشخيص وإصلاح مشاكل تسجيل دخول المدير في جميع المتصفحات</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="admin-login-quick.html">⚡ اختبار سريع</a>
            <a href="../pages/auth/login.html">🔐 تسجيل الدخول</a>
            <a href="../pages/admin/dashboard.html">📊 لوحة المدير</a>
        </div>

        <div class="test-section">
            <h3>1. فحص قاعدة البيانات</h3>
            <button onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <div id="db-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار تسجيل دخول المدير</h3>
            <input type="text" id="admin-username" placeholder="اسم المستخدم" value="admin">
            <input type="password" id="admin-password" placeholder="كلمة المرور" value="admin123">
            <br>
            <button onclick="testAdminLogin()">اختبار تسجيل الدخول</button>
            <div id="login-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. فحص التوافق مع المتصفح</h3>
            <button onclick="checkBrowserCompatibility()">فحص التوافق</button>
            <div id="browser-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار localStorage و sessionStorage</h3>
            <button onclick="testStorage()">اختبار التخزين</button>
            <div id="storage-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. إصلاح تلقائي للمشاكل</h3>
            <button onclick="autoFix()">إصلاح تلقائي</button>
            <div id="fix-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. اختبار التوجيه</h3>
            <button onclick="testRedirection()">اختبار التوجيه</button>
            <div id="redirect-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>7. إعادة تعيين النظام</h3>
            <button onclick="resetSystem()" style="background: #d32f2f;">إعادة تعيين كاملة</button>
            <div id="reset-result" class="result"></div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../assets/js/main.js"></script>

    <script>
        // متغيرات عامة للتشخيص
        let diagnosticResults = {
            database: null,
            login: null,
            browser: null,
            storage: null
        };

        // فحص قاعدة البيانات
        function checkDatabase() {
            const resultDiv = document.getElementById('db-result');
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري فحص قاعدة البيانات...</div>';

                // فحص وجود دالة getDatabase
                if (typeof window.getDatabase !== 'function') {
                    resultDiv.innerHTML = '<div class="error">✗ دالة getDatabase غير متوفرة</div>';
                    return;
                }

                const db = window.getDatabase();

                if (!db) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير موجودة</div>';
                    return;
                }

                if (!db.users || !Array.isArray(db.users)) {
                    resultDiv.innerHTML = '<div class="error">✗ جدول المستخدمين غير موجود</div>';
                    return;
                }

                // البحث عن المدير
                const admin = db.users.find(u => u.role === 'admin');

                if (!admin) {
                    resultDiv.innerHTML = '<div class="error">✗ حساب المدير غير موجود</div>';
                    return;
                }

                let html = '<div class="success">✓ قاعدة البيانات سليمة</div>';
                html += '<div class="info">تفاصيل حساب المدير:</div>';
                html += `<div class="code-block">ID: ${admin.id}
اسم المستخدم: "${admin.username}"
كلمة المرور: "${admin.password}"
الدور: "${admin.role}"
الاسم: "${admin.name}"</div>`;

                html += `<div class="info">إجمالي المستخدمين: ${db.users.length}</div>`;

                diagnosticResults.database = { status: 'success', admin: admin };
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في فحص قاعدة البيانات: ${error.message}</div>`;
                diagnosticResults.database = { status: 'error', error: error.message };
            }
        }

        // اختبار تسجيل دخول المدير
        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            const username = document.getElementById('admin-username').value;
            const password = document.getElementById('admin-password').value;

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار تسجيل الدخول...</div>';

                // الخطوة 1: فحص قاعدة البيانات
                resultDiv.innerHTML += '<div class="step">الخطوة 1: فحص قاعدة البيانات...</div>';

                const db = window.getDatabase();
                if (!db || !db.users) {
                    resultDiv.innerHTML += '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                resultDiv.innerHTML += '<div class="success">✓ قاعدة البيانات متوفرة</div>';

                // الخطوة 2: البحث عن المستخدم
                resultDiv.innerHTML += '<div class="step">الخطوة 2: البحث عن المستخدم...</div>';

                const user = db.users.find(u => u.username === username && u.role === 'admin');

                if (!user) {
                    resultDiv.innerHTML += '<div class="error">✗ المستخدم غير موجود</div>';
                    resultDiv.innerHTML += `<div class="info">البحث عن: اسم المستخدم="${username}" والدور="admin"</div>`;

                    // عرض جميع المدراء الموجودين
                    const admins = db.users.filter(u => u.role === 'admin');
                    resultDiv.innerHTML += `<div class="info">المدراء الموجودون: ${admins.length}</div>`;
                    admins.forEach(admin => {
                        resultDiv.innerHTML += `<div class="info">- ${admin.username} (${admin.name})</div>`;
                    });
                    return;
                }

                resultDiv.innerHTML += '<div class="success">✓ تم العثور على المستخدم</div>';

                // الخطوة 3: التحقق من كلمة المرور
                resultDiv.innerHTML += '<div class="step">الخطوة 3: التحقق من كلمة المرور...</div>';

                const passwordMatch = (user.password === password);

                if (!passwordMatch) {
                    resultDiv.innerHTML += '<div class="error">✗ كلمة المرور غير صحيحة</div>';
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المحفوظة: "${user.password}"</div>`;
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المدخلة: "${password}"</div>`;
                    return;
                }

                resultDiv.innerHTML += '<div class="success">✓ كلمة المرور صحيحة</div>';

                // الخطوة 4: اختبار تخزين البيانات
                resultDiv.innerHTML += '<div class="step">الخطوة 4: اختبار تخزين البيانات...</div>';

                try {
                    sessionStorage.setItem('testAdmin', JSON.stringify(user));
                    localStorage.setItem('testAdminRole', user.role);
                    resultDiv.innerHTML += '<div class="success">✓ تخزين البيانات يعمل</div>';
                } catch (storageError) {
                    resultDiv.innerHTML += `<div class="error">✗ خطأ في تخزين البيانات: ${storageError.message}</div>`;
                }

                resultDiv.innerHTML += '<div class="success"><strong>🎉 تسجيل الدخول نجح بالكامل!</strong></div>';

                diagnosticResults.login = { status: 'success', user: user };

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في اختبار تسجيل الدخول: ${error.message}</div>`;
                diagnosticResults.login = { status: 'error', error: error.message };
            }
        }

        // فحص التوافق مع المتصفح
        function checkBrowserCompatibility() {
            const resultDiv = document.getElementById('browser-result');

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري فحص التوافق مع المتصفح...</div>';

                let html = '<div class="info">معلومات المتصفح:</div>';
                html += `<div class="code-block">المتصفح: ${navigator.userAgent}
الإصدار: ${navigator.appVersion}
اللغة: ${navigator.language}
Platform: ${navigator.platform}</div>`;

                // فحص الميزات المطلوبة
                const features = {
                    'localStorage': typeof(Storage) !== "undefined" && localStorage,
                    'sessionStorage': typeof(Storage) !== "undefined" && sessionStorage,
                    'JSON': typeof JSON !== "undefined",
                    'fetch': typeof fetch !== "undefined",
                    'Promise': typeof Promise !== "undefined",
                    'async/await': (async () => {})().constructor === Promise,
                    'ES6 Arrow Functions': (() => {}).constructor === Function
                };

                html += '<div class="info">الميزات المدعومة:</div>';
                for (const [feature, supported] of Object.entries(features)) {
                    const status = supported ? '✓' : '✗';
                    const className = supported ? 'success' : 'error';
                    html += `<div class="${className}">${status} ${feature}</div>`;
                }

                diagnosticResults.browser = { status: 'success', features: features };
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في فحص المتصفح: ${error.message}</div>`;
                diagnosticResults.browser = { status: 'error', error: error.message };
            }
        }

        // اختبار التخزين
        function testStorage() {
            const resultDiv = document.getElementById('storage-result');

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار التخزين...</div>';

                let html = '';

                // اختبار localStorage
                try {
                    const testKey = 'test_' + Date.now();
                    const testValue = { test: true, timestamp: Date.now() };

                    localStorage.setItem(testKey, JSON.stringify(testValue));
                    const retrieved = JSON.parse(localStorage.getItem(testKey));
                    localStorage.removeItem(testKey);

                    if (retrieved && retrieved.test === true) {
                        html += '<div class="success">✓ localStorage يعمل بشكل صحيح</div>';
                    } else {
                        html += '<div class="error">✗ localStorage لا يعمل بشكل صحيح</div>';
                    }
                } catch (localError) {
                    html += `<div class="error">✗ خطأ في localStorage: ${localError.message}</div>`;
                }

                // اختبار sessionStorage
                try {
                    const testKey = 'test_' + Date.now();
                    const testValue = { test: true, timestamp: Date.now() };

                    sessionStorage.setItem(testKey, JSON.stringify(testValue));
                    const retrieved = JSON.parse(sessionStorage.getItem(testKey));
                    sessionStorage.removeItem(testKey);

                    if (retrieved && retrieved.test === true) {
                        html += '<div class="success">✓ sessionStorage يعمل بشكل صحيح</div>';
                    } else {
                        html += '<div class="error">✗ sessionStorage لا يعمل بشكل صحيح</div>';
                    }
                } catch (sessionError) {
                    html += `<div class="error">✗ خطأ في sessionStorage: ${sessionError.message}</div>`;
                }

                // فحص البيانات الموجودة
                html += '<div class="info">البيانات المخزنة حالياً:</div>';

                const currentDB = localStorage.getItem('cafeteriaDB');
                if (currentDB) {
                    try {
                        const parsed = JSON.parse(currentDB);
                        html += `<div class="success">✓ قاعدة البيانات موجودة (${Object.keys(parsed).length} جداول)</div>`;
                    } catch (parseError) {
                        html += '<div class="error">✗ قاعدة البيانات تالفة</div>';
                    }
                } else {
                    html += '<div class="warning">⚠ قاعدة البيانات غير موجودة في localStorage</div>';
                }

                diagnosticResults.storage = { status: 'success' };
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في اختبار التخزين: ${error.message}</div>`;
                diagnosticResults.storage = { status: 'error', error: error.message };
            }
        }

        // إصلاح تلقائي
        async function autoFix() {
            const resultDiv = document.getElementById('fix-result');

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري الإصلاح التلقائي...</div>';

                let html = '';

                // الخطوة 1: إعادة تهيئة قاعدة البيانات
                html += '<div class="step">الخطوة 1: إعادة تهيئة قاعدة البيانات...</div>';

                if (typeof window.initializeDatabase === 'function') {
                    await window.initializeDatabase();
                    html += '<div class="success">✓ تم تهيئة قاعدة البيانات</div>';
                } else {
                    // إنشاء قاعدة البيانات يدوياً
                    const defaultDB = {
                        users: [
                            {
                                id: 1,
                                username: 'admin',
                                password: 'admin123',
                                role: 'admin',
                                name: 'مدير النظام'
                            }
                        ],
                        schools: [],
                        products: [],
                        orders: []
                    };

                    localStorage.setItem('cafeteriaDB', JSON.stringify(defaultDB));
                    html += '<div class="success">✓ تم إنشاء قاعدة البيانات الافتراضية</div>';
                }

                // الخطوة 2: مسح البيانات المتضاربة
                html += '<div class="step">الخطوة 2: مسح البيانات المتضاربة...</div>';

                sessionStorage.clear();
                localStorage.removeItem('currentUserRole');
                localStorage.removeItem('currentUserId');
                html += '<div class="success">✓ تم مسح البيانات المتضاربة</div>';

                // الخطوة 3: اختبار تسجيل الدخول
                html += '<div class="step">الخطوة 3: اختبار تسجيل الدخول...</div>';

                const db = window.getDatabase();
                const admin = db.users.find(u => u.role === 'admin');

                if (admin) {
                    html += '<div class="success">✓ حساب المدير جاهز</div>';
                    html += `<div class="info">اسم المستخدم: ${admin.username}</div>`;
                    html += `<div class="info">كلمة المرور: ${admin.password}</div>`;
                } else {
                    html += '<div class="error">✗ لم يتم العثور على حساب المدير</div>';
                }

                html += '<div class="success"><strong>🎉 تم الإصلاح بنجاح!</strong></div>';
                html += '<div class="info">يمكنك الآن المحاولة مرة أخرى</div>';

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في الإصلاح التلقائي: ${error.message}</div>`;
            }
        }

        // اختبار التوجيه
        function testRedirection() {
            const resultDiv = document.getElementById('redirect-result');

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار التوجيه...</div>';

                let html = '';

                // فحص وجود الصفحات المطلوبة
                const pages = [
                    '../pages/admin/dashboard.html',
                    '../pages/admin/staff.html',
                    '../pages/auth/login.html'
                ];

                html += '<div class="info">فحص وجود الصفحات:</div>';

                pages.forEach(page => {
                    html += `<div class="info">📄 ${page}</div>`;
                });

                html += '<div class="step">اختبار التوجيه:</div>';
                html += '<div class="warning">⚠ سيتم اختبار التوجيه لصفحة لوحة التحكم</div>';

                setTimeout(() => {
                    const targetPage = '../pages/admin/dashboard.html';
                    html += `<div class="info">محاولة التوجيه إلى: ${targetPage}</div>`;

                    console.log('Test redirect to:', targetPage);

                    resultDiv.innerHTML = html + '<div class="success">✓ اختبار التوجيه مكتمل</div>';
                }, 1000);

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في اختبار التوجيه: ${error.message}</div>`;
            }
        }

        // إعادة تعيين النظام
        function resetSystem() {
            const resultDiv = document.getElementById('reset-result');

            if (!confirm('هل أنت متأكد من إعادة تعيين النظام بالكامل؟ سيتم حذف جميع البيانات!')) {
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري إعادة تعيين النظام...</div>';

                // مسح جميع البيانات
                localStorage.clear();
                sessionStorage.clear();

                // مسح الكوكيز
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });

                resultDiv.innerHTML = '<div class="success">✓ تم إعادة تعيين النظام بالكامل</div>';
                resultDiv.innerHTML += '<div class="info">يرجى تحديث الصفحة لإعادة تهيئة النظام</div>';

                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين النظام: ${error.message}</div>`;
            }
        }

        // تهيئة الصفحة
        setTimeout(() => {
            console.log('صفحة تشخيص مشكلة تسجيل دخول المدير جاهزة');
        }, 1000);
    </script>
</body>
</html>
