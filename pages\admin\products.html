<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - Smart Canteen</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2e7d32;
            --secondary-color: #ff9800;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --info-color: #2196f3;
            --text-color: #333;
            --text-muted: #666;
            --bg-light: #f8f9fa;
            --border-color: #e0e0e0;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            color: var(--text-color);
            min-height: 100vh;
        }

        /* الهيدر */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            min-height: 50px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* لوجو الشركة */
        .company-logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
        }

        .company-name {
            display: flex;
            flex-direction: column;
            line-height: 1;
        }

        .company-name-en {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: Arial, sans-serif;
        }

        .company-name-ar {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-muted);
        }

        .page-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-info {
            text-align: right;
            line-height: 1.2;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.8rem;
            color: var(--text-color);
        }

        .user-role {
            font-size: 0.7rem;
            color: var(--text-muted);
        }

        .logout-btn {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 4px;
            border: none;
            cursor: pointer;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }

        /* المحتوى الرئيسي */
        .main-content {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* شريط الأدوات */
        .toolbar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .search-box {
            position: relative;
            min-width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(46, 125, 50, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid var(--border-color);
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .add-product-btn {
            background: linear-gradient(135deg, var(--primary-color), #4caf50);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-product-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
        }

        /* شبكة المنتجات */
        .products-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .products-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .products-count {
            background: var(--info-color);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }

        /* بطاقة المنتج */
        .product-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .product-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            box-shadow: 0 3px 12px rgba(46, 125, 50, 0.3);
        }

        .product-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .edit-btn {
            background: rgba(33, 150, 243, 0.1);
            color: var(--info-color);
        }

        .edit-btn:hover {
            background: var(--info-color);
            color: white;
            transform: scale(1.1);
        }

        .delete-btn {
            background: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        .delete-btn:hover {
            background: var(--danger-color);
            color: white;
            transform: scale(1.1);
        }

        .product-info {
            margin-bottom: 10px;
            flex: 1;
        }

        .product-name {
            font-size: 1rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .product-category {
            font-size: 0.75rem;
            color: var(--text-muted);
            background: var(--bg-light);
            padding: 2px 8px;
            border-radius: 8px;
            display: inline-block;
        }

        .product-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .detail-item {
            text-align: center;
            padding: 6px;
            background: rgba(46, 125, 50, 0.05);
            border-radius: 8px;
        }

        .detail-label {
            font-size: 0.7rem;
            color: var(--text-muted);
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .popular-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b35, #ff8e53);
            color: white;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .stock-status {
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            margin-top: auto;
        }

        .stock-available {
            background: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
        }

        .stock-low {
            background: rgba(255, 152, 0, 0.1);
            color: var(--warning-color);
        }

        .stock-out {
            background: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        /* نافذة إضافة منتج جديد */
        .add-product-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.85);
            backdrop-filter: blur(10px);
            z-index: 2100;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.4s ease;
        }

        .add-product-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 35px;
            width: 95%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 80px rgba(0,0,0,0.4);
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: 3px solid var(--primary-color);
        }

        .add-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid var(--border-color);
        }

        .add-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .add-subtitle {
            font-size: 1rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        .add-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group-full {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-input {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(46, 125, 50, 0.1);
            transform: translateY(-2px);
        }

        .form-input.error {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.1);
        }

        .error-message {
            color: var(--danger-color);
            font-size: 0.8rem;
            margin-top: 5px;
            display: none;
        }

        .emoji-selector {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            padding: 15px;
            background: rgba(46, 125, 50, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(46, 125, 50, 0.1);
        }

        .emoji-option {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .emoji-option:hover {
            background: rgba(46, 125, 50, 0.1);
            transform: scale(1.1);
        }

        .emoji-option.selected,
        .edit-emoji-option.selected {
            background: var(--primary-color);
            border-color: var(--secondary-color);
            transform: scale(1.2);
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }

        .edit-emoji-option {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .edit-emoji-option:hover {
            background: rgba(46, 125, 50, 0.1);
            transform: scale(1.1);
        }

        .category-selector {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .category-option {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            background: white;
        }

        .category-option:hover {
            border-color: var(--primary-color);
            background: rgba(46, 125, 50, 0.05);
        }

        .category-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .popular-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(255, 107, 53, 0.05);
            border-radius: 12px;
            border: 2px solid rgba(255, 107, 53, 0.1);
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: #ccc;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #ff6b35;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(25px);
        }

        .add-actions {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .add-btn-action {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .add-btn-save {
            background: linear-gradient(135deg, var(--primary-color), #4caf50);
            color: white;
        }

        .add-btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
        }

        .add-btn-cancel {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .add-btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }

        .close-add-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.8rem;
            color: var(--text-muted);
            cursor: pointer;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-add-btn:hover {
            color: var(--danger-color);
            background: rgba(244, 67, 54, 0.1);
            transform: scale(1.1);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="header-left">
            <!-- لوجو الشركة -->
            <div class="company-logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="company-name">
                    <div class="company-name-en">Smart Canteen</div>
                    <div class="company-name-ar">سمارت مقصف</div>
                </div>
            </div>

            <h1 class="page-title">
                <i class="fas fa-boxes"></i>
                إدارة المنتجات
            </h1>
        </div>
        <div class="header-right">
            <div class="user-info">
                <div class="user-name" id="user-name">المدير</div>
                <div class="user-role" id="user-role">مدير النظام</div>
            </div>
            <button onclick="logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </button>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- شريط الأدوات -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="search-box">
                    <input type="text" class="search-input" id="search-input" placeholder="البحث في المنتجات...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">الكل</button>
                    <button class="filter-btn" data-category="meals">وجبات</button>
                    <button class="filter-btn" data-category="drinks">مشروبات</button>
                    <button class="filter-btn" data-category="snacks">وجبات خفيفة</button>
                    <button class="filter-btn" data-category="sweets">حلويات</button>
                </div>
            </div>

            <button class="add-product-btn" onclick="openAddModal()">
                <i class="fas fa-plus"></i>
                إضافة منتج جديد
            </button>
        </div>

        <!-- حاوية المنتجات -->
        <div class="products-container">
            <div class="products-header">
                <div class="products-title">
                    <i class="fas fa-list"></i>
                    قائمة المنتجات
                </div>
                <div class="products-count" id="products-count">0 منتج</div>
            </div>

            <div class="products-grid" id="products-grid">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </div>
    </main>

    <!-- نافذة إضافة منتج جديد -->
    <div id="add-product-modal" class="add-product-modal" style="display: none;">
        <div class="add-product-content">
            <button class="close-add-btn" onclick="closeAddModal()">
                <i class="fas fa-times"></i>
            </button>

            <div class="add-header">
                <div class="add-title">
                    <i class="fas fa-plus-circle"></i>
                    إضافة منتج جديد
                </div>
                <div class="add-subtitle">أضف منتج جديد إلى المقصف بجميع التفاصيل</div>
            </div>

            <form class="add-form" id="add-product-form">
                <!-- اسم المنتج -->
                <div class="form-group">
                    <label class="form-label" for="add-product-name">
                        <i class="fas fa-tag"></i>
                        اسم المنتج
                    </label>
                    <input type="text"
                           id="add-product-name"
                           class="form-input"
                           placeholder="مثال: ساندويش فلافل"
                           required>
                    <div class="error-message" id="add-name-error">يرجى إدخال اسم صحيح للمنتج</div>
                </div>

                <!-- السعر -->
                <div class="form-group">
                    <label class="form-label" for="add-product-price">
                        <i class="fas fa-money-bill-wave"></i>
                        السعر (ريال)
                    </label>
                    <input type="number"
                           id="add-product-price"
                           class="form-input"
                           placeholder="0.00"
                           min="0.01"
                           step="0.01"
                           required>
                    <div class="error-message" id="add-price-error">يرجى إدخال سعر صحيح أكبر من صفر</div>
                </div>

                <!-- المخزون -->
                <div class="form-group">
                    <label class="form-label" for="add-product-stock">
                        <i class="fas fa-boxes"></i>
                        الكمية المتوفرة
                    </label>
                    <input type="number"
                           id="add-product-stock"
                           class="form-input"
                           placeholder="0"
                           min="0"
                           step="1"
                           required>
                    <div class="error-message" id="add-stock-error">يرجى إدخال كمية صحيحة</div>
                </div>

                <!-- الفئة -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-list"></i>
                        الفئة
                    </label>
                    <div class="category-selector">
                        <div class="category-option" data-category="meals">
                            <i class="fas fa-hamburger"></i>
                            وجبات
                        </div>
                        <div class="category-option" data-category="drinks">
                            <i class="fas fa-glass-whiskey"></i>
                            مشروبات
                        </div>
                        <div class="category-option" data-category="snacks">
                            <i class="fas fa-cookie-bite"></i>
                            وجبات خفيفة
                        </div>
                        <div class="category-option" data-category="sweets">
                            <i class="fas fa-birthday-cake"></i>
                            حلويات
                        </div>
                    </div>
                    <div class="error-message" id="add-category-error">يرجى اختيار فئة للمنتج</div>
                </div>

                <!-- اختيار الإيموجي -->
                <div class="form-group-full">
                    <label class="form-label">
                        <i class="fas fa-smile"></i>
                        اختر أيقونة المنتج
                    </label>
                    <div class="emoji-selector" id="emoji-selector">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                    <div class="error-message" id="add-emoji-error">يرجى اختيار أيقونة للمنتج</div>
                </div>

                <!-- منتج شائع -->
                <div class="form-group-full">
                    <label class="form-label">
                        <i class="fas fa-fire"></i>
                        منتج شائع
                    </label>
                    <div class="popular-toggle">
                        <div class="toggle-switch" id="popular-toggle">
                            <div class="toggle-slider"></div>
                        </div>
                        <span>تمييز هذا المنتج كمنتج شائع مع شارة النار 🔥</span>
                    </div>
                </div>

                <div class="add-actions">
                    <button type="submit" class="add-btn-action add-btn-save">
                        <i class="fas fa-plus"></i>
                        إضافة المنتج
                    </button>
                    <button type="button" class="add-btn-action add-btn-cancel" onclick="closeAddModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل منتج -->
    <div id="edit-product-modal" class="add-product-modal" style="display: none;">
        <div class="add-product-content">
            <button class="close-add-btn" onclick="closeEditModal()">
                <i class="fas fa-times"></i>
            </button>

            <div class="add-header">
                <div class="add-title">
                    <i class="fas fa-edit"></i>
                    تعديل المنتج
                </div>
                <div class="add-subtitle">قم بتعديل تفاصيل المنتج</div>
            </div>

            <form class="add-form edit-form" id="edit-product-form">
                <!-- اسم المنتج -->
                <div class="form-group">
                    <label class="form-label" for="edit-product-name">
                        <i class="fas fa-tag"></i>
                        اسم المنتج
                    </label>
                    <input type="text"
                           id="edit-product-name"
                           class="form-input"
                           placeholder="مثال: ساندويش فلافل"
                           required>
                    <div class="error-message" id="edit-name-error">يرجى إدخال اسم صحيح للمنتج</div>
                </div>

                <!-- السعر -->
                <div class="form-group">
                    <label class="form-label" for="edit-product-price">
                        <i class="fas fa-money-bill-wave"></i>
                        السعر (ريال)
                    </label>
                    <input type="number"
                           id="edit-product-price"
                           class="form-input"
                           placeholder="0.00"
                           min="0.01"
                           step="0.01"
                           required>
                    <div class="error-message" id="edit-price-error">يرجى إدخال سعر صحيح أكبر من صفر</div>
                </div>

                <!-- المخزون -->
                <div class="form-group">
                    <label class="form-label" for="edit-product-stock">
                        <i class="fas fa-boxes"></i>
                        الكمية المتوفرة
                    </label>
                    <input type="number"
                           id="edit-product-stock"
                           class="form-input"
                           placeholder="0"
                           min="0"
                           step="1"
                           required>
                    <div class="error-message" id="edit-stock-error">يرجى إدخال كمية صحيحة</div>
                </div>

                <!-- الفئة -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-list"></i>
                        الفئة
                    </label>
                    <div class="category-selector">
                        <div class="category-option edit-category-option" data-category="meals">
                            <i class="fas fa-hamburger"></i>
                            وجبات
                        </div>
                        <div class="category-option edit-category-option" data-category="drinks">
                            <i class="fas fa-glass-whiskey"></i>
                            مشروبات
                        </div>
                        <div class="category-option edit-category-option" data-category="snacks">
                            <i class="fas fa-cookie-bite"></i>
                            وجبات خفيفة
                        </div>
                        <div class="category-option edit-category-option" data-category="sweets">
                            <i class="fas fa-birthday-cake"></i>
                            حلويات
                        </div>
                    </div>
                    <div class="error-message" id="edit-category-error">يرجى اختيار فئة للمنتج</div>
                </div>

                <!-- اختيار الإيموجي -->
                <div class="form-group-full">
                    <label class="form-label">
                        <i class="fas fa-smile"></i>
                        اختر أيقونة المنتج
                    </label>
                    <div class="emoji-selector" id="edit-emoji-selector">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                    <div class="error-message" id="edit-emoji-error">يرجى اختيار أيقونة للمنتج</div>
                </div>

                <!-- منتج شائع -->
                <div class="form-group-full">
                    <label class="form-label">
                        <i class="fas fa-fire"></i>
                        منتج شائع
                    </label>
                    <div class="popular-toggle">
                        <div class="toggle-switch" id="edit-popular-toggle">
                            <div class="toggle-slider"></div>
                        </div>
                        <span>تمييز هذا المنتج كمنتج شائع مع شارة النار 🔥</span>
                    </div>
                </div>

                <div class="add-actions">
                    <button type="submit" class="add-btn-action add-btn-save">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <button type="button" class="add-btn-action add-btn-cancel" onclick="closeEditModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let products = [];
        let filteredProducts = [];

        // تحميل المنتجات
        function loadProducts() {
            // محاولة تحميل المنتجات من localStorage أولاً
            const savedProducts = localStorage.getItem('canteenProducts');

            if (savedProducts) {
                products = JSON.parse(savedProducts);
                console.log('تم تحميل المنتجات من التخزين المحلي:', products.length, 'منتج');
            } else {
                // بيانات تجريبية للمنتجات (30 منتج مقصف مدرسي)
                products = [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, icon: '🥙', isPopular: true },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, icon: '🧀', isPopular: false },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, icon: '🐟', isPopular: false },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, icon: '🍗', isPopular: false },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, icon: '🥗', isPopular: false },
                { id: 6, name: 'بيتزا صغيرة', price: 12.00, category: 'meals', stock: 6, icon: '🍕', isPopular: true },

                // المشروبات
                { id: 7, name: 'عصير برتقال', price: 5.00, category: 'drinks', stock: 20, icon: '🍊', isPopular: true },
                { id: 8, name: 'عصير تفاح', price: 5.50, category: 'drinks', stock: 15, icon: '🍎', isPopular: true },
                { id: 9, name: 'عصير مانجو', price: 6.00, category: 'drinks', stock: 18, icon: '🥭', isPopular: true },
                { id: 10, name: 'ماء', price: 2.00, category: 'drinks', stock: 30, icon: '💧', isPopular: true },
                { id: 11, name: 'مياه غازية', price: 3.00, category: 'drinks', stock: 22, icon: '🥤', isPopular: true },
                { id: 12, name: 'قهوة باردة', price: 7.00, category: 'drinks', stock: 13, icon: '☕', isPopular: false },
                { id: 13, name: 'شاي مثلج', price: 4.50, category: 'drinks', stock: 16, icon: '🧊', isPopular: false },

                // الوجبات الخفيفة والمقرمشات
                { id: 14, name: 'مرامي', price: 2.50, category: 'snacks', stock: 25, icon: '🟡', isPopular: true },
                { id: 15, name: 'قشار', price: 3.00, category: 'snacks', stock: 20, icon: '🟠', isPopular: true },
                { id: 16, name: 'بطل', price: 2.00, category: 'snacks', stock: 30, icon: '🔴', isPopular: true },
                { id: 17, name: 'أفخاذ', price: 4.00, category: 'snacks', stock: 18, icon: '🟤', isPopular: true },
                { id: 18, name: 'شيبس', price: 3.50, category: 'snacks', stock: 25, icon: '🍟', isPopular: true },
                { id: 19, name: 'بسكويت', price: 4.50, category: 'snacks', stock: 18, icon: '🍪', isPopular: true },
                { id: 20, name: 'كرواسون', price: 6.00, category: 'snacks', stock: 14, icon: '🥐', isPopular: true },
                { id: 21, name: 'كعك محلى', price: 5.50, category: 'snacks', stock: 16, icon: '🥨', isPopular: true },
                { id: 22, name: 'مكسرات', price: 8.00, category: 'snacks', stock: 12, icon: '🥜', isPopular: false },
                { id: 23, name: 'فشار', price: 3.50, category: 'snacks', stock: 20, icon: '🍿', isPopular: true },

                // الحلويات
                { id: 24, name: 'كيك شوكولاتة', price: 12.00, category: 'sweets', stock: 8, icon: '🍰', isPopular: false },
                { id: 25, name: 'دونات', price: 6.50, category: 'sweets', stock: 10, icon: '🍩', isPopular: false },
                { id: 26, name: 'مافين', price: 7.50, category: 'sweets', stock: 12, icon: '🧁', isPopular: false },
                { id: 27, name: 'كيك فانيليا', price: 11.00, category: 'sweets', stock: 6, icon: '🎂', isPopular: false },
                { id: 28, name: 'آيس كريم', price: 8.00, category: 'sweets', stock: 9, icon: '🍦', isPopular: true },
                { id: 29, name: 'شوكولاتة', price: 5.00, category: 'sweets', stock: 15, icon: '🍫', isPopular: true },
                { id: 30, name: 'حلوى جيلي', price: 4.00, category: 'sweets', stock: 18, icon: '🍬', isPopular: false }
                ];

                // حفظ البيانات الأولية في localStorage
                saveProductsToStorage();
                console.log('تم إنشاء وحفظ البيانات الأولية:', products.length, 'منتج');
            }

            filteredProducts = [...products];
            displayProducts();
            updateProductsCount();
        }

        // حفظ المنتجات في localStorage
        function saveProductsToStorage() {
            localStorage.setItem('canteenProducts', JSON.stringify(products));
            console.log('تم حفظ المنتجات في التخزين المحلي');
        }

        // عرض المنتجات
        function displayProducts() {
            const productsGrid = document.getElementById('products-grid');

            if (filteredProducts.length === 0) {
                productsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: var(--text-muted);">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                        <h3>لا توجد منتجات</h3>
                        <p>لم يتم العثور على منتجات تطابق البحث</p>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = filteredProducts.map(product => {
                const stockStatus = getStockStatus(product.stock);
                const categoryName = getCategoryName(product.category);

                return `
                    <div class="product-card">
                        ${product.isPopular ? '<div class="popular-badge"><i class="fas fa-fire"></i></div>' : ''}

                        <div class="product-header">
                            <div class="product-icon">
                                ${product.icon}
                            </div>
                            <div class="product-actions">
                                <button class="action-btn edit-btn" onclick="editProduct(${product.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete-btn" onclick="deleteProduct(${product.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="product-info">
                            <div class="product-name">${product.name}</div>
                            <div class="product-category">${categoryName}</div>
                        </div>

                        <div class="product-details">
                            <div class="detail-item">
                                <div class="detail-label">السعر</div>
                                <div class="detail-value">${product.price.toFixed(2)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">المخزون</div>
                                <div class="detail-value">${product.stock}</div>
                            </div>
                        </div>

                        <div class="stock-status ${stockStatus.class}">
                            <i class="${stockStatus.icon}"></i>
                            ${stockStatus.text}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // تحديد حالة المخزون
        function getStockStatus(stock) {
            if (stock === 0) {
                return { class: 'stock-out', icon: 'fas fa-times-circle', text: 'نفد المخزون' };
            } else if (stock <= 5) {
                return { class: 'stock-low', icon: 'fas fa-exclamation-triangle', text: 'مخزون منخفض' };
            } else {
                return { class: 'stock-available', icon: 'fas fa-check-circle', text: 'متوفر' };
            }
        }

        // الحصول على اسم الفئة
        function getCategoryName(category) {
            const categories = {
                'meals': 'وجبات',
                'drinks': 'مشروبات',
                'snacks': 'وجبات خفيفة',
                'sweets': 'حلويات'
            };
            return categories[category] || category;
        }

        // تحديث عداد المنتجات
        function updateProductsCount() {
            const count = filteredProducts.length;
            document.getElementById('products-count').textContent = `${count} منتج`;
        }

        // البحث في المنتجات
        function searchProducts(searchTerm) {
            const term = searchTerm.toLowerCase();
            filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(term) ||
                getCategoryName(product.category).toLowerCase().includes(term)
            );
            displayProducts();
            updateProductsCount();
        }

        // فلترة المنتجات حسب الفئة
        function filterByCategory(category) {
            if (category === 'all') {
                filteredProducts = [...products];
            } else {
                filteredProducts = products.filter(product => product.category === category);
            }
            displayProducts();
            updateProductsCount();
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // البحث
            document.getElementById('search-input').addEventListener('input', function() {
                searchProducts(this.value);
            });

            // فلاتر الفئات
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    const category = this.dataset.category;
                    filterByCategory(category);
                });
            });
        }

        // ===== وظائف إضافة المنتجات =====

        // قائمة الإيموجي المتاحة
        const availableEmojis = [
            '🥙', '🍔', '🌭', '🥪', '🌮', '🌯', '🍕', '🍗', '🍖', '🥓',
            '🍊', '🍎', '🍌', '🥭', '🍇', '🍓', '🫐', '🍒', '🥤', '🧃',
            '🍟', '🍪', '🥐', '🥨', '🥜', '🍿', '🥯', '🧈', '🍞', '🥖',
            '🍰', '🧁', '🍩', '🍫', '🍬', '🍭', '🎂', '🍮', '🍦', '🍨',
            '💧', '☕', '🧊', '🥛', '🍵', '🧋', '🥃', '🍷', '🍺', '🥂'
        ];

        let selectedCategory = '';
        let selectedEmoji = '';
        let isPopular = false;

        // فتح نافذة إضافة المنتج
        function openAddModal() {
            // إعادة تعيين النموذج
            resetAddForm();

            // ملء قائمة الإيموجي
            populateEmojiSelector();

            // عرض النافذة
            document.getElementById('add-product-modal').style.display = 'flex';

            // التركيز على حقل الاسم
            setTimeout(() => {
                document.getElementById('add-product-name').focus();
            }, 100);
        }

        // إغلاق نافذة الإضافة
        function closeAddModal() {
            document.getElementById('add-product-modal').style.display = 'none';
            resetAddForm();
        }

        // إعادة تعيين النموذج
        function resetAddForm() {
            document.getElementById('add-product-form').reset();
            selectedCategory = '';
            selectedEmoji = '';
            isPopular = false;

            // إزالة التحديدات
            document.querySelectorAll('.category-option').forEach(option => {
                option.classList.remove('selected');
            });

            document.querySelectorAll('.emoji-option').forEach(option => {
                option.classList.remove('selected');
            });

            document.getElementById('popular-toggle').classList.remove('active');

            // إزالة رسائل الخطأ
            clearAddErrors();
        }

        // ملء قائمة الإيموجي
        function populateEmojiSelector() {
            const emojiSelector = document.getElementById('emoji-selector');
            emojiSelector.innerHTML = availableEmojis.map(emoji => `
                <div class="emoji-option" onclick="selectEmoji('${emoji}')" title="${emoji}">
                    ${emoji}
                </div>
            `).join('');
        }

        // اختيار إيموجي
        function selectEmoji(emoji) {
            selectedEmoji = emoji;

            // إزالة التحديد السابق
            document.querySelectorAll('.emoji-option').forEach(option => {
                option.classList.remove('selected');
            });

            // إضافة التحديد الجديد
            event.target.classList.add('selected');

            // إزالة رسالة الخطأ
            document.getElementById('add-emoji-error').style.display = 'none';
        }

        // مسح رسائل الخطأ
        function clearAddErrors() {
            const errorElements = [
                'add-name-error', 'add-price-error', 'add-stock-error',
                'add-category-error', 'add-emoji-error'
            ];

            errorElements.forEach(id => {
                document.getElementById(id).style.display = 'none';
            });

            // إزالة فئة الخطأ من الحقول
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error');
            });
        }

        // التحقق من صحة نموذج الإضافة
        function validateAddForm() {
            const name = document.getElementById('add-product-name').value.trim();
            const price = parseFloat(document.getElementById('add-product-price').value);
            const stock = parseInt(document.getElementById('add-product-stock').value);
            let isValid = true;

            // التحقق من الاسم
            if (!name || name.length < 2) {
                document.getElementById('add-product-name').classList.add('error');
                document.getElementById('add-name-error').style.display = 'block';
                isValid = false;
            } else {
                // التحقق من عدم تكرار الاسم
                const existingProduct = products.find(p =>
                    p.name.toLowerCase() === name.toLowerCase()
                );
                if (existingProduct) {
                    document.getElementById('add-product-name').classList.add('error');
                    document.getElementById('add-name-error').textContent = 'هذا الاسم موجود بالفعل';
                    document.getElementById('add-name-error').style.display = 'block';
                    isValid = false;
                }
            }

            // التحقق من السعر
            if (!price || price <= 0 || isNaN(price)) {
                document.getElementById('add-product-price').classList.add('error');
                document.getElementById('add-price-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من المخزون
            if (isNaN(stock) || stock < 0) {
                document.getElementById('add-product-stock').classList.add('error');
                document.getElementById('add-stock-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من الفئة
            if (!selectedCategory) {
                document.getElementById('add-category-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من الإيموجي
            if (!selectedEmoji) {
                document.getElementById('add-emoji-error').style.display = 'block';
                isValid = false;
            }

            return isValid;
        }

        // حفظ المنتج الجديد
        function saveNewProduct() {
            if (!validateAddForm()) return;

            const name = document.getElementById('add-product-name').value.trim();
            const price = parseFloat(document.getElementById('add-product-price').value);
            const stock = parseInt(document.getElementById('add-product-stock').value);

            // إنشاء ID جديد
            const newId = Math.max(...products.map(p => p.id)) + 1;

            // إنشاء المنتج الجديد
            const newProduct = {
                id: newId,
                name: name,
                price: price,
                category: selectedCategory,
                stock: stock,
                icon: selectedEmoji,
                isPopular: isPopular
            };

            // إضافة المنتج إلى القائمة
            products.push(newProduct);
            filteredProducts = [...products];

            // حفظ في localStorage
            saveProductsToStorage();

            // تحديث العرض
            displayProducts();
            updateProductsCount();

            // إغلاق النافذة
            closeAddModal();

            // عرض رسالة نجاح
            alert(`تم إضافة ${name} بنجاح! السعر: ${price.toFixed(2)} ريال`);

            console.log('تم إضافة منتج جديد:', newProduct);
        }

        // إعداد مستمعي أحداث نافذة الإضافة
        function setupAddModalListeners() {
            // إرسال النموذج
            document.getElementById('add-product-form').addEventListener('submit', function(e) {
                e.preventDefault();
                saveNewProduct();
            });

            // اختيار الفئة
            document.querySelectorAll('.category-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedCategory = this.dataset.category;

                    // إزالة التحديد السابق
                    document.querySelectorAll('.category-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // إضافة التحديد الجديد
                    this.classList.add('selected');

                    // إزالة رسالة الخطأ
                    document.getElementById('add-category-error').style.display = 'none';
                });
            });

            // تبديل الشعبية
            document.getElementById('popular-toggle').addEventListener('click', function() {
                isPopular = !isPopular;
                this.classList.toggle('active', isPopular);
            });

            // إغلاق النافذة بالضغط على الخلفية
            document.getElementById('add-product-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeAddModal();
                }
            });

            // إغلاق النافذة بمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.getElementById('add-product-modal').style.display === 'flex') {
                    closeAddModal();
                }
            });

            // التحقق الفوري من البيانات
            document.getElementById('add-product-name').addEventListener('input', function() {
                if (this.value.trim().length >= 2) {
                    this.classList.remove('error');
                    document.getElementById('add-name-error').style.display = 'none';
                }
            });

            document.getElementById('add-product-price').addEventListener('input', function() {
                const price = parseFloat(this.value);
                if (price > 0 && !isNaN(price)) {
                    this.classList.remove('error');
                    document.getElementById('add-price-error').style.display = 'none';
                }
            });

            document.getElementById('add-product-stock').addEventListener('input', function() {
                const stock = parseInt(this.value);
                if (!isNaN(stock) && stock >= 0) {
                    this.classList.remove('error');
                    document.getElementById('add-stock-error').style.display = 'none';
                }
            });
        }

        // ===== وظائف تعديل المنتجات =====

        let currentEditingProduct = null;

        // فتح نافذة تعديل المنتج
        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            currentEditingProduct = product;

            // ملء البيانات في النموذج
            document.getElementById('edit-product-name').value = product.name;
            document.getElementById('edit-product-price').value = product.price.toFixed(2);
            document.getElementById('edit-product-stock').value = product.stock;

            // تحديد الفئة
            selectedEditCategory = product.category;
            document.querySelectorAll('.edit-category-option').forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.category === product.category) {
                    option.classList.add('selected');
                }
            });

            // تحديد الإيموجي
            selectedEditEmoji = product.icon;
            populateEditEmojiSelector();
            setTimeout(() => {
                document.querySelectorAll('.edit-emoji-option').forEach(option => {
                    if (option.textContent === product.icon) {
                        option.classList.add('selected');
                    }
                });
            }, 100);

            // تحديد الشعبية
            isEditPopular = product.isPopular;
            document.getElementById('edit-popular-toggle').classList.toggle('active', product.isPopular);

            // إزالة رسائل الخطأ السابقة
            clearEditErrors();

            // عرض النافذة
            document.getElementById('edit-product-modal').style.display = 'flex';

            // التركيز على حقل الاسم
            setTimeout(() => {
                document.getElementById('edit-product-name').focus();
                document.getElementById('edit-product-name').select();
            }, 100);
        }

        // إغلاق نافذة التعديل
        function closeEditModal() {
            document.getElementById('edit-product-modal').style.display = 'none';
            currentEditingProduct = null;
            clearEditErrors();
            document.getElementById('edit-product-form').reset();
        }

        // متغيرات التعديل
        let selectedEditCategory = '';
        let selectedEditEmoji = '';
        let isEditPopular = false;

        // ملء قائمة الإيموجي للتعديل
        function populateEditEmojiSelector() {
            const emojiSelector = document.getElementById('edit-emoji-selector');
            emojiSelector.innerHTML = availableEmojis.map(emoji => `
                <div class="edit-emoji-option" onclick="selectEditEmoji('${emoji}')" title="${emoji}">
                    ${emoji}
                </div>
            `).join('');
        }

        // اختيار إيموجي للتعديل
        function selectEditEmoji(emoji) {
            selectedEditEmoji = emoji;

            // إزالة التحديد السابق
            document.querySelectorAll('.edit-emoji-option').forEach(option => {
                option.classList.remove('selected');
            });

            // إضافة التحديد الجديد
            event.target.classList.add('selected');

            // إزالة رسالة الخطأ
            document.getElementById('edit-emoji-error').style.display = 'none';
        }

        // مسح رسائل الخطأ للتعديل
        function clearEditErrors() {
            const errorElements = [
                'edit-name-error', 'edit-price-error', 'edit-stock-error',
                'edit-category-error', 'edit-emoji-error'
            ];

            errorElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.style.display = 'none';
            });

            // إزالة فئة الخطأ من الحقول
            document.querySelectorAll('.edit-form .form-input').forEach(input => {
                input.classList.remove('error');
            });
        }

        // التحقق من صحة نموذج التعديل
        function validateEditForm() {
            const name = document.getElementById('edit-product-name').value.trim();
            const price = parseFloat(document.getElementById('edit-product-price').value);
            const stock = parseInt(document.getElementById('edit-product-stock').value);
            let isValid = true;

            // التحقق من الاسم
            if (!name || name.length < 2) {
                document.getElementById('edit-product-name').classList.add('error');
                document.getElementById('edit-name-error').style.display = 'block';
                isValid = false;
            } else {
                // التحقق من عدم تكرار الاسم (باستثناء المنتج الحالي)
                const existingProduct = products.find(p =>
                    p.id !== currentEditingProduct.id &&
                    p.name.toLowerCase() === name.toLowerCase()
                );
                if (existingProduct) {
                    document.getElementById('edit-product-name').classList.add('error');
                    document.getElementById('edit-name-error').textContent = 'هذا الاسم موجود بالفعل';
                    document.getElementById('edit-name-error').style.display = 'block';
                    isValid = false;
                }
            }

            // التحقق من السعر
            if (!price || price <= 0 || isNaN(price)) {
                document.getElementById('edit-product-price').classList.add('error');
                document.getElementById('edit-price-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من المخزون
            if (isNaN(stock) || stock < 0) {
                document.getElementById('edit-product-stock').classList.add('error');
                document.getElementById('edit-stock-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من الفئة
            if (!selectedEditCategory) {
                document.getElementById('edit-category-error').style.display = 'block';
                isValid = false;
            }

            // التحقق من الإيموجي
            if (!selectedEditEmoji) {
                document.getElementById('edit-emoji-error').style.display = 'block';
                isValid = false;
            }

            return isValid;
        }

        // حفظ تعديلات المنتج
        function saveEditedProduct() {
            if (!validateEditForm() || !currentEditingProduct) return;

            const name = document.getElementById('edit-product-name').value.trim();
            const price = parseFloat(document.getElementById('edit-product-price').value);
            const stock = parseInt(document.getElementById('edit-product-stock').value);

            // تحديث المنتج
            currentEditingProduct.name = name;
            currentEditingProduct.price = price;
            currentEditingProduct.stock = stock;
            currentEditingProduct.category = selectedEditCategory;
            currentEditingProduct.icon = selectedEditEmoji;
            currentEditingProduct.isPopular = isEditPopular;

            // حفظ في localStorage
            saveProductsToStorage();

            // تحديث العرض
            displayProducts();
            updateProductsCount();

            // إغلاق النافذة
            closeEditModal();

            // عرض رسالة نجاح
            alert(`تم تحديث ${name} بنجاح! السعر الجديد: ${price.toFixed(2)} ريال`);

            console.log('تم تحديث المنتج:', currentEditingProduct);
        }

        function deleteProduct(id) {
            const product = products.find(p => p.id === id);
            if (confirm(`هل أنت متأكد من حذف المنتج: ${product.name}؟`)) {
                products = products.filter(p => p.id !== id);
                filteredProducts = filteredProducts.filter(p => p.id !== id);

                // حفظ في localStorage
                saveProductsToStorage();

                displayProducts();
                updateProductsCount();
                alert('تم حذف المنتج بنجاح');
            }
        }

        // إعداد مستمعي أحداث نافذة التعديل
        function setupEditModalListeners() {
            // إرسال النموذج
            document.getElementById('edit-product-form').addEventListener('submit', function(e) {
                e.preventDefault();
                saveEditedProduct();
            });

            // اختيار الفئة للتعديل
            document.querySelectorAll('.edit-category-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectedEditCategory = this.dataset.category;

                    // إزالة التحديد السابق
                    document.querySelectorAll('.edit-category-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // إضافة التحديد الجديد
                    this.classList.add('selected');

                    // إزالة رسالة الخطأ
                    document.getElementById('edit-category-error').style.display = 'none';
                });
            });

            // تبديل الشعبية للتعديل
            document.getElementById('edit-popular-toggle').addEventListener('click', function() {
                isEditPopular = !isEditPopular;
                this.classList.toggle('active', isEditPopular);
            });

            // إغلاق النافذة بالضغط على الخلفية
            document.getElementById('edit-product-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditModal();
                }
            });

            // إغلاق النافذة بمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.getElementById('edit-product-modal').style.display === 'flex') {
                    closeEditModal();
                }
            });

            // التحقق الفوري من البيانات للتعديل
            document.getElementById('edit-product-name').addEventListener('input', function() {
                if (this.value.trim().length >= 2) {
                    this.classList.remove('error');
                    document.getElementById('edit-name-error').style.display = 'none';
                }
            });

            document.getElementById('edit-product-price').addEventListener('input', function() {
                const price = parseFloat(this.value);
                if (price > 0 && !isNaN(price)) {
                    this.classList.remove('error');
                    document.getElementById('edit-price-error').style.display = 'none';
                }
            });

            document.getElementById('edit-product-stock').addEventListener('input', function() {
                const stock = parseInt(this.value);
                if (!isNaN(stock) && stock >= 0) {
                    this.classList.remove('error');
                    document.getElementById('edit-stock-error').style.display = 'none';
                }
            });
        }

        // تهيئة النظام
        function initializeSystem() {
            loadProducts();
            setupEventListeners();
            setupAddModalListeners();
            setupEditModalListeners();
            console.log('تم تهيئة نظام إدارة المنتجات بنجاح');
        }

        // دالة تسجيل الخروج
        function logout() {
            // تأكيد تسجيل الخروج
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // مسح بيانات المستخدم من التخزين
                localStorage.removeItem('currentUser');
                sessionStorage.removeItem('currentUser');

                // إظهار رسالة تسجيل الخروج
                alert('تم تسجيل الخروج بنجاح');

                // التوجه إلى صفحة تسجيل الدخول الأساسية
                window.location.href = '../auth/login.html';
            }
        }

        // تشغيل النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializeSystem);
    </script>
</body>
</html>
