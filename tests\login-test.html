<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام تسجيل الدخول</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Aria<PERSON>, sans-serif;
            background: linear-gradient(135deg, #1976d2, #2196f3);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #1976d2;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin: 10px 0;
        }
        .btn:hover {
            background: #1565c0;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn.success {
            background: #2e7d32;
        }
        .btn.success:hover {
            background: #1b5e20;
        }
        .btn.danger {
            background: #d32f2f;
        }
        .btn.danger:hover {
            background: #b71c1c;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .user-list {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .login-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            align-items: end;
        }
        .login-form .form-group {
            margin: 0;
        }
        .login-form .btn {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 اختبار نظام تسجيل الدخول</h1>
            <p>اختبار شامل لنظام المصادقة والتبديل بين أنواع المستخدمين</p>
        </div>

        <div class="test-section">
            <div class="test-title">👥 المستخدمين المتاحين</div>
            <button class="btn" onclick="listAvailableUsers()">عرض المستخدمين المتاحين</button>
            <div id="users-list" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 اختبار تسجيل الدخول</div>
            <div class="login-form">
                <div class="form-group">
                    <label for="testUsername">اسم المستخدم:</label>
                    <input type="text" id="testUsername" placeholder="أدخل اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label for="testPassword">كلمة المرور:</label>
                    <input type="password" id="testPassword" placeholder="أدخل كلمة المرور">
                </div>
                
                <div class="form-group">
                    <label for="testUserType">نوع المستخدم:</label>
                    <select id="testUserType">
                        <option value="student">طالب</option>
                        <option value="staff">عامل</option>
                        <option value="parent">ولي أمر</option>
                        <option value="admin">مدير</option>
                        <option value="school">مدرسة</option>
                    </select>
                </div>
                
                <button class="btn" onclick="testLogin()">اختبار تسجيل الدخول</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">⚡ اختبارات سريعة</div>
            <button class="btn success" onclick="testQuickLogin('admin', 'admin123', 'admin')">تسجيل دخول مدير</button>
            <button class="btn success" onclick="testQuickLogin('staff1', 'staff123', 'staff')">تسجيل دخول عامل</button>
            <button class="btn success" onclick="testQuickLogin('student1', 'student123', 'student')">تسجيل دخول طالب</button>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 أدوات الاختبار</div>
            <button class="btn" onclick="createTestUsers()">إنشاء مستخدمين تجريبيين</button>
            <button class="btn" onclick="checkLoginSystem()">فحص نظام تسجيل الدخول</button>
            <button class="btn danger" onclick="clearAllData()">مسح جميع البيانات</button>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 روابط سريعة</div>
            <button class="btn" onclick="window.open('../pages/auth/login.html', '_blank')">فتح صفحة تسجيل الدخول</button>
            <button class="btn" onclick="window.open('../pages/auth/register.html', '_blank')">فتح صفحة التسجيل</button>
            <button class="btn" onclick="window.open('registration-test.html', '_blank')">اختبار التسجيل</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        // عرض المستخدمين المتاحين
        function listAvailableUsers() {
            const resultDiv = document.getElementById('users-list');
            
            try {
                const db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                
                if (!db.users || db.users.length === 0) {
                    resultDiv.className = 'result warning';
                    resultDiv.innerHTML = '⚠️ لا توجد مستخدمين في قاعدة البيانات. قم بإنشاء مستخدمين تجريبيين أولاً.';
                } else {
                    const usersByRole = {};
                    db.users.forEach(user => {
                        if (!usersByRole[user.role]) {
                            usersByRole[user.role] = [];
                        }
                        usersByRole[user.role].push(user);
                    });

                    let usersHtml = `📊 إجمالي المستخدمين: ${db.users.length}<br><br>`;
                    
                    Object.keys(usersByRole).forEach(role => {
                        const roleNames = {
                            'admin': 'المديرين',
                            'staff': 'العاملين', 
                            'student': 'الطلاب',
                            'parent': 'أولياء الأمور',
                            'school': 'المدارس'
                        };
                        
                        usersHtml += `<strong>${roleNames[role] || role} (${usersByRole[role].length}):</strong><br>`;
                        usersByRole[role].forEach(user => {
                            usersHtml += `• ${user.username} / ${user.password} - ${user.name}<br>`;
                        });
                        usersHtml += '<br>';
                    });
                    
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = `<div class="user-list">${usersHtml}</div>`;
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في قراءة قاعدة البيانات: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        // اختبار تسجيل الدخول
        function testLogin() {
            const username = document.getElementById('testUsername').value.trim();
            const password = document.getElementById('testPassword').value.trim();
            const userType = document.getElementById('testUserType').value;
            
            if (!username || !password) {
                showResult('error', '❌ يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }
            
            testQuickLogin(username, password, userType);
        }

        // اختبار سريع لتسجيل الدخول
        function testQuickLogin(username, password, userType) {
            const resultDiv = document.getElementById('result');
            
            try {
                const db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                
                if (!db.users || db.users.length === 0) {
                    showResult('warning', '⚠️ لا توجد مستخدمين في قاعدة البيانات');
                    return;
                }
                
                // البحث عن المستخدم
                const user = db.users.find(u => 
                    u.username === username && 
                    u.password === password && 
                    u.role === userType &&
                    u.status === 'active'
                );
                
                if (user) {
                    let successMessage = `✅ تم تسجيل الدخول بنجاح!<br>`;
                    successMessage += `👤 المستخدم: ${user.name}<br>`;
                    successMessage += `🔑 اسم المستخدم: ${user.username}<br>`;
                    successMessage += `👥 النوع: ${user.role}<br>`;
                    successMessage += `📧 البريد: ${user.email || 'غير محدد'}<br>`;
                    
                    if (user.noorId) {
                        successMessage += `🆔 رقم نور: ${user.noorId}<br>`;
                    }
                    if (user.residenceId) {
                        successMessage += `🏠 رقم الإقامة: ${user.residenceId}<br>`;
                    }
                    if (user.schoolName) {
                        successMessage += `🏫 اسم المدرسة: ${user.schoolName}<br>`;
                    }
                    
                    // محاكاة حفظ الجلسة
                    const userSession = {
                        id: user.id,
                        username: user.username,
                        name: user.name,
                        role: user.role,
                        loginTime: new Date().toISOString()
                    };
                    sessionStorage.setItem('currentUser', JSON.stringify(userSession));
                    localStorage.setItem('currentUserRole', user.role);
                    
                    successMessage += `<br>🕒 تم حفظ الجلسة بنجاح`;
                    
                    showResult('success', successMessage);
                } else {
                    showResult('error', `❌ فشل تسجيل الدخول<br>لم يتم العثور على مستخدم بالبيانات:<br>اسم المستخدم: ${username}<br>النوع: ${userType}`);
                }
                
            } catch (error) {
                showResult('error', `❌ خطأ في اختبار تسجيل الدخول: ${error.message}`);
            }
        }

        // إنشاء مستخدمين تجريبيين
        function createTestUsers() {
            try {
                let db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                
                if (!db.users) {
                    db.users = [];
                }
                
                const testUsers = [
                    { id: 1, username: 'admin', password: 'admin123', role: 'admin', name: 'مدير النظام', email: '<EMAIL>', status: 'active' },
                    { id: 2, username: 'staff1', password: 'staff123', role: 'staff', name: 'عامل المقصف', email: '<EMAIL>', status: 'active', schoolId: 1 },
                    { id: 3, username: 'student1', password: 'student123', role: 'student', name: 'أحمد محمد', email: '<EMAIL>', status: 'active', noorId: '123456789', schoolId: 1, balance: 50 },
                    { id: 4, username: 'parent1', password: 'parent123', role: 'parent', name: 'محمد أحمد', email: '<EMAIL>', status: 'active', studentNoorId: '123456789', residenceId: '987654321' },
                    { id: 5, username: 'school1', password: 'school123', role: 'school', name: 'مدرسة الأمل', email: '<EMAIL>', status: 'active', schoolName: 'مدرسة الأمل الابتدائية', address: 'الرياض' }
                ];
                
                // إضافة المستخدمين إذا لم يكونوا موجودين
                testUsers.forEach(testUser => {
                    const exists = db.users.find(u => u.username === testUser.username);
                    if (!exists) {
                        testUser.createdAt = new Date().toISOString();
                        db.users.push(testUser);
                    }
                });
                
                localStorage.setItem('cafeteriaDB', JSON.stringify(db));
                
                showResult('success', `✅ تم إنشاء ${testUsers.length} مستخدم تجريبي بنجاح!<br>يمكنك الآن اختبار تسجيل الدخول معهم.`);
                
            } catch (error) {
                showResult('error', `❌ خطأ في إنشاء المستخدمين: ${error.message}`);
            }
        }

        // فحص نظام تسجيل الدخول
        function checkLoginSystem() {
            try {
                const checks = {
                    'قاعدة البيانات متاحة': !!localStorage.getItem('cafeteriaDB'),
                    'sessionStorage متاح': typeof sessionStorage !== 'undefined',
                    'localStorage متاح': typeof localStorage !== 'undefined',
                    'JSON متاح': typeof JSON !== 'undefined'
                };
                
                const db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                checks['مصفوفة المستخدمين موجودة'] = !!(db.users && Array.isArray(db.users));
                checks['يوجد مستخدمين'] = !!(db.users && db.users.length > 0);
                
                const results = Object.entries(checks).map(([check, result]) => 
                    `${result ? '✅' : '❌'} ${check}`
                ).join('<br>');
                
                const allPassed = Object.values(checks).every(check => check);
                
                showResult(allPassed ? 'success' : 'warning', `
                    <strong>نتائج فحص نظام تسجيل الدخول:</strong><br><br>
                    ${results}<br><br>
                    ${allPassed ? '🎉 جميع الفحوصات نجحت!' : '⚠️ بعض الفحوصات فشلت'}
                `);
                
            } catch (error) {
                showResult('error', `❌ خطأ في فحص النظام: ${error.message}`);
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ سيتم حذف جميع المستخدمين والجلسات!')) {
                localStorage.removeItem('cafeteriaDB');
                sessionStorage.clear();
                
                showResult('success', '✅ تم مسح جميع البيانات والجلسات');
            }
        }

        // دالة عرض النتائج
        function showResult(type, message) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
            
            // إخفاء النتيجة تلقائياً بعد 10 ثوان
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 10000);
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkLoginSystem, 1000);
        });
    </script>
</body>
</html>
