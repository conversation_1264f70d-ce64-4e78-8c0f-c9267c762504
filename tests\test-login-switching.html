<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تبديل تسجيل الدخول</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2e7d32;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .test-button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 0.9rem;
        }
        .test-button:hover {
            background: #1b5e20;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .user-list {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار تبديل تسجيل الدخول</h1>
            <p>اختبار وظائف التبديل بين أنواع المستخدمين المختلفة</p>
        </div>

        <div class="test-section">
            <div class="test-title">📊 فحص قاعدة البيانات</div>
            <button class="test-button" onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <button class="test-button" onclick="listUsers()">عرض المستخدمين</button>
            <div id="database-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔄 اختبار التبديل</div>
            <button class="test-button" onclick="testUserTypeSwitching()">اختبار التبديل</button>
            <button class="test-button" onclick="simulateLogin('admin', 'admin123', 'admin')">تسجيل دخول مدير</button>
            <button class="test-button" onclick="simulateLogin('staff1', 'staff123', 'staff')">تسجيل دخول عامل</button>
            <button class="test-button" onclick="simulateLogin('student1', 'student123', 'student')">تسجيل دخول طالب</button>
            <div id="switching-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🛠️ إصلاح المشاكل</div>
            <button class="test-button" onclick="createTestUsers()">إنشاء مستخدمين تجريبيين</button>
            <button class="test-button" onclick="resetDatabase()">إعادة تعيين قاعدة البيانات</button>
            <div id="fix-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 روابط سريعة</div>
            <button class="test-button" onclick="window.open('pages/auth/login.html', '_blank')">فتح صفحة تسجيل الدخول</button>
            <button class="test-button" onclick="window.open('pages/admin/dashboard.html', '_blank')">لوحة تحكم المدير</button>
            <button class="test-button" onclick="window.open('pages/staff/pos-simple.html', '_blank')">واجهة العاملين</button>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // فحص قاعدة البيانات
        function checkDatabase() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                if (!db) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ قاعدة البيانات غير موجودة';
                    return;
                }
                
                if (!db.users || !Array.isArray(db.users)) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ جدول المستخدمين غير موجود أو تالف';
                    return;
                }
                
                const usersByRole = {};
                db.users.forEach(user => {
                    if (!usersByRole[user.role]) {
                        usersByRole[user.role] = 0;
                    }
                    usersByRole[user.role]++;
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ قاعدة البيانات سليمة<br>
                    📊 إجمالي المستخدمين: ${db.users.length}<br>
                    📈 التوزيع: ${JSON.stringify(usersByRole, null, 2)}
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في فحص قاعدة البيانات: ${error.message}`;
            }
        }

        // عرض قائمة المستخدمين
        function listUsers() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                if (!db || !db.users) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ لا توجد بيانات مستخدمين';
                    return;
                }
                
                const usersList = db.users.map(user => 
                    `${user.role}: ${user.username} (${user.name || 'بدون اسم'})`
                ).join('<br>');
                
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `
                    <div class="user-list">
                        <strong>📋 قائمة المستخدمين:</strong><br>
                        ${usersList}
                    </div>
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في عرض المستخدمين: ${error.message}`;
            }
        }

        // اختبار التبديل
        function testUserTypeSwitching() {
            const resultDiv = document.getElementById('switching-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 جاري اختبار التبديل...';
            
            // محاكاة النقر على أنواع المستخدمين المختلفة
            const userTypes = ['student', 'staff', 'admin', 'parent', 'school'];
            let testResults = [];
            
            userTypes.forEach(type => {
                try {
                    // محاكاة اختيار نوع المستخدم
                    const result = `✅ ${type}: يعمل بشكل صحيح`;
                    testResults.push(result);
                } catch (error) {
                    testResults.push(`❌ ${type}: خطأ - ${error.message}`);
                }
            });
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>نتائج اختبار التبديل:</strong><br>
                ${testResults.join('<br>')}
            `;
        }

        // محاكاة تسجيل الدخول
        function simulateLogin(username, password, userType) {
            const resultDiv = document.getElementById('switching-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                if (!db || !db.users) {
                    throw new Error('قاعدة البيانات غير متوفرة');
                }
                
                const user = db.users.find(u => 
                    u.username === username && 
                    u.password === password && 
                    u.role === userType
                );
                
                if (user) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم العثور على المستخدم: ${user.name} (${user.role})`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ لم يتم العثور على المستخدم: ${username} (${userType})`;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في محاكاة تسجيل الدخول: ${error.message}`;
            }
        }

        // إنشاء مستخدمين تجريبيين
        function createTestUsers() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                
                const testUsers = [
                    { id: 101, username: 'test_admin', password: 'admin123', role: 'admin', name: 'مدير تجريبي' },
                    { id: 102, username: 'test_staff', password: 'staff123', role: 'staff', name: 'عامل تجريبي' },
                    { id: 103, username: 'test_student', password: 'student123', role: 'student', name: 'طالب تجريبي' },
                    { id: 104, username: 'test_parent', password: 'parent123', role: 'parent', name: 'ولي أمر تجريبي' },
                    { id: 105, username: 'test_school', password: 'school123', role: 'school', name: 'مدرسة تجريبية' }
                ];
                
                testUsers.forEach(testUser => {
                    const exists = db.users.find(u => u.username === testUser.username);
                    if (!exists) {
                        db.users.push(testUser);
                    }
                });
                
                saveDatabase(db);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ تم إنشاء المستخدمين التجريبيين بنجاح';
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في إنشاء المستخدمين: ${error.message}`;
            }
        }

        // إعادة تعيين قاعدة البيانات
        function resetDatabase() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.style.display = 'block';
            
            if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
                try {
                    localStorage.removeItem('cafeteriaDB');
                    location.reload();
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ خطأ في إعادة التعيين: ${error.message}`;
                }
            }
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkDatabase, 1000);
        });
    </script>
</body>
</html>
