<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة التسجيل</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2e7d32;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .test-button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 0.9rem;
        }
        .test-button:hover {
            background: #1b5e20;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .log-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.8rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .form-test {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-test input, .form-test select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .form-test label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص مشكلة التسجيل</h1>
            <p>أداة تشخيص متقدمة لحل مشاكل إنشاء الحسابات</p>
        </div>

        <div class="test-section">
            <div class="test-title">📊 فحص البيئة</div>
            <button class="test-button" onclick="checkEnvironment()">فحص البيئة</button>
            <button class="test-button" onclick="checkLocalStorage()">فحص التخزين المحلي</button>
            <button class="test-button" onclick="checkConsoleErrors()">فحص أخطاء وحدة التحكم</button>
            <div id="environment-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 اختبار التسجيل المباشر</div>
            <div class="form-test">
                <label>اسم المستخدم:</label>
                <input type="text" id="debug-username" value="test_debug_user">
                
                <label>كلمة المرور:</label>
                <input type="password" id="debug-password" value="test123">
                
                <label>الاسم الكامل:</label>
                <input type="text" id="debug-fullname" value="مستخدم تجريبي">
                
                <label>البريد الإلكتروني:</label>
                <input type="email" id="debug-email" value="<EMAIL>">
                
                <label>نوع المستخدم:</label>
                <select id="debug-usertype">
                    <option value="student">طالب</option>
                    <option value="staff">عامل</option>
                    <option value="parent">ولي أمر</option>
                    <option value="admin">مدير</option>
                    <option value="school">مدرسة</option>
                </select>
            </div>
            <button class="test-button" onclick="testDirectRegistration()">اختبار التسجيل المباشر</button>
            <button class="test-button" onclick="simulateFormSubmission()">محاكاة إرسال النموذج</button>
            <div id="registration-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 سجل الأحداث</div>
            <button class="test-button" onclick="clearLog()">مسح السجل</button>
            <button class="test-button" onclick="exportLog()">تصدير السجل</button>
            <div id="log-area" class="log-area"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 أدوات الإصلاح</div>
            <button class="test-button" onclick="resetLocalStorage()">إعادة تعيين التخزين المحلي</button>
            <button class="test-button" onclick="createMinimalDatabase()">إنشاء قاعدة بيانات أساسية</button>
            <button class="test-button" onclick="testUserManager()">اختبار مدير المستخدمين</button>
            <div id="fix-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        let logArea = null;
        let testUserManager = null;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logArea = document.getElementById('log-area');
            log('تم تحميل صفحة التشخيص');
            
            // إنشاء مدير المستخدمين للاختبار
            createTestUserManager();
        });

        // دالة تسجيل الأحداث
        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (logArea) {
                logArea.textContent += logMessage;
                logArea.scrollTop = logArea.scrollHeight;
            }
            
            console.log(message);
        }

        // إنشاء مدير المستخدمين للاختبار
        function createTestUserManager() {
            try {
                testUserManager = {
                    storageKey: 'cafeteriaDB',
                    
                    getDatabase() {
                        try {
                            const db = localStorage.getItem(this.storageKey);
                            return db ? JSON.parse(db) : this.createDefaultDatabase();
                        } catch (error) {
                            log('خطأ في قراءة قاعدة البيانات: ' + error.message);
                            return this.createDefaultDatabase();
                        }
                    },
                    
                    createDefaultDatabase() {
                        const defaultDB = {
                            users: [],
                            schools: [],
                            products: [],
                            orders: [],
                            preorders: [],
                            notifications: [],
                            settings: { themes: [] }
                        };
                        this.saveDatabase(defaultDB);
                        log('تم إنشاء قاعدة بيانات افتراضية');
                        return defaultDB;
                    },
                    
                    saveDatabase(db) {
                        localStorage.setItem(this.storageKey, JSON.stringify(db));
                        log('تم حفظ قاعدة البيانات');
                    },
                    
                    saveUser(userData) {
                        try {
                            log('بدء عملية حفظ المستخدم: ' + JSON.stringify(userData));
                            
                            const db = this.getDatabase();
                            
                            if (!db.users || !Array.isArray(db.users)) {
                                db.users = [];
                                log('تم إنشاء مصفوفة مستخدمين جديدة');
                            }
                            
                            const newId = db.users.length > 0 ? Math.max(...db.users.map(u => u.id || 0), 0) + 1 : 1;
                            
                            const newUser = {
                                id: newId,
                                username: userData.username,
                                password: userData.password,
                                role: userData.userType,
                                name: userData.fullName,
                                email: userData.email || '',
                                status: 'active',
                                createdAt: new Date().toISOString()
                            };
                            
                            db.users.push(newUser);
                            this.saveDatabase(db);
                            
                            log('تم إنشاء المستخدم بنجاح: ' + JSON.stringify(newUser));
                            return { success: true, user: newUser };
                            
                        } catch (error) {
                            log('خطأ في حفظ المستخدم: ' + error.message);
                            return { success: false, error: error.message };
                        }
                    }
                };
                
                log('تم إنشاء مدير المستخدمين للاختبار');
                
            } catch (error) {
                log('خطأ في إنشاء مدير المستخدمين: ' + error.message);
            }
        }

        // فحص البيئة
        function checkEnvironment() {
            const resultDiv = document.getElementById('environment-result');
            resultDiv.style.display = 'block';
            
            try {
                log('بدء فحص البيئة...');
                
                const checks = {
                    'localStorage متاح': typeof Storage !== 'undefined',
                    'JSON متاح': typeof JSON !== 'undefined',
                    'console متاح': typeof console !== 'undefined',
                    'getDatabase متاح': typeof getDatabase === 'function',
                    'saveDatabase متاح': typeof saveDatabase === 'function'
                };
                
                const results = Object.entries(checks).map(([check, result]) => 
                    `${result ? '✅' : '❌'} ${check}`
                ).join('<br>');
                
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `<strong>نتائج فحص البيئة:</strong><br>${results}`;
                
                log('تم فحص البيئة بنجاح');
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في فحص البيئة: ${error.message}`;
                log('خطأ في فحص البيئة: ' + error.message);
            }
        }

        // فحص التخزين المحلي
        function checkLocalStorage() {
            const resultDiv = document.getElementById('environment-result');
            resultDiv.style.display = 'block';
            
            try {
                log('فحص التخزين المحلي...');
                
                const cafeteriaDB = localStorage.getItem('cafeteriaDB');
                const smartCanteenUsers = localStorage.getItem('smartCanteen_users');
                
                let info = '';
                if (cafeteriaDB) {
                    const db = JSON.parse(cafeteriaDB);
                    info += `✅ cafeteriaDB موجود (${db.users ? db.users.length : 0} مستخدم)<br>`;
                } else {
                    info += '❌ cafeteriaDB غير موجود<br>';
                }
                
                if (smartCanteenUsers) {
                    const users = JSON.parse(smartCanteenUsers);
                    info += `⚠️ smartCanteen_users موجود (${users.length} مستخدم)<br>`;
                } else {
                    info += '✅ smartCanteen_users غير موجود (جيد)<br>';
                }
                
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `<strong>حالة التخزين المحلي:</strong><br>${info}`;
                
                log('تم فحص التخزين المحلي');
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في فحص التخزين: ${error.message}`;
                log('خطأ في فحص التخزين المحلي: ' + error.message);
            }
        }

        // اختبار التسجيل المباشر
        function testDirectRegistration() {
            const resultDiv = document.getElementById('registration-result');
            resultDiv.style.display = 'block';
            
            try {
                log('بدء اختبار التسجيل المباشر...');
                
                const userData = {
                    username: document.getElementById('debug-username').value,
                    password: document.getElementById('debug-password').value,
                    fullName: document.getElementById('debug-fullname').value,
                    email: document.getElementById('debug-email').value,
                    userType: document.getElementById('debug-usertype').value
                };
                
                log('بيانات المستخدم: ' + JSON.stringify(userData));
                
                if (!testUserManager) {
                    throw new Error('مدير المستخدمين غير متاح');
                }
                
                const result = testUserManager.saveUser(userData);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ تم إنشاء الحساب بنجاح!<br>المعرف: ${result.user.id}<br>النوع: ${result.user.role}`;
                    log('نجح التسجيل المباشر');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ فشل التسجيل: ${result.error}`;
                    log('فشل التسجيل المباشر: ' + result.error);
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
                log('خطأ في اختبار التسجيل المباشر: ' + error.message);
            }
        }

        // مسح السجل
        function clearLog() {
            if (logArea) {
                logArea.textContent = '';
            }
        }

        // تصدير السجل
        function exportLog() {
            if (logArea) {
                const logContent = logArea.textContent;
                const blob = new Blob([logContent], { type: 'text/plain' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `registration_debug_log_${new Date().toISOString().split('T')[0]}.txt`;
                link.click();
                log('تم تصدير السجل');
            }
        }

        // إعادة تعيين التخزين المحلي
        function resetLocalStorage() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.style.display = 'block';
            
            try {
                localStorage.removeItem('cafeteriaDB');
                localStorage.removeItem('smartCanteen_users');
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ تم إعادة تعيين التخزين المحلي';
                log('تم إعادة تعيين التخزين المحلي');
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في إعادة التعيين: ${error.message}`;
                log('خطأ في إعادة تعيين التخزين المحلي: ' + error.message);
            }
        }

        // إنشاء قاعدة بيانات أساسية
        function createMinimalDatabase() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.style.display = 'block';
            
            try {
                const minimalDB = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            name: 'مدير النظام',
                            status: 'active',
                            createdAt: new Date().toISOString()
                        }
                    ],
                    schools: [],
                    products: [],
                    orders: [],
                    preorders: [],
                    notifications: [],
                    settings: { themes: [] }
                };
                
                localStorage.setItem('cafeteriaDB', JSON.stringify(minimalDB));
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ تم إنشاء قاعدة بيانات أساسية مع حساب مدير افتراضي';
                log('تم إنشاء قاعدة بيانات أساسية');
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في إنشاء قاعدة البيانات: ${error.message}`;
                log('خطأ في إنشاء قاعدة البيانات الأساسية: ' + error.message);
            }
        }

        // اختبار مدير المستخدمين
        function testUserManager() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.style.display = 'block';
            
            try {
                log('اختبار مدير المستخدمين...');
                
                if (!testUserManager) {
                    throw new Error('مدير المستخدمين غير متاح');
                }
                
                const db = testUserManager.getDatabase();
                const testUser = {
                    username: 'test_manager_' + Date.now(),
                    password: 'test123',
                    fullName: 'اختبار مدير المستخدمين',
                    email: '<EMAIL>',
                    userType: 'student'
                };
                
                const result = testUserManager.saveUser(testUser);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ مدير المستخدمين يعمل بشكل صحيح<br>تم إنشاء مستخدم تجريبي بالمعرف: ${result.user.id}`;
                    log('مدير المستخدمين يعمل بشكل صحيح');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ مدير المستخدمين لا يعمل: ${result.error}`;
                    log('مدير المستخدمين لا يعمل: ' + result.error);
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في اختبار مدير المستخدمين: ${error.message}`;
                log('خطأ في اختبار مدير المستخدمين: ' + error.message);
            }
        }
    </script>
</body>
</html>
