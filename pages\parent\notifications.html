<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }
        
        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .notifications-filter {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notifications-filter label {
            font-weight: 500;
        }
        
        .notifications-filter select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }
        
        .mark-all-read {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .mark-all-read i {
            margin-left: 5px;
        }
        
        .mark-all-read:hover {
            background-color: var(--dark-color);
        }
        
        .notifications-list {
            list-style: none;
        }
        
        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            transition: all 0.3s ease;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background-color: #f9f9f9;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(46, 125, 50, 0.1);
            color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.2rem;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .notification-message {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .notification-date {
            color: #999;
            font-size: 0.8rem;
        }
        
        .notification-unread {
            background-color: rgba(46, 125, 50, 0.05);
        }
        
        .notification-unread .notification-title {
            color: var(--primary-color);
        }
        
        .notification-actions {
            display: flex;
            gap: 10px;
            margin-right: 15px;
        }
        
        .notification-actions button {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .notification-actions button:hover {
            color: var(--primary-color);
        }
        
        .empty-notifications {
            text-align: center;
            padding: 50px 0;
            color: #666;
        }
        
        .empty-notifications i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .empty-notifications h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }
        
        .logout-btn i {
            margin-left: 8px;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .notifications-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>ولي الأمر</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="children.html"><i class="fas fa-child"></i> الأبناء</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                <li><a href="notifications.html" class="active"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1>الإشعارات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>
            
            <div class="content-card">
                <div class="notifications-header">
                    <div class="notifications-filter">
                        <label for="notification-filter">عرض:</label>
                        <select id="notification-filter">
                            <option value="all">جميع الإشعارات</option>
                            <option value="unread">الإشعارات غير المقروءة</option>
                            <option value="read">الإشعارات المقروءة</option>
                        </select>
                    </div>
                    <button id="mark-all-read" class="mark-all-read"><i class="fas fa-check-double"></i> تعيين الكل كمقروء</button>
                </div>
                
                <ul class="notifications-list" id="notifications-list">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </ul>
                
                <div id="empty-notifications" class="empty-notifications" style="display: none;">
                    <i class="fas fa-bell-slash"></i>
                    <h3>لا توجد إشعارات</h3>
                    <p>ستظهر هنا الإشعارات الخاصة بك عند وصولها.</p>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is parent
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            
            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            
            // Get database
            const db = getDatabase();
            
            // Get notifications
            let notifications = db.notifications.filter(notification => notification.userId === currentUser.id);
            
            // Function to render notifications
            function renderNotifications(filter = 'all') {
                const notificationsList = document.getElementById('notifications-list');
                const emptyNotifications = document.getElementById('empty-notifications');
                
                // Filter notifications
                let filteredNotifications = notifications;
                if (filter === 'unread') {
                    filteredNotifications = notifications.filter(notification => !notification.isRead);
                } else if (filter === 'read') {
                    filteredNotifications = notifications.filter(notification => notification.isRead);
                }
                
                // Clear list
                notificationsList.innerHTML = '';
                
                // Check if there are notifications
                if (filteredNotifications.length === 0) {
                    notificationsList.style.display = 'none';
                    emptyNotifications.style.display = 'block';
                    return;
                }
                
                notificationsList.style.display = 'block';
                emptyNotifications.style.display = 'none';
                
                // Populate notifications list
                filteredNotifications.forEach(notification => {
                    const notificationItem = document.createElement('li');
                    notificationItem.className = 'notification-item' + (notification.isRead ? '' : ' notification-unread');
                    notificationItem.setAttribute('data-id', notification.id);
                    notificationItem.innerHTML = `
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-message">${notification.message}</div>
                            <div class="notification-date">${notification.date}</div>
                        </div>
                        <div class="notification-actions">
                            <button class="mark-read-btn" title="تعيين كمقروء" ${notification.isRead ? 'style="display: none;"' : ''}>
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="delete-btn" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    notificationsList.appendChild(notificationItem);
                });
                
                // Add event listeners to mark as read buttons
                document.querySelectorAll('.mark-read-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const notificationItem = this.closest('.notification-item');
                        const notificationId = parseInt(notificationItem.getAttribute('data-id'));
                        
                        // Update notification in database
                        const notificationIndex = db.notifications.findIndex(n => n.id === notificationId);
                        if (notificationIndex !== -1) {
                            db.notifications[notificationIndex].isRead = true;
                            saveDatabase(db);
                            
                            // Update UI
                            notificationItem.classList.remove('notification-unread');
                            this.style.display = 'none';
                            
                            // Update local notifications array
                            const notificationLocalIndex = notifications.findIndex(n => n.id === notificationId);
                            if (notificationLocalIndex !== -1) {
                                notifications[notificationLocalIndex].isRead = true;
                            }
                        }
                    });
                });
                
                // Add event listeners to delete buttons
                document.querySelectorAll('.delete-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const notificationItem = this.closest('.notification-item');
                        const notificationId = parseInt(notificationItem.getAttribute('data-id'));
                        
                        if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                            // Remove notification from database
                            db.notifications = db.notifications.filter(n => n.id !== notificationId);
                            saveDatabase(db);
                            
                            // Remove from UI
                            notificationItem.remove();
                            
                            // Update local notifications array
                            notifications = notifications.filter(n => n.id !== notificationId);
                            
                            // Check if there are notifications left
                            if (notifications.length === 0) {
                                notificationsList.style.display = 'none';
                                emptyNotifications.style.display = 'block';
                            }
                        }
                    });
                });
            }
            
            // Initial render
            renderNotifications();
            
            // Filter change event
            document.getElementById('notification-filter').addEventListener('change', function() {
                renderNotifications(this.value);
            });
            
            // Mark all as read button
            document.getElementById('mark-all-read').addEventListener('click', function() {
                if (notifications.length === 0) {
                    return;
                }
                
                if (confirm('هل أنت متأكد من تعيين جميع الإشعارات كمقروءة؟')) {
                    // Update notifications in database
                    db.notifications.forEach(notification => {
                        if (notification.userId === currentUser.id) {
                            notification.isRead = true;
                        }
                    });
                    saveDatabase(db);
                    
                    // Update local notifications array
                    notifications.forEach(notification => {
                        notification.isRead = true;
                    });
                    
                    // Update UI
                    document.querySelectorAll('.notification-item').forEach(item => {
                        item.classList.remove('notification-unread');
                    });
                    
                    document.querySelectorAll('.mark-read-btn').forEach(btn => {
                        btn.style.display = 'none';
                    });
                    
                    alert('تم تعيين جميع الإشعارات كمقروءة.');
                }
            });
            
            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
    </script>
</body>
</html>
