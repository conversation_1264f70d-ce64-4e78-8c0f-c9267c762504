/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * ملف JavaScript الخاص بلوحة تحكم العاملين
 */

/**
 * تحديث الإحصائيات في لوحة التحكم
 * @param {Object} db - قاعدة البيانات
 * @param {Object} currentUser - المستخدم الحالي
 */
function updateStats(db, currentUser) {
    try {
        // الحصول على المدرسة الحالية
        const schoolId = currentUser.schoolId;
        
        // الحصول على تاريخ اليوم
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // حساب عدد الطلبات اليوم
        const todayOrders = db.orders.filter(order => {
            const orderDate = new Date(order.date);
            orderDate.setHours(0, 0, 0, 0);
            return order.schoolId === schoolId && orderDate.getTime() === today.getTime();
        });
        
        // حساب المبيعات اليوم
        const todaySales = todayOrders.reduce((total, order) => total + order.totalPrice, 0);
        
        // حساب عدد المنتجات المتاحة
        const availableProducts = db.products.filter(product => 
            product.status === 'active' && 
            (!product.schoolId || product.schoolId === schoolId)
        );
        
        // حساب عدد الطلاب في المدرسة
        const schoolStudents = db.users.filter(user => 
            user.role === 'student' && 
            user.schoolId === schoolId
        );
        
        // تحديث العناصر في الواجهة
        document.getElementById('orders-count').textContent = todayOrders.length;
        document.getElementById('sales-today').textContent = todaySales;
        document.getElementById('products-count').textContent = availableProducts.length;
        document.getElementById('students-count').textContent = schoolStudents.length;
        
        // إضافة تأثيرات حركية للإحصائيات
        animateStats();
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

/**
 * إضافة تأثيرات حركية للإحصائيات
 */
function animateStats() {
    try {
        const statValues = document.querySelectorAll('.stat-value');
        
        statValues.forEach(statValue => {
            const finalValue = parseInt(statValue.textContent);
            
            // إعادة تعيين القيمة إلى صفر
            statValue.textContent = '0';
            
            // تحريك القيمة من صفر إلى القيمة النهائية
            let currentValue = 0;
            const duration = 1500; // مدة التحريك بالمللي ثانية
            const interval = 16; // فترة التحديث بالمللي ثانية
            const steps = duration / interval;
            const increment = finalValue / steps;
            
            const animation = setInterval(() => {
                currentValue += increment;
                
                if (currentValue >= finalValue) {
                    clearInterval(animation);
                    statValue.textContent = finalValue;
                } else {
                    statValue.textContent = Math.floor(currentValue);
                }
            }, interval);
        });
    } catch (error) {
        console.error('خطأ في تحريك الإحصائيات:', error);
    }
}

/**
 * إنشاء الرسم البياني للمبيعات
 * @param {Object} db - قاعدة البيانات
 * @param {Object} currentUser - المستخدم الحالي
 */
function createSalesChart(db, currentUser) {
    try {
        const schoolId = currentUser.schoolId;
        
        // الحصول على بيانات المبيعات للأيام السبعة الماضية
        const salesData = getLast7DaysSales(db, schoolId);
        
        // الحصول على سياق الرسم البياني
        const ctx = document.getElementById('sales-chart').getContext('2d');
        
        // إنشاء الرسم البياني
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: salesData.labels,
                datasets: [{
                    label: 'المبيعات اليومية (ريال)',
                    data: salesData.values,
                    backgroundColor: 'rgba(46, 125, 50, 0.1)',
                    borderColor: 'rgba(46, 125, 50, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(46, 125, 50, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                family: 'Tajawal',
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            family: 'Tajawal',
                            size: 14
                        },
                        bodyFont: {
                            family: 'Tajawal',
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + ' ريال';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Tajawal',
                                size: 12
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                family: 'Tajawal',
                                size: 12
                            },
                            callback: function(value) {
                                return value + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('خطأ في إنشاء الرسم البياني للمبيعات:', error);
    }
}

/**
 * الحصول على بيانات المبيعات للأيام السبعة الماضية
 * @param {Object} db - قاعدة البيانات
 * @param {number} schoolId - معرف المدرسة
 * @returns {Object} - كائن يحتوي على تسميات الأيام وقيم المبيعات
 */
function getLast7DaysSales(db, schoolId) {
    try {
        const labels = [];
        const values = [];
        
        // الحصول على التاريخ الحالي
        const today = new Date();
        
        // حساب المبيعات لكل يوم من الأيام السبعة الماضية
        for (let i = 6; i >= 0; i--) {
            // حساب تاريخ اليوم
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            date.setHours(0, 0, 0, 0);
            
            // تنسيق التاريخ للعرض
            const formattedDate = formatDate(date);
            
            // إضافة التاريخ إلى التسميات
            labels.push(formattedDate);
            
            // حساب المبيعات لهذا اليوم
            const daySales = db.orders.filter(order => {
                const orderDate = new Date(order.date);
                orderDate.setHours(0, 0, 0, 0);
                return order.schoolId === schoolId && orderDate.getTime() === date.getTime();
            }).reduce((total, order) => total + order.totalPrice, 0);
            
            // إضافة المبيعات إلى القيم
            values.push(daySales);
        }
        
        return { labels, values };
    } catch (error) {
        console.error('خطأ في الحصول على بيانات المبيعات:', error);
        return { labels: [], values: [] };
    }
}

/**
 * تنسيق التاريخ للعرض
 * @param {Date} date - التاريخ
 * @returns {string} - التاريخ المنسق
 */
function formatDate(date) {
    try {
        const days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const dayName = days[date.getDay()];
        const day = date.getDate();
        const month = date.getMonth() + 1;
        
        return `${dayName} ${day}/${month}`;
    } catch (error) {
        console.error('خطأ في تنسيق التاريخ:', error);
        return '';
    }
}

/**
 * عرض آخر الطلبات
 * @param {Object} db - قاعدة البيانات
 * @param {Object} currentUser - المستخدم الحالي
 */
function displayRecentOrders(db, currentUser) {
    try {
        const schoolId = currentUser.schoolId;
        
        // الحصول على آخر 5 طلبات للمدرسة
        const recentOrders = db.orders
            .filter(order => order.schoolId === schoolId)
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);
        
        // الحصول على عنصر الجدول
        const tableBody = document.getElementById('recent-orders-table');
        
        // مسح محتوى الجدول
        tableBody.innerHTML = '';
        
        // إضافة الطلبات إلى الجدول
        if (recentOrders.length === 0) {
            // إذا لم تكن هناك طلبات
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="6" style="text-align: center;">لا توجد طلبات حديثة</td>
            `;
            tableBody.appendChild(emptyRow);
        } else {
            // إضافة الطلبات
            recentOrders.forEach(order => {
                // الحصول على معلومات الطالب
                const student = db.users.find(user => user.id === order.userId);
                
                // تنسيق التاريخ
                const orderDate = new Date(order.date);
                const formattedDate = `${orderDate.getDate()}/${orderDate.getMonth() + 1}/${orderDate.getFullYear()} ${orderDate.getHours()}:${orderDate.getMinutes().toString().padStart(2, '0')}`;
                
                // إنشاء صف الطلب
                const orderRow = document.createElement('tr');
                
                // تحديد لون الصف حسب حالة الطلب
                if (order.status === 'completed') {
                    orderRow.style.borderLeft = '3px solid #4caf50';
                } else if (order.status === 'pending') {
                    orderRow.style.borderLeft = '3px solid #ff9800';
                } else if (order.status === 'cancelled') {
                    orderRow.style.borderLeft = '3px solid #f44336';
                }
                
                // إضافة محتوى الصف
                orderRow.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${student ? student.name : 'غير معروف'}</td>
                    <td>${getOrderProductsText(order, db)}</td>
                    <td>${order.totalPrice} ريال</td>
                    <td>${formattedDate}</td>
                    <td><span class="status ${order.status}">${getStatusText(order.status)}</span></td>
                `;
                
                // إضافة الصف إلى الجدول
                tableBody.appendChild(orderRow);
            });
        }
    } catch (error) {
        console.error('خطأ في عرض آخر الطلبات:', error);
    }
}

/**
 * الحصول على نص منتجات الطلب
 * @param {Object} order - الطلب
 * @param {Object} db - قاعدة البيانات
 * @returns {string} - نص المنتجات
 */
function getOrderProductsText(order, db) {
    try {
        if (!order.items || order.items.length === 0) {
            return 'لا توجد منتجات';
        }
        
        // إذا كان هناك أكثر من 2 منتجات، نعرض أول منتجين فقط
        if (order.items.length > 2) {
            const firstTwoItems = order.items.slice(0, 2);
            const remainingCount = order.items.length - 2;
            
            const itemsText = firstTwoItems.map(item => {
                const product = db.products.find(p => p.id === item.productId);
                return `${product ? product.name : 'منتج غير معروف'} (${item.quantity})`;
            }).join('، ');
            
            return `${itemsText} و${remainingCount} أخرى`;
        } else {
            // عرض جميع المنتجات
            return order.items.map(item => {
                const product = db.products.find(p => p.id === item.productId);
                return `${product ? product.name : 'منتج غير معروف'} (${item.quantity})`;
            }).join('، ');
        }
    } catch (error) {
        console.error('خطأ في الحصول على نص منتجات الطلب:', error);
        return 'خطأ في عرض المنتجات';
    }
}

/**
 * الحصول على نص حالة الطلب
 * @param {string} status - حالة الطلب
 * @returns {string} - النص المعروض
 */
function getStatusText(status) {
    switch (status) {
        case 'completed':
            return 'مكتمل';
        case 'pending':
            return 'معلق';
        case 'cancelled':
            return 'ملغي';
        default:
            return status;
    }
}

/**
 * تهيئة الإجراءات السريعة
 */
function initializeQuickActions() {
    try {
        // الحصول على جميع الإجراءات السريعة
        const quickActions = document.querySelectorAll('.quick-action');
        
        // إضافة مستمع حدث لكل إجراء
        quickActions.forEach(action => {
            action.addEventListener('click', function() {
                const actionType = this.getAttribute('data-action');
                
                // تنفيذ الإجراء المناسب
                switch (actionType) {
                    case 'pos':
                        window.location.href = 'pos.html';
                        break;
                    case 'pending-orders':
                        window.location.href = 'orders.html?status=pending';
                        break;
                    case 'products':
                        window.location.href = 'products.html';
                        break;
                    case 'reports':
                        window.location.href = 'reports.html';
                        break;
                }
            });
        });
    } catch (error) {
        console.error('خطأ في تهيئة الإجراءات السريعة:', error);
    }
}
