<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <link rel="stylesheet" href="../../assets/css/dark-mode.css">
    <link rel="stylesheet" href="../../assets/css/animations.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;700&family=Almarai:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #eee;
        }

        .theme-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .theme-option {
            border: 2px solid transparent;
            border-radius: 5px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .theme-option.active {
            border-color: var(--primary-color);
        }

        .theme-preview {
            height: 150px;
            display: flex;
            flex-direction: column;
        }

        .theme-header {
            height: 30%;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: 500;
        }

        .theme-body {
            height: 70%;
            display: flex;
        }

        .theme-sidebar {
            width: 30%;
            height: 100%;
        }

        .theme-content {
            width: 70%;
            height: 100%;
            background-color: #f5f5f5;
            padding: 10px;
        }

        .theme-name {
            text-align: center;
            padding: 10px;
            font-weight: 500;
            border-top: 1px solid #eee;
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .color-picker {
            display: flex;
            align-items: center;
        }

        .color-picker label {
            margin-right: 10px;
            min-width: 100px;
        }

        .color-picker input[type="color"] {
            width: 50px;
            height: 30px;
            padding: 0;
            border: none;
            cursor: pointer;
        }

        .custom-theme-preview {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .theme-options {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .color-picker-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="schools.html"><i class="fas fa-school"></i> إدارة المدارس</a></li>
                <li><a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> إدارة العاملين</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>الإعدادات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>إعدادات النظام</h2>
                <div class="settings-section">
                    <form id="system-settings-form">
                        <div class="form-group">
                            <label for="system-name">اسم النظام</label>
                            <input type="text" id="system-name" value="فواصل النجاح للخدمات الإعاشة">
                        </div>
                        <div class="form-group">
                            <label for="system-email">البريد الإلكتروني للنظام</label>
                            <input type="email" id="system-email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="system-phone">رقم الهاتف</label>
                            <input type="tel" id="system-phone" value="+966 12 345 6789">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="content-card">
                <h2>تخصيص المظهر</h2>
                <div class="settings-section">
                    <h3>السمات الجاهزة</h3>
                    <div class="theme-options" id="theme-options">
                        <!-- سيتم ملء هذا القسم ديناميكيًا -->
                    </div>

                    <h3>إنشاء سمة مخصصة</h3>
                    <div class="color-picker-group">
                        <div class="color-picker">
                            <label for="primary-color">اللون الرئيسي</label>
                            <input type="color" id="primary-color" value="#2e7d32">
                        </div>
                        <div class="color-picker">
                            <label for="secondary-color">اللون الثانوي</label>
                            <input type="color" id="secondary-color" value="#f9a825">
                        </div>
                        <div class="color-picker">
                            <label for="dark-color">اللون الداكن</label>
                            <input type="color" id="dark-color" value="#1b5e20">
                        </div>
                        <div class="color-picker">
                            <label for="light-color">اللون الفاتح</label>
                            <input type="color" id="light-color" value="#e8f5e9">
                        </div>
                    </div>

                    <div class="custom-theme-preview" id="custom-theme-preview">
                        <div class="theme-preview">
                            <div class="theme-header" id="preview-header">
                                العنوان
                            </div>
                            <div class="theme-body">
                                <div class="theme-sidebar" id="preview-sidebar"></div>
                                <div class="theme-content" id="preview-content">
                                    <div style="margin-bottom: 10px; height: 20px; width: 70%; background-color: #ddd; border-radius: 3px;"></div>
                                    <div style="height: 10px; width: 90%; background-color: #ddd; border-radius: 3px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="theme-name">اسم السمة</label>
                        <input type="text" id="theme-name" placeholder="أدخل اسم السمة المخصصة">
                    </div>

                    <div class="form-actions">
                        <button type="button" id="save-custom-theme" class="btn btn-primary">حفظ السمة المخصصة</button>
                        <button type="button" id="apply-theme" class="btn btn-primary">تطبيق السمة</button>
                        <a href="../settings/customization.html" class="btn btn-secondary">خيارات تخصيص متقدمة</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/theme-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is admin
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'admin') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Populate theme options
            const themeOptions = document.getElementById('theme-options');
            db.settings.themes.forEach(theme => {
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.setAttribute('data-id', theme.id);

                // Set active theme
                if (theme.id === 1) { // Default theme
                    themeOption.classList.add('active');
                }

                themeOption.innerHTML = `
                    <div class="theme-preview">
                        <div class="theme-header" style="background-color: ${theme.primaryColor};">
                            العنوان
                        </div>
                        <div class="theme-body">
                            <div class="theme-sidebar" style="background-color: ${theme.darkColor};"></div>
                            <div class="theme-content">
                                <div style="margin-bottom: 10px; height: 20px; width: 70%; background-color: #ddd; border-radius: 3px;"></div>
                                <div style="height: 10px; width: 90%; background-color: #ddd; border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="theme-name">${theme.name}</div>
                `;

                themeOptions.appendChild(themeOption);
            });

            // Theme selection
            document.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.theme-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                });
            });

            // Custom theme color pickers
            const primaryColorPicker = document.getElementById('primary-color');
            const secondaryColorPicker = document.getElementById('secondary-color');
            const darkColorPicker = document.getElementById('dark-color');
            const lightColorPicker = document.getElementById('light-color');

            // Update preview on color change
            function updatePreview() {
                document.getElementById('preview-header').style.backgroundColor = primaryColorPicker.value;
                document.getElementById('preview-sidebar').style.backgroundColor = darkColorPicker.value;
            }

            primaryColorPicker.addEventListener('input', updatePreview);
            darkColorPicker.addEventListener('input', updatePreview);

            // Initial preview update
            updatePreview();

            // Save custom theme
            document.getElementById('save-custom-theme').addEventListener('click', function() {
                const themeName = document.getElementById('theme-name').value.trim();

                if (!themeName) {
                    alert('الرجاء إدخال اسم للسمة المخصصة!');
                    return;
                }

                // Create new theme
                const newTheme = {
                    id: db.settings.themes.length + 1,
                    name: themeName,
                    primaryColor: primaryColorPicker.value,
                    secondaryColor: secondaryColorPicker.value,
                    darkColor: darkColorPicker.value,
                    lightColor: lightColorPicker.value
                };

                // Add to database
                db.settings.themes.push(newTheme);
                saveDatabase(db);

                // Add to UI
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.setAttribute('data-id', newTheme.id);

                themeOption.innerHTML = `
                    <div class="theme-preview">
                        <div class="theme-header" style="background-color: ${newTheme.primaryColor};">
                            العنوان
                        </div>
                        <div class="theme-body">
                            <div class="theme-sidebar" style="background-color: ${newTheme.darkColor};"></div>
                            <div class="theme-content">
                                <div style="margin-bottom: 10px; height: 20px; width: 70%; background-color: #ddd; border-radius: 3px;"></div>
                                <div style="height: 10px; width: 90%; background-color: #ddd; border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="theme-name">${newTheme.name}</div>
                `;

                themeOptions.appendChild(themeOption);

                // Add click event
                themeOption.addEventListener('click', function() {
                    document.querySelectorAll('.theme-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                });

                alert('تم حفظ السمة المخصصة بنجاح!');
                document.getElementById('theme-name').value = '';
            });

            // Apply theme
            document.getElementById('apply-theme').addEventListener('click', function() {
                const selectedTheme = document.querySelector('.theme-option.active');
                if (selectedTheme) {
                    const themeId = selectedTheme.getAttribute('data-id');
                    const theme = db.settings.themes.find(t => t.id == themeId);

                    if (theme) {
                        // In a real application, this would update CSS variables
                        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
                        document.documentElement.style.setProperty('--secondary-color', theme.secondaryColor);
                        document.documentElement.style.setProperty('--dark-color', theme.darkColor);
                        document.documentElement.style.setProperty('--light-color', theme.lightColor);

                        alert(`تم تطبيق السمة "${theme.name}" بنجاح!`);
                    }
                }
            });

            // System settings form
            document.getElementById('system-settings-form').addEventListener('submit', function(e) {
                e.preventDefault();

                // In a real application, this would save to the database
                alert('تم حفظ إعدادات النظام بنجاح!');
            });

            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
    </script>
</body>
</html>
