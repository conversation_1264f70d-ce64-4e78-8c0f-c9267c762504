<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <link rel="stylesheet" href="../../assets/css/dark-mode.css">
    <link rel="stylesheet" href="../../assets/css/animations.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;700&family=Almarai:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #eee;
        }

        .theme-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .theme-option {
            border: 2px solid transparent;
            border-radius: 5px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-option.active {
            border-color: var(--primary-color);
        }

        .theme-preview {
            height: 100px;
            display: flex;
            flex-direction: column;
        }

        .theme-header {
            height: 30%;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: 500;
        }

        .theme-body {
            height: 70%;
            padding: 10px;
            display: flex;
        }

        .theme-sidebar {
            width: 30%;
            height: 100%;
        }

        .theme-content {
            width: 70%;
            height: 100%;
            background-color: #f5f5f5;
        }

        .theme-name {
            text-align: center;
            padding: 8px;
            font-weight: 500;
        }

        .notification-settings {
            margin-bottom: 20px;
        }

        .notification-option {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .notification-option input[type="checkbox"] {
            margin-left: 10px;
            width: auto;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .theme-options {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>ولي الأمر</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="children.html"><i class="fas fa-child"></i> الأبناء</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>الإعدادات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>المعلومات الشخصية</h2>
                <div class="settings-section">
                    <form id="profile-form">
                        <div class="form-group">
                            <label for="name">الاسم</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="content-card">
                <h2>تغيير كلمة المرور</h2>
                <div class="settings-section">
                    <form id="password-form">
                        <div class="form-group">
                            <label for="current-password">كلمة المرور الحالية</label>
                            <input type="password" id="current-password" name="current-password" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">كلمة المرور الجديدة</label>
                            <input type="password" id="new-password" name="new-password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" id="confirm-password" name="confirm-password" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="content-card">
                <h2>إعدادات الإشعارات</h2>
                <div class="settings-section">
                    <div class="notification-settings">
                        <div class="notification-option">
                            <input type="checkbox" id="order-notifications" checked>
                            <label for="order-notifications">إشعارات الطلبات الجديدة</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="balance-notifications" checked>
                            <label for="balance-notifications">إشعارات تغيير الرصيد</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="status-notifications" checked>
                            <label for="status-notifications">إشعارات تغيير حالة الطلب</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button id="save-notifications" class="btn btn-primary">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <h2>تخصيص المظهر</h2>
                <div class="settings-section">
                    <div class="theme-options" id="theme-options">
                        <!-- سيتم ملء هذا القسم ديناميكيًا -->
                    </div>
                    <div class="form-actions">
                        <button id="save-theme" class="btn btn-primary">تطبيق المظهر</button>
                        <a href="../settings/customization.html" class="btn btn-secondary">خيارات تخصيص متقدمة</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/theme-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is parent
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Fill profile form
            document.getElementById('name').value = currentUser.name || '';
            document.getElementById('email').value = currentUser.email || '';
            document.getElementById('phone').value = currentUser.phone || '';

            // Profile form submission
            document.getElementById('profile-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                const phone = document.getElementById('phone').value;

                // Update user in database
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].name = name;
                    db.users[userIndex].email = email;
                    db.users[userIndex].phone = phone;

                    // Update current user in session
                    currentUser.name = name;
                    currentUser.email = email;
                    currentUser.phone = phone;
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                    // Update user name display
                    document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + name + '</strong>';

                    saveDatabase(db);
                    alert('تم حفظ المعلومات الشخصية بنجاح!');
                }
            });

            // Password form submission
            document.getElementById('password-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                // Check if current password is correct
                if (currentPassword !== currentUser.password) {
                    alert('كلمة المرور الحالية غير صحيحة!');
                    return;
                }

                // Check if new passwords match
                if (newPassword !== confirmPassword) {
                    alert('كلمة المرور الجديدة وتأكيدها غير متطابقين!');
                    return;
                }

                // Update password in database
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].password = newPassword;

                    // Update current user in session
                    currentUser.password = newPassword;
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                    saveDatabase(db);
                    alert('تم تغيير كلمة المرور بنجاح!');

                    // Reset form
                    document.getElementById('password-form').reset();
                }
            });

            // Save notification settings
            document.getElementById('save-notifications').addEventListener('click', function() {
                const orderNotifications = document.getElementById('order-notifications').checked;
                const balanceNotifications = document.getElementById('balance-notifications').checked;
                const statusNotifications = document.getElementById('status-notifications').checked;

                // In a real application, these settings would be saved to the database
                alert('تم حفظ إعدادات الإشعارات بنجاح!');
            });

            // Populate theme options
            const themeOptions = document.getElementById('theme-options');
            db.settings.themes.forEach(theme => {
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.setAttribute('data-id', theme.id);

                // Set active theme
                if (theme.id === 1) { // Default theme
                    themeOption.classList.add('active');
                }

                themeOption.innerHTML = `
                    <div class="theme-preview">
                        <div class="theme-header" style="background-color: ${theme.primaryColor};">
                            العنوان
                        </div>
                        <div class="theme-body">
                            <div class="theme-sidebar" style="background-color: ${theme.darkColor};"></div>
                            <div class="theme-content"></div>
                        </div>
                    </div>
                    <div class="theme-name">${theme.name}</div>
                `;

                themeOptions.appendChild(themeOption);
            });

            // Theme selection
            document.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.theme-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                });
            });

            // Save theme
            document.getElementById('save-theme').addEventListener('click', function() {
                const selectedTheme = document.querySelector('.theme-option.active');
                if (selectedTheme) {
                    const themeId = selectedTheme.getAttribute('data-id');
                    const theme = db.settings.themes.find(t => t.id == themeId);

                    if (theme) {
                        // In a real application, this would update CSS variables
                        alert(`تم تطبيق المظهر "${theme.name}" بنجاح!`);
                    }
                }
            });

            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
    </script>
</body>
</html>
