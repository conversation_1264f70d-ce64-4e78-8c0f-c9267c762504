/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * أنماط الوضع الليلي (Dark Mode)
 */

/* متغيرات الألوان للوضع الليلي */
:root[data-theme="dark"],
.dark-mode {
    --primary-color: #43a047;
    --secondary-color: #ffb300;
    --dark-color: #2e7d32;
    --light-color: #1e3b2f;
    --text-color: #e0e0e0;
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --card-color: #252525;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --hover-color: rgba(255, 255, 255, 0.05);
    --active-color: rgba(255, 255, 255, 0.1);
    --disabled-color: #666666;
    --error-color: #cf6679;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --info-color: #2196f3;
}

/* زر تبديل الوضع الليلي/النهاري */
.dark-mode-toggle {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 15px;
    position: relative;
    overflow: hidden;
}

.dark-mode-toggle:hover {
    background-color: var(--hover-color);
}

.dark-mode-toggle:active {
    transform: scale(0.95);
}

.dark-mode-toggle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    z-index: 1;
}

.dark-mode-toggle:hover::after {
    width: 100%;
    height: 100%;
}

.dark-mode-toggle i {
    position: relative;
    z-index: 2;
}

.dark-mode .dark-mode-toggle,
:root[data-theme="dark"] .dark-mode-toggle {
    color: var(--secondary-color);
}

/* أنماط عامة للوضع الليلي */
.dark-mode body,
:root[data-theme="dark"] body {
    background-color: var(--background-color);
    color: var(--text-color);
}

/* تأثير انتقالي للوضع الليلي */
body {
    transition: background-color 0.5s ease, color 0.5s ease;
}

/* الشريط العلوي */
.dark-mode .header,
:root[data-theme="dark"] .header {
    background-color: var(--surface-color);
    box-shadow: 0 2px 10px var(--shadow-color);
}

.dark-mode .logo h1,
:root[data-theme="dark"] .logo h1 {
    color: var(--primary-color);
}

.dark-mode .main-nav ul li a,
:root[data-theme="dark"] .main-nav ul li a {
    color: var(--text-color);
}

.dark-mode .main-nav ul li a:hover,
.dark-mode .main-nav ul li a.active,
:root[data-theme="dark"] .main-nav ul li a:hover,
:root[data-theme="dark"] .main-nav ul li a.active {
    color: var(--secondary-color);
}

/* البطاقات والأقسام */
.dark-mode .content-card,
.dark-mode .stat-card,
.dark-mode .feature-card,
.dark-mode .meal-card,
.dark-mode .product-card,
.dark-mode .auth-container,
.dark-mode .modal-content,
.dark-mode .sidebar {
    background-color: var(--card-color);
    box-shadow: 0 5px 15px var(--shadow-color);
    border-color: var(--border-color);
}

.dark-mode .section-header h2,
.dark-mode .content-card h2,
.dark-mode .stat-card h3,
.dark-mode .feature-title,
.dark-mode .meal-title,
.dark-mode .product-info h3 {
    color: var(--primary-color);
}

.dark-mode .section-header p,
.dark-mode .feature-description,
.dark-mode .meal-description,
.dark-mode .about-text p {
    color: var(--text-color);
}

/* الجداول */
.dark-mode table {
    border-color: var(--border-color);
}

.dark-mode table th {
    background-color: var(--surface-color);
    color: var(--primary-color);
}

.dark-mode table td {
    border-color: var(--border-color);
}

.dark-mode table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.03);
}

.dark-mode table tr:hover {
    background-color: var(--hover-color);
}

/* النماذج */
.dark-mode input[type="text"],
.dark-mode input[type="email"],
.dark-mode input[type="password"],
.dark-mode input[type="number"],
.dark-mode input[type="tel"],
.dark-mode input[type="date"],
.dark-mode input[type="time"],
.dark-mode input[type="search"],
.dark-mode select,
.dark-mode textarea {
    background-color: var(--surface-color);
    color: var(--text-color);
    border-color: var(--border-color);
}

.dark-mode input[type="text"]:focus,
.dark-mode input[type="email"]:focus,
.dark-mode input[type="password"]:focus,
.dark-mode input[type="number"]:focus,
.dark-mode input[type="tel"]:focus,
.dark-mode input[type="date"]:focus,
.dark-mode input[type="time"]:focus,
.dark-mode input[type="search"]:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
}

.dark-mode input::placeholder,
.dark-mode textarea::placeholder {
    color: var(--disabled-color);
}

.dark-mode input[type="checkbox"],
.dark-mode input[type="radio"] {
    accent-color: var(--primary-color);
}

/* الأزرار */
.dark-mode .btn-primary {
    background-color: var(--primary-color);
    color: #ffffff;
}

.dark-mode .btn-primary:hover {
    background-color: var(--dark-color);
}

.dark-mode .btn-secondary {
    background-color: var(--secondary-color);
    color: #000000;
}

.dark-mode .btn-secondary:hover {
    background-color: #ffc107;
}

.dark-mode .btn-outline {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.dark-mode .btn-outline:hover {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* الشريط الجانبي */
.dark-mode .sidebar {
    background-color: var(--surface-color);
}

.dark-mode .sidebar-menu li a {
    color: var(--text-color);
}

.dark-mode .sidebar-menu li a:hover,
.dark-mode .sidebar-menu li a.active {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.dark-mode .sidebar-menu li a i {
    color: var(--primary-color);
}

/* الإشعارات */
.dark-mode .notification-dropdown {
    background-color: var(--card-color);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.dark-mode .notification-item {
    border-bottom-color: var(--border-color);
}

.dark-mode .notification-item:hover {
    background-color: var(--hover-color);
}

.dark-mode .notification-title {
    color: var(--primary-color);
}

.dark-mode .notification-message {
    color: var(--text-color);
}

.dark-mode .notification-date {
    color: var(--disabled-color);
}

/* الصفحة الرئيسية */
.dark-mode .hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/hero-bg.svg');
}

.dark-mode .features-section,
.dark-mode .about-section,
.dark-mode .contact-section {
    background-color: var(--background-color);
}

.dark-mode .feature-card,
.dark-mode .meal-card,
.dark-mode .testimonial-card {
    background-color: var(--card-color);
}

/* الشريط السفلي */
.dark-mode .footer {
    background-color: var(--surface-color);
    color: var(--text-color);
}

.dark-mode .footer-links a {
    color: var(--text-color);
}

.dark-mode .footer-links a:hover {
    color: var(--secondary-color);
}

.dark-mode .social-links a {
    color: var(--text-color);
    background-color: var(--hover-color);
}

.dark-mode .social-links a:hover {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* حالات الطلبات */
.dark-mode .status-badge {
    background-color: var(--surface-color);
}

.dark-mode .status-pending {
    background-color: rgba(255, 152, 0, 0.2);
    color: #ffb74d;
}

.dark-mode .status-processing {
    background-color: rgba(33, 150, 243, 0.2);
    color: #64b5f6;
}

.dark-mode .status-completed {
    background-color: rgba(76, 175, 80, 0.2);
    color: #81c784;
}

.dark-mode .status-cancelled {
    background-color: rgba(244, 67, 54, 0.2);
    color: #e57373;
}

/* تأثيرات الانتقال للوضع الليلي */
body {
    transition: background-color 0.5s ease, color 0.5s ease;
}

.header,
.content-card,
.stat-card,
.feature-card,
.meal-card,
.product-card,
.sidebar,
.footer,
input,
select,
textarea,
.btn,
table,
.notification-dropdown {
    transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
}
