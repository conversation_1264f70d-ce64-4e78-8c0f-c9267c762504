<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2e7d32">
    <meta name="description" content="صفحة تسجيل الدخول لنظام فواصل النجاح للخدمات الإعاشة - نظام إدارة المقاصف المدرسية">
    <meta name="author" content="فواصل النجاح للخدمات الإعاشة">
    <title>تسجيل الدخول - فواصل النجاح للخدمات الإعاشة</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- تعريف الأنماط الأساسية قبل تحميل ملفات CSS الخارجية -->
    <style>
        /* تعريف المتغيرات الأساسية */
        :root {
            --primary-color: #2e7d32;
            --secondary-color: #f9a825;
            --dark-color: #1b5e20;
            --light-color: #e8f5e9;
            --text-color: #333;
            --white-color: #fff;
            --beta-color: #ff5722;
            --beta-dark-color: #e64a19;
        }

        /* أنماط أساسية لمنع FOUC (Flash of Unstyled Content) */
        body {
            opacity: 1;
            visibility: visible;
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 25%, #e0f2f1 50%, #f3e5f5 75%, #fff3e0 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 30px 0 0 0; /* إضافة مساحة للشريط التجريبي */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            position: relative;
            overflow-x: hidden;
        }

        /* تأثير الخلفية المتحركة */
        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* عناصر هندسية في الخلفية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(46, 125, 50, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(249, 168, 37, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(46, 125, 50, 0.05) 0%, transparent 50%);
            z-index: -2;
            animation: float 20s ease-in-out infinite;
        }

        /* عناصر هندسية إضافية */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(45deg, transparent 40%, rgba(46, 125, 50, 0.03) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(249, 168, 37, 0.03) 50%, transparent 60%);
            z-index: -1;
            animation: rotate 30s linear infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) scale(1);
            }
            50% {
                transform: translateY(-20px) scale(1.05);
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* شريط تجريبي */
        .beta-banner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(45deg, var(--beta-dark-color), var(--beta-color));
            color: white;
            text-align: center;
            padding: 5px 0;
            font-weight: bold;
            z-index: 2000;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);
            animation: pulse 2s infinite;
        }

        .beta-banner span {
            display: inline-block;
            padding: 1px 8px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            margin: 0 4px;
            font-size: 0.8rem;
            transform: rotate(-2deg);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* تحسين توافق المتصفحات */
        html, body {
            width: 100%;
            height: 100%;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            overflow-x: hidden;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
        }

        /* تحسينات للطباعة */
        @media print {
            .beta-banner,
            .header,
            .auth-features,
            .geometric-shapes,
            .particles,
            .zoom-controls {
                display: none !important;
            }

            .auth-container {
                box-shadow: none !important;
                border: 1px solid #000 !important;
                transform: none !important;
            }

            .auth-left {
                background: #f5f5f5 !important;
            }
        }



        /* تأثيرات حركية */
        @-webkit-keyframes fadeIn {
            from { opacity: 0; -webkit-transform: translateY(20px); }
            to { opacity: 1; -webkit-transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @-webkit-keyframes fadeOut {
            from { opacity: 1; -webkit-transform: translate(-50%, -50%); }
            to { opacity: 0; -webkit-transform: translate(-50%, -60%); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, -50%); }
            to { opacity: 0; transform: translate(-50%, -60%); }
        }

        @-webkit-keyframes pulse {
            0% { -webkit-transform: scale(1); }
            50% { -webkit-transform: scale(1.05); }
            100% { -webkit-transform: scale(1); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .success-message, .error-message {
            animation: fadeIn 0.5s ease forwards;
            -webkit-animation: fadeIn 0.5s ease forwards;
        }
    </style>

    <!-- Google Fonts - تحميل مسبق -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome - تحميل مسبق -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <style>
        /* تحسينات خاصة بصفحة تسجيل الدخول */

        /* عناصر هندسية متحركة */
        .geometric-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: floatShapes 15s ease-in-out infinite;
        }

        .shape-1 {
            top: 10%;
            left: 10%;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape-2 {
            top: 20%;
            right: 15%;
            width: 40px;
            height: 40px;
            background: var(--secondary-color);
            transform: rotate(45deg);
            animation-delay: 2s;
        }

        .shape-3 {
            bottom: 30%;
            left: 20%;
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 20px;
            animation-delay: 4s;
        }

        .shape-4 {
            bottom: 15%;
            right: 25%;
            width: 50px;
            height: 50px;
            background: var(--secondary-color);
            border-radius: 50%;
            animation-delay: 6s;
        }

        .shape-5 {
            top: 50%;
            left: 5%;
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            transform: rotate(45deg);
            animation-delay: 8s;
        }

        .shape-6 {
            top: 70%;
            right: 10%;
            width: 70px;
            height: 70px;
            background: var(--secondary-color);
            border-radius: 35px;
            animation-delay: 10s;
        }

        @keyframes floatShapes {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.1;
            }
            25% {
                transform: translateY(-20px) rotate(90deg);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-10px) rotate(180deg);
                opacity: 0.15;
            }
            75% {
                transform: translateY(-30px) rotate(270deg);
                opacity: 0.25;
            }
        }

        /* تحسين عرض الصفحة على الأجهزة المختلفة */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
            position: relative;
            z-index: 1;
        }

        /* تحسينات إضافية للتجاوب */
        * {
            box-sizing: border-box;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        /* منع التمرير الأفقي */
        html, body {
            overflow-x: hidden;
        }

        /* تحسين الخطوط للشاشات الصغيرة */
        @media (max-width: 480px) {
            html {
                font-size: 14px;
            }
        }

        @media (max-width: 320px) {
            html {
                font-size: 13px;
            }
        }

        /* جسيمات متحركة */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -3;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.3;
            animation: particleFloat 20s linear infinite;
        }

        .particle:nth-child(2n) {
            background: var(--secondary-color);
            animation-duration: 25s;
        }

        .particle:nth-child(3n) {
            width: 6px;
            height: 6px;
            animation-duration: 30s;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* تحسين حجم الشريط العلوي */
        .header {
            padding: 12px 0;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .header .container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
        }

        .header .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .header .logo a {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: inherit;
        }

        .header .logo h1 {
            font-size: 1rem;
            margin: 0;
            line-height: 1.2;
            color: var(--text-color);
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 280px;
        }

        .header .logo i {
            font-size: 2rem;
            color: #4caf50;
            flex-shrink: 0;
            filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            animation: headerLogoAnimation 3s ease-in-out infinite;
            transition: all 0.3s ease;
            transform: rotate(-10deg);
        }

        .header .logo i:hover {
            transform: rotate(-5deg) scale(1.1);
            filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
        }

        /* تأثير متقدم للشعار الرئيسي */
        @keyframes headerLogoAnimation {
            0%, 100% {
                transform: rotate(-10deg) scale(1);
                filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            }
            25% {
                transform: rotate(-12deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
            50% {
                transform: rotate(-15deg) scale(1.05);
                filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
            }
            75% {
                transform: rotate(-18deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
        }

        /* تحسين القائمة الرئيسية */
        .main-nav {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .main-nav ul {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .main-nav li a {
            font-size: 0.85rem;
            padding: 8px 14px;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-color);
            white-space: nowrap;
        }

        .main-nav li a:hover,
        .main-nav li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* زر القائمة للأجهزة المحمولة */
        .menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
        }

        .menu-toggle i {
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        /* قسم تسجيل الدخول */
        .auth-section {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            min-height: calc(100vh - 60px);
            padding: 20px 20px;
            padding-top: 30px;
            padding-bottom: 30px;
        }

        /* تحسينات حاوية تسجيل الدخول - مدمجة ومتوسطة الحجم */
        .auth-container {
            max-width: 950px;
            width: 92%;
            margin: 0 auto;
            padding: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 10;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            -webkit-transition: opacity 0.3s ease, visibility 0.3s ease;
            display: flex;
            min-height: 480px;
            max-height: 530px;
            overflow: hidden;
        }

        /* الجانب الأيسر - الصورة والشعار */
        .auth-left {
            flex: 1;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px 15px;
            color: white;
            position: relative;
            overflow: hidden;
            border-radius: 15px 0 0 15px;
        }

        .auth-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .auth-logo {
            text-align: center;
            margin-bottom: 15px;
            z-index: 2;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .auth-logo::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(249, 168, 37, 0.15) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            0% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(0.8);
            }
            100% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        .auth-logo:hover i {
            transform: translateY(-8px) scale(1.08);
            filter: drop-shadow(0 0 30px rgba(249, 168, 37, 0.8));
            animation-duration: 1.5s;
        }

        .auth-logo:hover::before {
            opacity: 0.8;
            transform: translate(-50%, -50%) scale(1.4);
        }

        .auth-logo i {
            font-size: 3.5rem;
            margin-bottom: 10px;
            display: block;
            color: #4caf50;
            text-shadow: 0 4px 20px rgba(0,0,0,0.4);
            position: relative;
            z-index: 10;
            animation: logoIconAnimation 4s ease-in-out infinite;
            filter: drop-shadow(0 0 25px rgba(76, 175, 80, 0.8));
            cursor: pointer;
            transition: all 0.3s ease;
            transform: rotate(-15deg);
        }

        .auth-logo i:hover {
            animation: logoIconHover 0.8s ease-in-out infinite;
            filter: drop-shadow(0 0 35px rgba(76, 175, 80, 1));
            transform: scale(1.1) rotate(-10deg);
        }

        /* تأثير متقدم للشعار الرئيسي */
        @keyframes logoIconAnimation {
            0%, 100% {
                transform: translateY(0px) scale(1) rotate(-15deg);
                filter: drop-shadow(0 0 25px rgba(76, 175, 80, 0.8));
            }
            25% {
                transform: translateY(-5px) scale(1.02) rotate(-12deg);
                filter: drop-shadow(0 0 30px rgba(76, 175, 80, 0.9));
            }
            50% {
                transform: translateY(-8px) scale(1.05) rotate(-15deg);
                filter: drop-shadow(0 0 35px rgba(76, 175, 80, 1));
            }
            75% {
                transform: translateY(-5px) scale(1.02) rotate(-18deg);
                filter: drop-shadow(0 0 30px rgba(76, 175, 80, 0.9));
            }
        }

        @keyframes logoIconHover {
            0%, 100% {
                transform: scale(1.1) rotate(-10deg);
            }
            50% {
                transform: scale(1.2) rotate(-5deg);
            }
        }

        .auth-logo h1 {
            font-size: 1.3rem;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .auth-logo p {
            font-size: 0.85rem;
            margin: 0;
            opacity: 0.9;
            font-weight: 500;
            text-shadow: 0 1px 5px rgba(0,0,0,0.2);
        }

        .auth-features {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 2;
            position: relative;
        }

        .auth-features li {
            display: flex;
            align-items: center;
            padding: 4px 0;
            font-size: 0.75rem;
            font-weight: 500;
            opacity: 0.95;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .auth-features li:hover {
            opacity: 1;
            transform: translateX(-5px);
            text-shadow: 0 1px 5px rgba(0,0,0,0.3);
        }

        .auth-features i {
            margin-left: 8px;
            font-size: 0.9rem;
            color: var(--secondary-color);
            text-shadow: 0 1px 5px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .auth-features li:hover i {
            transform: scale(1.2);
            filter: drop-shadow(0 0 10px rgba(249, 168, 37, 0.8));
        }

        /* الجانب الأيمن - النموذج */
        .auth-right {
            flex: 1.5;
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 0 15px 15px 0;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .auth-header h2 {
            font-size: 1.5rem;
            margin: 0 0 6px 0;
            color: var(--text-color);
            font-weight: 700;
            letter-spacing: 0.3px;
        }

        .auth-header p {
            font-size: 0.85rem;
            margin: 0;
            color: #666;
            font-weight: 400;
        }

        /* محدد نوع المستخدم المتقدم */
        .user-type-selector {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-bottom: 18px;
            padding: 0;
        }

        .user-type-option {
            background: rgba(248, 249, 250, 0.8);
            border: 2px solid rgba(233, 236, 239, 0.6);
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            min-height: 85px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .user-type-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .user-type-option:hover::before {
            left: 100%;
        }

        .user-type-option:hover {
            border-color: var(--primary-color);
            background: rgba(232, 245, 233, 0.9);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
        }

        .user-type-option.active {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3);
        }

        .user-type-option.active .user-content span {
            color: white;
        }

        .user-type-option.active .user-description {
            color: rgba(255, 255, 255, 0.9);
        }

        .user-icon-wrapper {
            position: relative;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-type-option i {
            font-size: 28px;
            margin-bottom: 0;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
            animation: iconBreathing 3s ease-in-out infinite;
        }

        /* تأثيرات الأيقونات المتقدمة */
        @keyframes iconBreathing {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
        }

        /* تأثيرات مختلفة لكل نوع مستخدم */
        .user-type-option[data-type="student"]:hover i {
            animation: iconJump 0.8s ease-in-out;
            color: #2196f3;
        }

        .user-type-option[data-type="parent"]:hover i {
            animation: iconWave 1s ease-in-out;
            color: #e91e63;
        }

        .user-type-option[data-type="school"]:hover i {
            animation: iconPulseBig 1.2s ease-in-out;
            color: #ff9800;
        }

        .user-type-option[data-type="admin"]:hover i {
            animation: iconRotateLoop 2s linear;
            color: #9c27b0;
        }

        .user-type-option[data-type="staff"]:hover i {
            animation: iconShake 0.5s ease-in-out;
            color: #607d8b;
        }

        @keyframes iconJump {
            0%, 100% { transform: translateY(0) scale(1); }
            25% { transform: translateY(-8px) scale(1.1); }
            50% { transform: translateY(-12px) scale(1.15); }
            75% { transform: translateY(-8px) scale(1.1); }
        }

        @keyframes iconWave {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(-10deg) scale(1.1); }
            50% { transform: rotate(10deg) scale(1.15); }
            75% { transform: rotate(-5deg) scale(1.1); }
        }

        @keyframes iconPulseBig {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.3); }
        }

        @keyframes iconRotateLoop {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.2); }
            100% { transform: rotate(360deg) scale(1); }
        }

        @keyframes iconShake {
            0%, 100% { transform: translateX(0) scale(1); }
            25% { transform: translateX(-3px) scale(1.05); }
            50% { transform: translateX(3px) scale(1.1); }
            75% { transform: translateX(-2px) scale(1.05); }
        }

        .icon-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 45px;
            height: 45px;
            background: radial-gradient(circle, rgba(46, 125, 50, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .user-type-option:hover .icon-glow {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.5);
        }

        .user-type-option.active .icon-glow {
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .user-content {
            z-index: 2;
            position: relative;
        }

        .user-content span {
            font-size: 0.7rem;
            font-weight: 600;
            display: block;
            margin-bottom: 1px;
            transition: all 0.3s ease;
            line-height: 1.1;
        }

        .user-description {
            font-size: 0.6rem;
            opacity: 0.7;
            margin-top: 1px;
            line-height: 1.1;
            transition: all 0.3s ease;
        }

        .user-type-option:hover .user-description {
            opacity: 0.9;
        }

        /* ===== أدوات التكبير والتصغير المتقدمة ===== */

        .zoom-controls {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .zoom-controls:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* تأثير النبضة لجذب الانتباه */
        .zoom-controls {
            animation: zoomControlsPulse 3s ease-in-out infinite;
        }

        @keyframes zoomControlsPulse {
            0%, 100% {
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            50% {
                box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
            }
        }

        /* عنوان أدوات التكبير */
        .zoom-controls-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
            padding: 4px 8px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--primary-color);
            text-align: center;
            justify-content: center;
        }

        .zoom-controls-header i {
            font-size: 0.8rem;
        }

        .zoom-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .zoom-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .zoom-btn:hover::before {
            left: 100%;
        }

        .zoom-btn:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .zoom-btn:active {
            transform: scale(0.95);
        }

        .zoom-btn.zoom-in {
            background: linear-gradient(135deg, #4caf50, #2e7d32);
        }

        .zoom-btn.zoom-out {
            background: linear-gradient(135deg, #f44336, #c62828);
        }

        .zoom-btn.zoom-reset {
            background: linear-gradient(135deg, #2196f3, #1565c0);
        }

        .zoom-indicator {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            text-align: center;
            font-size: 0.9rem;
            font-weight: 600;
            min-width: 60px;
            transition: all 0.3s ease;
        }

        .zoom-indicator.updating {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        /* تأثيرات التكبير على الحاوية */
        .auth-container {
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center center;
        }

        .auth-container.zooming {
            transition: transform 0.2s ease;
        }

        /* حالات التكبير المختلفة */
        .zoom-level-50 { transform: scale(0.5); }
        .zoom-level-75 { transform: scale(0.75); }
        .zoom-level-100 { transform: scale(1); }
        .zoom-level-125 { transform: scale(1.25); }
        .zoom-level-150 { transform: scale(1.5); }
        .zoom-level-175 { transform: scale(1.75); }
        .zoom-level-200 { transform: scale(2); }

        /* تأثيرات خاصة للتكبير */
        @keyframes zoomPulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
            }
        }

        .zoom-btn.zoom-in.active {
            animation: zoomPulse 1s ease-in-out infinite;
        }

        @keyframes zoomOutPulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
            }
        }

        .zoom-btn.zoom-out.active {
            animation: zoomOutPulse 1s ease-in-out infinite;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .zoom-controls {
                top: 100px;
                right: 15px;
                padding: 8px;
                gap: 6px;
            }

            .zoom-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .zoom-indicator {
                font-size: 0.8rem;
                padding: 6px 8px;
            }
        }

        @media (max-width: 480px) {
            .zoom-controls {
                top: 80px;
                right: 10px;
                padding: 6px;
                gap: 4px;
                flex-direction: row;
                border-radius: 12px;
            }

            .zoom-controls-header {
                display: none; /* إخفاء العنوان على الشاشات الصغيرة */
            }

            .zoom-btn {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
                border-radius: 8px;
            }

            .zoom-indicator {
                font-size: 0.7rem;
                padding: 4px 6px;
                min-width: 45px;
            }
        }

        /* إخفاء أدوات التكبير عند الطباعة */
        @media print {
            .zoom-controls {
                display: none !important;
            }
        }

        /* تحسينات للنموذج */
        .auth-form {
            margin-top: 15px;
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .form-group label {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.85rem;
            gap: 6px;
        }

        .form-group label i {
            font-size: 0.9rem;
            color: var(--primary-color);
            width: 16px;
            text-align: center;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 0.9rem;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px 12px 35px;
            border: 2px solid rgba(233, 236, 239, 0.8);
            border-radius: 8px;
            font-size: 0.9rem;
            font-family: inherit;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
            transform: translateY(-1px);
        }

        .form-group input:focus + .input-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        .form-group input::placeholder {
            color: #aaa;
            font-style: italic;
            font-size: 0.85rem;
        }

        /* تأثيرات خاصة للأيقونات */
        .input-icon.fas {
            filter: drop-shadow(0 0 3px rgba(46, 125, 50, 0.3));
        }

        .form-group:hover .input-icon {
            color: var(--secondary-color);
            transform: translateY(-50%) scale(1.05);
        }

        .btn {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .auth-footer {
            margin-top: 18px;
            text-align: center;
        }

        .auth-footer p {
            margin: 6px 0;
            color: #666;
            font-size: 0.75rem;
            line-height: 1.3;
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-footer a:hover {
            text-decoration: underline;
            color: var(--dark-color);
        }

        /* استعلامات الوسائط للتجاوب */
        @media (max-width: 768px) {
            /* تحسينات الشريط العلوي للأجهزة المتوسطة */
            .header .logo h1 {
                font-size: 0.8rem;
                max-width: 200px;
            }

            .header .logo i {
                font-size: 1.6rem;
                transform: rotate(-8deg);
            }

            .main-nav ul {
                gap: 12px;
            }

            .main-nav li a {
                font-size: 0.75rem;
                padding: 5px 8px;
            }

            .auth-container {
                flex-direction: column;
                max-width: 500px;
                width: 95%;
                min-height: auto;
                max-height: none;
            }

            .auth-left {
                flex: none;
                padding: 15px;
                border-radius: 15px 15px 0 0;
            }

            .auth-right {
                flex: none;
                padding: 20px 15px;
                border-radius: 0 0 15px 15px;
            }

            .user-type-selector {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
            }

            .user-type-option {
                min-height: 75px;
                padding: 10px 6px;
            }

            .user-type-option i {
                font-size: 24px;
            }

            .user-content span {
                font-size: 0.65rem;
            }

            .user-description {
                font-size: 0.55rem;
            }
        }

        @media (max-width: 480px) {
            /* تحسينات الشريط العلوي للأجهزة الصغيرة */
            .header {
                padding: 8px 0;
                min-height: 50px;
            }

            .header .logo {
                height: 45px;
                padding: 5px 8px;
                margin-left: 10px;
            }

            .header .logo h1 {
                font-size: 0.9rem;
                max-width: 160px;
            }

            .header .logo i {
                font-size: 1.8rem;
                transform: rotate(-6deg);
            }

            .main-nav {
                display: none; /* إخفاء القائمة على الشاشات الصغيرة جداً */
            }

            .menu-toggle {
                display: flex;
            }

            .auth-section {
                padding: 15px 10px;
                padding-top: 25px;
            }

            .auth-container {
                max-width: 400px;
                width: 98%;
            }

            .auth-left {
                padding: 12px;
            }

            .auth-right {
                padding: 15px 12px;
            }

            .auth-logo i {
                font-size: 2.5rem;
            }

            .auth-logo h1 {
                font-size: 1.1rem;
            }

            .auth-logo p {
                font-size: 0.75rem;
            }

            .auth-features li {
                font-size: 0.7rem;
            }

            .auth-header h2 {
                font-size: 1.3rem;
            }

            .auth-header p {
                font-size: 0.8rem;
            }

            .user-type-selector {
                grid-template-columns: repeat(2, 1fr);
                gap: 5px;
            }

            .user-type-option {
                min-height: 65px;
                padding: 8px 4px;
            }

            .user-type-option i {
                font-size: 20px;
            }

            .user-content span {
                font-size: 0.6rem;
            }

            .user-description {
                font-size: 0.5rem;
            }

            .form-group input {
                padding: 10px 12px;
                font-size: 0.85rem;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.85rem;
            }

            .auth-footer p {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 320px) {
            /* تحسينات للشاشات الصغيرة جداً */
            .header {
                padding: 6px 0;
                min-height: 45px;
            }

            .header .logo {
                height: 40px;
                padding: 4px 6px;
                margin-left: 8px;
            }

            .header .logo h1 {
                font-size: 0.8rem;
                max-width: 120px;
            }

            .header .logo i {
                font-size: 1.5rem;
                transform: rotate(-5deg);
            }

            .auth-section {
                padding-top: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Beta Banner -->
    <div class="beta-banner">
        <span>تجريبي</span> هذا النظام في مرحلة تجريبية وقد يخضع للتغييرات والتحسينات المستمرة
    </div>
    <noscript>
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: #fff; z-index: 10000; display: flex; justify-content: center; align-items: center; text-align: center; padding: 20px;">
            <div>
                <h2 style="color: #e74c3c;">يرجى تفعيل JavaScript</h2>
                <p>هذا الموقع يتطلب تفعيل JavaScript للعمل بشكل صحيح.</p>
            </div>
        </div>
    </noscript>

    <!-- إضافة عنصر للتحقق من حالة تحميل الصفحة -->
    <div id="page-load-check" style="display: none;"></div>

    <!-- عناصر هندسية متحركة في الخلفية -->
    <div class="geometric-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
    </div>

    <!-- جسيمات متحركة -->
    <div class="particles" id="particles"></div>

    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="../../index.html">
                    <i class="fas fa-utensils"></i>
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                </a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="../../index.html">الرئيسية</a></li>
                    <li><a href="../../index.html#features">المميزات</a></li>
                    <li><a href="../../index.html#meals">الوجبات</a></li>
                    <li><a href="../../index.html#about">عن المقصف</a></li>
                    <li><a href="../../index.html#contact">اتصل بنا</a></li>
                    <li><a href="login.html" class="btn btn-primary active">تسجيل الدخول</a></li>
                </ul>
            </nav>
            <div class="menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Login Section -->
    <section class="auth-section">
        <div class="container">
            <!-- أيقونة التكبير والتصغير المتقدمة -->
            <div class="zoom-controls">
                <div class="zoom-controls-header">
                    <i class="fas fa-search"></i>
                    <span>التكبير</span>
                </div>
                <button class="zoom-btn zoom-in" id="zoom-in" title="تكبير الشاشة (Ctrl + +)">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="zoom-btn zoom-out" id="zoom-out" title="تصغير الشاشة (Ctrl + -)">
                    <i class="fas fa-search-minus"></i>
                </button>
                <button class="zoom-btn zoom-reset" id="zoom-reset" title="إعادة تعيين الحجم (Ctrl + 0)">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <div class="zoom-indicator" id="zoom-indicator">100%</div>
            </div>

            <div class="auth-container" id="auth-container">
                <!-- الجانب الأيسر - الشعار والميزات -->
                <div class="auth-left">
                    <div class="auth-logo">
                        <i class="fas fa-utensils"></i>
                        <div>
                            <h1>Smart Canteen</h1>
                            <p>سمارت مقصف</p>
                        </div>
                    </div>

                    <ul class="auth-features">
                        <li>
                            <i class="fas fa-shield-alt"></i>
                            نظام آمن ومحمي
                        </li>
                        <li>
                            <i class="fas fa-mobile-alt"></i>
                            سهل الاستخدام
                        </li>
                        <li>
                            <i class="fas fa-chart-line"></i>
                            تقارير مفصلة
                        </li>
                        <li>
                            <i class="fas fa-clock"></i>
                            متاح 24/7
                        </li>
                        <li>
                            <i class="fas fa-users"></i>
                            دعم متعدد المستخدمين
                        </li>
                    </ul>
                </div>

                <!-- الجانب الأيمن - النموذج -->
                <div class="auth-right">
                    <div class="auth-header">
                        <h2>تسجيل الدخول</h2>
                        <p>أدخل بيانات الدخول للوصول إلى حسابك</p>
                    </div>

                    <div class="user-type-selector">
                        <div class="user-type-option active" data-type="student">
                            <div class="user-icon-wrapper">
                                <i class="fas fa-graduation-cap"></i>
                                <div class="icon-glow"></div>
                            </div>
                            <div class="user-content">
                                <span>طالب</span>
                                <div class="user-description">الطلاب والدارسين</div>
                            </div>
                        </div>
                        <div class="user-type-option" data-type="parent">
                            <div class="user-icon-wrapper">
                                <i class="fas fa-heart"></i>
                                <div class="icon-glow"></div>
                            </div>
                            <div class="user-content">
                                <span>ولي أمر</span>
                                <div class="user-description">أولياء الأمور</div>
                            </div>
                        </div>
                        <div class="user-type-option" data-type="school">
                            <div class="user-icon-wrapper">
                                <i class="fas fa-building"></i>
                                <div class="icon-glow"></div>
                            </div>
                            <div class="user-content">
                                <span>مدير مدرسة</span>
                                <div class="user-description">إدارة المدارس</div>
                            </div>
                        </div>
                        <div class="user-type-option" data-type="admin">
                            <div class="user-icon-wrapper">
                                <i class="fas fa-crown"></i>
                                <div class="icon-glow"></div>
                            </div>
                            <div class="user-content">
                                <span>مدير النظام</span>
                                <div class="user-description">الإدارة العليا</div>
                            </div>
                        </div>
                        <div class="user-type-option" data-type="staff">
                            <div class="user-icon-wrapper">
                                <i class="fas fa-users-cog"></i>
                                <div class="icon-glow"></div>
                            </div>
                            <div class="user-content">
                                <span>العاملين</span>
                                <div class="user-description">فريق العمل</div>
                            </div>
                        </div>
                    </div>

                    <form class="auth-form" id="loginForm">
                        <div class="form-group">
                            <label for="username">
                                <i class="fas fa-user"></i>
                                اسم المستخدم
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="username" name="username" placeholder="أدخل اسم المستخدم أو البريد الإلكتروني" required>
                                <i class="fas fa-user input-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="password">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <div class="input-wrapper">
                                <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required>
                                <i class="fas fa-lock input-icon"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <div class="auth-footer">
                        <p>ليس لديك حساب؟ <a href="register.html">إنشاء حساب جديد</a></p>
                        <p style="margin-top: 10px; font-size: 0.9rem;">للدخول إلى صفحة العاملين المخصصة: <a href="../staff/login.html">صفحة تسجيل دخول العاملين</a></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>فواصل النجاح للخدمات الإعاشة</h2>
                    <p>نظام متكامل لإدارة المقاصف المدرسية</p>
                </div>
                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="../../index.html">الرئيسية</a></li>
                        <li><a href="../../index.html#about">عن الخدمة</a></li>
                        <li><a href="../../index.html#services">خدماتنا</a></li>
                        <li><a href="../../index.html#contact">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-social">
                    <h3>تابعنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 فواصل النجاح للخدمات الإعاشة. جميع الحقوق محفوظة.</p>

                <div class="developer-info">
                    <div class="developer-info-text">
                        <i class="fas fa-code"></i>
                        <span>تطوير:</span>
                        <strong>محمد الأشرافي جابر</strong>
                    </div>
                    <div class="developer-info-text">
                        <i class="fas fa-phone"></i>
                        <a href="tel:0532969067" dir="ltr">0532969067</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../../assets/js/main.js"></script>
    <script>
        // ===== تحسينات متقدمة لصفحة تسجيل الدخول =====

        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء الجسيمات المتحركة
            createParticles();

            // تفعيل أدوات التكبير والتصغير
            initZoomControls();

            // معالجة اختيار نوع المستخدم
            initUserTypeSelector();

            // معالجة تقديم النموذج
            initLoginForm();

            // تفعيل التأثيرات التفاعلية
            initInteractiveEffects();

            // تفعيل اختصارات لوحة المفاتيح
            initKeyboardShortcuts();
        });

        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // موضع عشوائي
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // تفعيل أدوات التكبير والتصغير المتقدمة
        function initZoomControls() {
            let currentZoom = 100;
            const authContainer = document.getElementById('auth-container');
            const zoomIndicator = document.getElementById('zoom-indicator');
            const zoomInBtn = document.getElementById('zoom-in');
            const zoomOutBtn = document.getElementById('zoom-out');
            const zoomResetBtn = document.getElementById('zoom-reset');

            if (!authContainer || !zoomIndicator) return;

            function updateZoom(newZoom) {
                currentZoom = Math.max(50, Math.min(200, newZoom));

                // إزالة جميع فئات التكبير السابقة
                authContainer.className = authContainer.className.replace(/zoom-level-\d+/g, '');

                // إضافة فئة التكبير الجديدة
                authContainer.classList.add(`zoom-level-${currentZoom}`);

                // تحديث المؤشر
                zoomIndicator.textContent = currentZoom + '%';
                zoomIndicator.classList.add('updating');

                setTimeout(() => {
                    zoomIndicator.classList.remove('updating');
                }, 300);
            }

            // أزرار التكبير والتصغير
            if (zoomInBtn) {
                zoomInBtn.addEventListener('click', () => {
                    updateZoom(currentZoom + 25);
                    zoomInBtn.classList.add('active');
                    setTimeout(() => zoomInBtn.classList.remove('active'), 200);
                });
            }

            if (zoomOutBtn) {
                zoomOutBtn.addEventListener('click', () => {
                    updateZoom(currentZoom - 25);
                    zoomOutBtn.classList.add('active');
                    setTimeout(() => zoomOutBtn.classList.remove('active'), 200);
                });
            }

            if (zoomResetBtn) {
                zoomResetBtn.addEventListener('click', () => {
                    updateZoom(100);
                    zoomResetBtn.classList.add('active');
                    setTimeout(() => zoomResetBtn.classList.remove('active'), 200);
                });
            }
        }

        // معالجة اختيار نوع المستخدم مع تأثيرات متقدمة
        function initUserTypeSelector() {
            const userTypeOptions = document.querySelectorAll('.user-type-option');

            userTypeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    console.log('تم النقر على نوع المستخدم:', this.getAttribute('data-type'));

                    // إزالة التحديد من جميع الخيارات
                    userTypeOptions.forEach(opt => {
                        opt.classList.remove('active');
                        const icon = opt.querySelector('i');
                        if (icon) {
                            icon.style.animation = '';
                        }
                    });

                    // تحديد الخيار الحالي
                    this.classList.add('active');

                    // تحديث عنوان الصفحة حسب نوع المستخدم
                    const userType = this.getAttribute('data-type');
                    const authHeader = document.querySelector('.auth-header h2');
                    if (authHeader) {
                        const userTypeNames = {
                            'student': 'تسجيل دخول الطلاب',
                            'parent': 'تسجيل دخول أولياء الأمور',
                            'staff': 'تسجيل دخول العاملين',
                            'admin': 'تسجيل دخول المديرين',
                            'school': 'تسجيل دخول المدارس'
                        };
                        authHeader.textContent = userTypeNames[userType] || 'تسجيل الدخول';
                    }

                    // تأثير خاص عند التحديد
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.animation = 'iconPulseBig 0.6s ease-out';
                        setTimeout(() => {
                            icon.style.animation = '';
                        }, 600);
                    }

                    // تأثير صوتي (اختياري)
                    playClickSound();

                    console.log('تم تحديد نوع المستخدم بنجاح:', userType);
                });

                // تأثيرات التمرير المتقدمة
                option.addEventListener('mouseenter', function() {
                    const iconGlow = this.querySelector('.icon-glow');
                    if (iconGlow) {
                        iconGlow.style.opacity = '1';
                        iconGlow.style.transform = 'translate(-50%, -50%) scale(1.5)';
                    }
                });

                option.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        const iconGlow = this.querySelector('.icon-glow');
                        if (iconGlow) {
                            iconGlow.style.opacity = '0';
                            iconGlow.style.transform = 'translate(-50%, -50%) scale(1)';
                        }
                    }
                });
            });
        }

        // معالجة تقديم النموذج مع تحسينات
        function initLoginForm() {
            const loginForm = document.getElementById('loginForm');
            if (!loginForm) return;

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                const activeOption = document.querySelector('.user-type-option.active');
                const userType = activeOption ? activeOption.getAttribute('data-type') : 'student';

                console.log('محاولة تسجيل دخول:', {
                    username: username,
                    userType: userType,
                    hasPassword: !!password
                });

                // التحقق من صحة البيانات
                if (!username || !password) {
                    showMessage('الرجاء إدخال اسم المستخدم وكلمة المرور', 'error');
                    return;
                }

                if (!userType) {
                    showMessage('الرجاء اختيار نوع المستخدم', 'error');
                    return;
                }

                // إظهار مؤشر التحميل
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
                submitBtn.disabled = true;

                // محاولة تسجيل الدخول
                setTimeout(() => {
                    try {
                        // نظام إدارة المستخدمين المتقدم - متوافق مع قاعدة البيانات الحالية
                        const userManager = {
                            getDatabase() {
                                try {
                                    const db = localStorage.getItem('cafeteriaDB');
                                    return db ? JSON.parse(db) : null;
                                } catch (error) {
                                    console.error('خطأ في قراءة قاعدة البيانات:', error);
                                    return null;
                                }
                            },

                            findUser(username, password, userType) {
                                const db = this.getDatabase();
                                if (!db || !db.users) {
                                    console.error('قاعدة البيانات غير متوفرة');
                                    return null;
                                }

                                console.log('البحث في قاعدة البيانات:', {
                                    totalUsers: db.users.length,
                                    searchUsername: username,
                                    searchUserType: userType
                                });

                                // البحث عن المستخدم باستخدام role بدلاً من userType
                                const user = db.users.find(user =>
                                    (user.username === username || user.email === username) &&
                                    user.password === password &&
                                    user.role === userType &&
                                    (!user.status || user.status === 'active') // التحقق من الحالة النشطة
                                );

                                console.log('نتيجة البحث:', {
                                    userFound: !!user,
                                    userRole: user ? user.role : 'غير موجود',
                                    userName: user ? user.name : 'غير موجود'
                                });

                                return user;
                            },

                            setCurrentUser(user) {
                                const userSession = {
                                    id: user.id,
                                    username: user.username,
                                    name: user.name,
                                    email: user.email || '',
                                    role: user.role,
                                    schoolId: user.schoolId,
                                    loginTime: new Date().toISOString()
                                };
                                sessionStorage.setItem('currentUser', JSON.stringify(userSession));
                                localStorage.setItem('currentUserRole', user.role);
                                localStorage.setItem('currentUserId', user.id.toString());
                            }
                        };

                        // البحث عن المستخدم
                        const user = userManager.findUser(username, password, userType);

                        if (user) {
                            // حفظ بيانات المستخدم
                            userManager.setCurrentUser(user);

                            showMessage(`مرحباً ${user.name}! تم تسجيل الدخول بنجاح`, 'success');

                            // التوجيه حسب نوع المستخدم
                            setTimeout(() => {
                                switch(user.role) {
                                    case 'admin':
                                        window.location.href = '../admin/dashboard.html';
                                        break;
                                    case 'staff':
                                        window.location.href = '../staff/pos-simple.html';
                                        break;
                                    case 'student':
                                        window.location.href = '../student/dashboard.html';
                                        break;
                                    case 'parent':
                                        window.location.href = '../parent/dashboard.html';
                                        break;
                                    case 'school':
                                        window.location.href = '../school/dashboard.html';
                                        break;
                                    default:
                                        showMessage('نوع المستخدم غير صحيح', 'error');
                                        submitBtn.innerHTML = originalText;
                                        submitBtn.disabled = false;
                                }
                            }, 1500);
                        } else {
                            showMessage('اسم المستخدم أو كلمة المرور غير صحيحة أو نوع المستخدم غير مطابق', 'error');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }
                    } catch (error) {
                        console.error('خطأ في تسجيل الدخول:', error);
                        showMessage('حدث خطأ أثناء تسجيل الدخول', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 1000);
            });
        }

        // تفعيل التأثيرات التفاعلية
        function initInteractiveEffects() {
            // تأثيرات الشعار
            const authLogo = document.querySelector('.auth-logo');
            if (authLogo) {
                authLogo.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.animation = 'logoIconHover 0.8s ease-in-out';
                        setTimeout(() => {
                            icon.style.animation = '';
                        }, 800);
                    }
                });
            }

            // تأثيرات الميزات
            const featureItems = document.querySelectorAll('.auth-features li');
            featureItems.forEach(item => {
                item.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'scale(1.3) rotate(360deg)';
                        setTimeout(() => {
                            icon.style.transform = '';
                        }, 500);
                    }
                });
            });
        }

        // تفعيل اختصارات لوحة المفاتيح
        function initKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl + Plus للتكبير
                if (e.ctrlKey && (e.key === '+' || e.key === '=')) {
                    e.preventDefault();
                    document.getElementById('zoom-in')?.click();
                }

                // Ctrl + Minus للتصغير
                if (e.ctrlKey && e.key === '-') {
                    e.preventDefault();
                    document.getElementById('zoom-out')?.click();
                }

                // Ctrl + 0 لإعادة التعيين
                if (e.ctrlKey && e.key === '0') {
                    e.preventDefault();
                    document.getElementById('zoom-reset')?.click();
                }

                // Enter لتقديم النموذج
                if (e.key === 'Enter' && !e.shiftKey) {
                    const activeElement = document.activeElement;
                    if (activeElement && (activeElement.id === 'username' || activeElement.id === 'password')) {
                        e.preventDefault();
                        document.getElementById('loginForm')?.dispatchEvent(new Event('submit'));
                    }
                }
            });
        }

        // عرض الرسائل مع تأثيرات متقدمة
        function showMessage(message, type = 'info') {
            // إزالة الرسائل السابقة
            const existingMessages = document.querySelectorAll('.message-popup');
            existingMessages.forEach(msg => msg.remove());

            const messageDiv = document.createElement('div');
            messageDiv.className = `message-popup ${type}-message`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // إضافة الأنماط
            messageDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: 600;
                min-width: 300px;
                text-align: center;
                animation: fadeIn 0.5s ease forwards;
            `;

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'fadeOut 0.5s ease forwards';
                setTimeout(() => {
                    messageDiv.remove();
                }, 500);
            }, 3000);
        }

        // تأثير صوتي (اختياري)
        function playClickSound() {
            // يمكن إضافة ملف صوتي هنا
            // const audio = new Audio('../../assets/sounds/click.mp3');
            // audio.play().catch(() => {});
        }

        // تحسينات الأداء
        function optimizePerformance() {
            // تقليل التأثيرات على الأجهزة الضعيفة
            if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
                document.body.classList.add('low-performance');
            }

            // تقليل الجسيمات على الشاشات الصغيرة
            if (window.innerWidth < 768) {
                const particles = document.querySelectorAll('.particle');
                particles.forEach((particle, index) => {
                    if (index % 2 === 0) {
                        particle.remove();
                    }
                });
            }
        }

        // تفعيل تحسينات الأداء
        optimizePerformance();

        // إعادة تحسين الأداء عند تغيير حجم النافذة
        window.addEventListener('resize', optimizePerformance);
    </script>
</body>
</html>
