<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2e7d32">
    <meta name="description" content="التقارير - نظام فواصل النجاح للخدمات الإعاشة">
    <title>التقارير - فواصل النجاح للخدمات الإعاشة</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">

    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .report-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
        }

        .filter-group label {
            margin-left: 10px;
            font-weight: 500;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            margin-right: auto;
        }

        .filter-btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .filter-btn i {
            margin-left: 8px;
        }

        .apply-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .export-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
        }

        .print-btn {
            background-color: #2196f3;
            color: white;
            border: none;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 30px;
        }

        .report-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .report-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .report-tab.active {
            border-bottom: 2px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: 500;
        }

        .report-content {
            display: none;
        }

        .report-content.active {
            display: block;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .summary-card h3 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .summary-card p {
            color: #666;
            font-size: 0.9rem;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .main-content {
                padding: 10px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .page-header h1 {
                margin-bottom: 10px;
            }

            .report-filters {
                flex-direction: column;
            }

            .filter-actions {
                margin-top: 10px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="schools.html"><i class="fas fa-school"></i> إدارة المدارس</a></li>
                <li><a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> إدارة العاملين</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="reports.html" class="active"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>التقارير</h1>
                <div class="filter-actions">
                    <button id="export-btn" class="filter-btn export-btn"><i class="fas fa-file-export"></i> تصدير</button>
                    <button id="print-btn" class="filter-btn print-btn"><i class="fas fa-print"></i> طباعة</button>
                </div>
            </div>

            <div class="content-card">
                <div class="report-filters">
                    <div class="filter-group">
                        <label for="report-type">نوع التقرير:</label>
                        <select id="report-type">
                            <option value="sales">تقرير المبيعات</option>
                            <option value="products">تقرير المنتجات</option>
                            <option value="schools">تقرير المدارس</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date-from">من تاريخ:</label>
                        <input type="date" id="date-from">
                    </div>
                    <div class="filter-group">
                        <label for="date-to">إلى تاريخ:</label>
                        <input type="date" id="date-to">
                    </div>
                    <button id="apply-filter-btn" class="filter-btn apply-btn"><i class="fas fa-filter"></i> تطبيق</button>
                </div>

                <div class="report-tabs">
                    <div class="report-tab active" data-tab="summary">ملخص</div>
                    <div class="report-tab" data-tab="chart">رسم بياني</div>
                    <div class="report-tab" data-tab="details">تفاصيل</div>
                </div>

                <div id="summary-tab" class="report-content active">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h3 id="total-sales">0</h3>
                            <p>إجمالي المبيعات (ريال)</p>
                        </div>
                        <div class="summary-card">
                            <h3 id="total-orders">0</h3>
                            <p>عدد الطلبات</p>
                        </div>
                        <div class="summary-card">
                            <h3 id="avg-order">0</h3>
                            <p>متوسط قيمة الطلب (ريال)</p>
                        </div>
                        <div class="summary-card">
                            <h3 id="top-product">-</h3>
                            <p>المنتج الأكثر مبيعًا</p>
                        </div>
                    </div>
                </div>

                <div id="chart-tab" class="report-content">
                    <div class="chart-container">
                        <canvas id="report-chart"></canvas>
                    </div>
                </div>

                <div id="details-tab" class="report-content">
                    <div class="table-responsive">
                        <table id="details-table">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>التاريخ</th>
                                    <th>المدرسة</th>
                                    <th>الطالب</th>
                                    <th>المنتجات</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="report-details">
                                <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة للتحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم من sessionStorage
                let currentUser = null;
                try {
                    const userDataString = sessionStorage.getItem('currentUser');
                    if (userDataString) {
                        currentUser = JSON.parse(userDataString);
                    }
                } catch (sessionError) {
                    console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
                }

                // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
                if (!currentUser || !currentUser.role) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'admin' && userId) {
                        // إنشاء كائن مستخدم بسيط
                        currentUser = {
                            id: parseInt(userId),
                            role: userRole,
                            name: 'مدير النظام'
                        };

                        // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                        try {
                            const db = getDatabase();
                            const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                            if (fullUserData) {
                                currentUser = fullUserData;
                                // تخزين البيانات الكاملة في sessionStorage
                                sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                            }
                        } catch (dbError) {
                            console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                        }
                    }
                }

                // التحقق من صلاحيات المستخدم
                if (!currentUser || currentUser.role !== 'admin') {
                    alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                    window.location.href = '../auth/login.html';
                    return null;
                }

                return currentUser;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
                window.location.href = '../auth/login.html';
                return null;
            }
        }

        // وظيفة لتحويل حالة الطلب إلى نص
        function getOrderStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التجهيز';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }

        // وظيفة لتنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // وظيفة لتهيئة التقرير
        function initReport() {
            // تعيين التاريخ الافتراضي (الشهر الحالي)
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            document.getElementById('date-from').valueAsDate = firstDay;
            document.getElementById('date-to').valueAsDate = lastDay;

            // تحديث التقرير
            updateReport();
        }

        // وظيفة لتحديث التقرير
        function updateReport() {
            const reportType = document.getElementById('report-type').value;
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;

            // الحصول على البيانات المصفاة
            const filteredData = getFilteredData(reportType, dateFrom, dateTo);

            // تحديث ملخص التقرير
            updateSummary(filteredData);

            // تحديث الرسم البياني
            updateChart(filteredData, reportType);

            // تحديث تفاصيل التقرير
            updateDetails(filteredData);
        }

        // وظيفة للحصول على البيانات المصفاة
        function getFilteredData(reportType, dateFrom, dateFrom_obj, dateTo, dateTo_obj) {
            const db = getDatabase();

            // تحويل التواريخ إلى كائنات Date
            dateFrom_obj = dateFrom_obj || new Date(dateFrom);
            dateTo_obj = dateTo_obj || new Date(dateTo);

            // ضبط dateTo ليشمل اليوم بأكمله
            dateTo_obj.setHours(23, 59, 59, 999);

            // تصفية الطلبات حسب التاريخ
            const filteredOrders = db.orders.filter(order => {
                const orderDate = new Date(order.date);
                return orderDate >= dateFrom_obj && orderDate <= dateTo_obj;
            });

            return {
                orders: filteredOrders,
                products: db.products,
                schools: db.schools,
                users: db.users,
                reportType: reportType,
                dateFrom: dateFrom_obj,
                dateTo: dateTo_obj
            };
        }

        // وظيفة لتحديث ملخص التقرير
        function updateSummary(data) {
            // حساب إجمالي المبيعات
            const totalSales = data.orders.reduce((sum, order) => sum + order.totalPrice, 0);

            // عدد الطلبات
            const totalOrders = data.orders.length;

            // متوسط قيمة الطلب
            const avgOrder = totalOrders > 0 ? (totalSales / totalOrders).toFixed(2) : 0;

            // المنتج الأكثر مبيعًا
            const productCounts = {};
            data.orders.forEach(order => {
                order.products.forEach(item => {
                    productCounts[item.productId] = (productCounts[item.productId] || 0) + item.quantity;
                });
            });

            let topProductId = null;
            let topProductCount = 0;

            for (const [productId, count] of Object.entries(productCounts)) {
                if (count > topProductCount) {
                    topProductCount = count;
                    topProductId = parseInt(productId);
                }
            }

            const topProduct = topProductId ? data.products.find(p => p.id === topProductId) : null;

            // تحديث العناصر في الصفحة
            document.getElementById('total-sales').textContent = totalSales.toFixed(2);
            document.getElementById('total-orders').textContent = totalOrders;
            document.getElementById('avg-order').textContent = avgOrder;
            document.getElementById('top-product').textContent = topProduct ? topProduct.name : '-';
        }

        // وظيفة لتحديث الرسم البياني
        function updateChart(data, reportType) {
            // الحصول على عنصر الرسم البياني
            const ctx = document.getElementById('report-chart').getContext('2d');

            // تدمير الرسم البياني السابق إذا وجد
            if (window.reportChart) {
                window.reportChart.destroy();
            }

            // إعداد البيانات حسب نوع التقرير
            let chartData = {};
            let chartTitle = '';

            if (reportType === 'sales') {
                // تقرير المبيعات حسب اليوم
                chartData = prepareSalesChartData(data);
                chartTitle = 'المبيعات اليومية';
            } else if (reportType === 'products') {
                // تقرير المنتجات الأكثر مبيعًا
                chartData = prepareProductsChartData(data);
                chartTitle = 'المنتجات الأكثر مبيعًا';
            } else if (reportType === 'schools') {
                // تقرير المبيعات حسب المدرسة
                chartData = prepareSchoolsChartData(data);
                chartTitle = 'المبيعات حسب المدرسة';
            }

            // إنشاء الرسم البياني
            window.reportChart = new Chart(ctx, {
                type: reportType === 'sales' ? 'line' : 'bar',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: chartTitle,
                        data: chartData.values,
                        backgroundColor: 'rgba(46, 125, 50, 0.2)',
                        borderColor: 'rgba(46, 125, 50, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // وظيفة لإعداد بيانات الرسم البياني للمبيعات
        function prepareSalesChartData(data) {
            // إنشاء قاموس للمبيعات اليومية
            const salesByDay = {};

            // تحديد نطاق التاريخ
            const dateFrom = data.dateFrom;
            const dateTo = data.dateTo;

            // إنشاء مصفوفة بجميع الأيام في النطاق
            const allDates = [];
            const currentDate = new Date(dateFrom);

            while (currentDate <= dateTo) {
                const dateString = currentDate.toISOString().split('T')[0];
                allDates.push(dateString);
                salesByDay[dateString] = 0;

                // الانتقال إلى اليوم التالي
                currentDate.setDate(currentDate.getDate() + 1);
            }

            // حساب المبيعات لكل يوم
            data.orders.forEach(order => {
                const orderDate = new Date(order.date).toISOString().split('T')[0];
                salesByDay[orderDate] = (salesByDay[orderDate] || 0) + order.totalPrice;
            });

            // تحويل القاموس إلى مصفوفتين للتسميات والقيم
            const labels = allDates.map(date => formatDate(date));
            const values = allDates.map(date => salesByDay[date]);

            return { labels, values };
        }

        // وظيفة لإعداد بيانات الرسم البياني للمنتجات
        function prepareProductsChartData(data) {
            // حساب مبيعات كل منتج
            const productSales = {};

            data.orders.forEach(order => {
                order.products.forEach(item => {
                    const product = data.products.find(p => p.id === item.productId);
                    if (product) {
                        const productName = product.name;
                        productSales[productName] = (productSales[productName] || 0) + item.quantity;
                    }
                });
            });

            // ترتيب المنتجات حسب المبيعات (تنازليًا) واختيار أعلى 10
            const sortedProducts = Object.entries(productSales)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);

            const labels = sortedProducts.map(item => item[0]);
            const values = sortedProducts.map(item => item[1]);

            return { labels, values };
        }

        // وظيفة لإعداد بيانات الرسم البياني للمدارس
        function prepareSchoolsChartData(data) {
            // حساب مبيعات كل مدرسة
            const schoolSales = {};

            data.orders.forEach(order => {
                const school = data.schools.find(s => s.id === order.schoolId);
                if (school) {
                    const schoolName = school.name;
                    schoolSales[schoolName] = (schoolSales[schoolName] || 0) + order.totalPrice;
                }
            });

            // ترتيب المدارس حسب المبيعات (تنازليًا)
            const sortedSchools = Object.entries(schoolSales)
                .sort((a, b) => b[1] - a[1]);

            const labels = sortedSchools.map(item => item[0]);
            const values = sortedSchools.map(item => item[1]);

            return { labels, values };
        }

        // وظيفة لتحديث تفاصيل التقرير
        function updateDetails(data) {
            const detailsTable = document.getElementById('report-details');
            detailsTable.innerHTML = '';

            if (data.orders.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="7" style="text-align: center;">لا توجد بيانات متاحة</td>';
                detailsTable.appendChild(row);
                return;
            }

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            const sortedOrders = [...data.orders].sort((a, b) => new Date(b.date) - new Date(a.date));

            sortedOrders.forEach(order => {
                const school = data.schools.find(s => s.id === order.schoolId);
                const user = data.users.find(u => u.id === order.userId);

                // إعداد قائمة المنتجات
                const productsList = order.products.map(item => {
                    const product = data.products.find(p => p.id === item.productId);
                    return product ? `${product.name} (${item.quantity})` : 'منتج غير معروف';
                }).join('<br>');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.id}</td>
                    <td>${formatDate(order.date)}</td>
                    <td>${school ? school.name : 'غير معروف'}</td>
                    <td>${user ? user.name : 'غير معروف'}</td>
                    <td>${productsList}</td>
                    <td>${order.totalPrice} ريال</td>
                    <td>${getOrderStatusText(order.status)}</td>
                `;
                detailsTable.appendChild(row);
            });
        }

        // وظيفة لتصدير التقرير
        function exportReport() {
            const reportType = document.getElementById('report-type').value;
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;

            // إنشاء نص CSV
            let csv = 'الرقم,التاريخ,المدرسة,الطالب,المنتجات,المبلغ,الحالة\n';

            // الحصول على البيانات المصفاة
            const data = getFilteredData(reportType, dateFrom, null, dateTo, null);

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            const sortedOrders = [...data.orders].sort((a, b) => new Date(b.date) - new Date(a.date));

            sortedOrders.forEach(order => {
                const school = data.schools.find(s => s.id === order.schoolId);
                const user = data.users.find(u => u.id === order.userId);

                // إعداد قائمة المنتجات
                const productsList = order.products.map(item => {
                    const product = data.products.find(p => p.id === item.productId);
                    return product ? `${product.name} (${item.quantity})` : 'منتج غير معروف';
                }).join(' - ');

                csv += `${order.id},${formatDate(order.date)},${school ? school.name : 'غير معروف'},${user ? user.name : 'غير معروف'},"${productsList}",${order.totalPrice},${getOrderStatusText(order.status)}\n`;
            });

            // إنشاء رابط للتنزيل
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_${reportType}_${dateFrom}_${dateTo}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // وظيفة لطباعة التقرير
        function printReport() {
            window.print();
        }

        // تهيئة الصفحة عند تحميلها
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صلاحيات المستخدم
            const currentUser = checkUserAuthorization();
            if (!currentUser) {
                return;
            }

            // تعيين اسم المستخدم
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // تهيئة التقرير
            initReport();

            // إضافة مستمعي الأحداث للتبويبات
            document.querySelectorAll('.report-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع التبويبات
                    document.querySelectorAll('.report-tab').forEach(t => t.classList.remove('active'));
                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');

                    // إخفاء جميع محتويات التقارير
                    document.querySelectorAll('.report-content').forEach(content => content.classList.remove('active'));
                    // إظهار محتوى التقرير المحدد
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });

            // إضافة مستمع الحدث لزر تطبيق الفلتر
            document.getElementById('apply-filter-btn').addEventListener('click', updateReport);

            // إضافة مستمع الحدث لزر التصدير
            document.getElementById('export-btn').addEventListener('click', exportReport);

            // إضافة مستمع الحدث لزر الطباعة
            document.getElementById('print-btn').addEventListener('click', printReport);

            // زر تسجيل الخروج
            document.getElementById('logout-btn').addEventListener('click', function() {
                try {
                    // حذف بيانات المستخدم من جميع وسائل التخزين
                    sessionStorage.clear();
                    localStorage.removeItem('currentUserRole');
                    localStorage.removeItem('currentUserId');

                    // حذف الكوكيز المتعلقة بالمستخدم
                    document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                    console.log('تم تسجيل الخروج بنجاح');

                    // التوجيه إلى صفحة تسجيل الدخول
                    window.location.href = '../auth/login.html';
                } catch (error) {
                    console.error('خطأ في تسجيل الخروج:', error);
                }
            });
        });
    </script>
</body>
</html>
