<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="فواصل النجاح">
    <meta name="theme-color" content="#2e7d32">
    <meta name="msapplication-TileColor" content="#2e7d32">
    <meta name="format-detection" content="telephone=yes, email=yes">
    <meta name="HandheldFriendly" content="true">
    <meta name="MobileOptimized" content="width">
    <title>فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/home.css">
    <link rel="stylesheet" href="assets/css/enhanced-home.css">
    <link rel="stylesheet" href="assets/css/modern.css">
    <link rel="stylesheet" href="assets/css/dark-mode.css">
    <link rel="stylesheet" href="assets/css/animations.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;700&family=Almarai:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />

    <!-- Favicon and PWA Icons -->
    <link rel="icon" href="assets/images/logo-enhanced.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="assets/images/logo-enhanced.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/logo-enhanced.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/logo-enhanced.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/logo-enhanced.svg">
    <link rel="manifest" href="manifest.json">
    <meta name="msapplication-config" content="browserconfig.xml">
</head>
<body>
    <!-- Beta Banner -->
    <div class="beta-banner">
        <span>تجريبي</span> هذا النظام في مرحلة تجريبية وقد يخضع للتغييرات والتحسينات المستمرة
    </div>

    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-utensils"></i>
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                </a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html" class="active">الرئيسية</a></li>
                    <li><a href="#features">المميزات</a></li>
                    <li><a href="#meals">الوجبات</a></li>
                    <li><a href="#about">عن المقصف</a></li>
                    <li><a href="#contact">اتصل بنا</a></li>
                    <li><a href="pages/auth/login.html" class="btn btn-primary">تسجيل الدخول</a></li>
                </ul>
            </nav>
            <button class="menu-toggle" aria-label="فتح القائمة">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Hero Section المحسن -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="hero-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>

        <div class="container">
            <div class="hero-content" data-aos="fade-up">
                <div class="hero-badge">
                    <i class="fas fa-utensils"></i>
                    <span>Smart Canteen</span>
                </div>

                <h1 class="hero-title">
                    نظام إدارة المقصف
                    <span class="highlight">الذكي</span>
                </h1>

                <p class="hero-subtitle">
                    حلول متكاملة وذكية لإدارة المقاصف المدرسية بكفاءة عالية
                </p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>آمن ومحمي</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-mobile-alt"></i>
                        <span>سهل الاستخدام</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-chart-line"></i>
                        <span>تقارير ذكية</span>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="pages/auth/login.html" class="hero-btn hero-btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>ابدأ الآن</span>
                    </a>
                    <a href="#features" class="hero-btn hero-btn-secondary">
                        <i class="fas fa-play"></i>
                        <span>شاهد العرض</span>
                    </a>
                </div>

                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">مدرسة</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">20K+</div>
                        <div class="stat-label">طالب</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">100+</div>
                        <div class="stat-label">وجبة</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section المحسن -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <div class="section-badge">
                    <i class="fas fa-star"></i>
                    <span>المميزات</span>
                </div>
                <h2 class="section-title">لماذا نحن الأفضل؟</h2>
                <p class="section-subtitle">نظام متكامل وذكي يوفر حلولاً شاملة لإدارة المقاصف المدرسية</p>
            </div>

            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <h3 class="feature-title">وجبات صحية</h3>
                    <p class="feature-description">تشكيلة متنوعة من الوجبات الصحية المتوازنة والمغذية</p>
                    <div class="feature-link">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3 class="feature-title">دفع إلكتروني</h3>
                    <p class="feature-description">نظام دفع متطور وآمن لشحن الرصيد ومتابعة المصروفات</p>
                    <div class="feature-link">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-cash-register"></i>
                    </div>
                    <h3 class="feature-title">نقطة بيع متقدمة</h3>
                    <p class="feature-description">نظام نقطة بيع ذكي وسريع مع واجهة سهلة للعاملين</p>
                    <div class="feature-link">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="feature-title">تنبيهات الحساسية</h3>
                    <p class="feature-description">نظام ذكي لتتبع حساسية الطعام وحماية الطلاب</p>
                    <div class="feature-link">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="feature-title">لوحة تحكم ذكية</h3>
                    <p class="feature-description">إدارة شاملة للمبيعات والمخزون مع تقارير تفصيلية</p>
                    <div class="feature-link">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Meals Section المحسن -->
    <section id="meals" class="meals-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <div class="section-badge">
                    <i class="fas fa-utensils"></i>
                    <span>الوجبات</span>
                </div>
                <h2 class="section-title">وجبات صحية ولذيذة</h2>
                <p class="section-subtitle">مجموعة متنوعة من الوجبات الصحية والشهية المناسبة لجميع الأذواق</p>
            </div>

            <div class="meals-showcase">
                <div class="meal-card featured" data-aos="fade-up" data-aos-delay="100">
                    <div class="meal-badge">الأكثر طلباً</div>
                    <div class="meal-icon">🍿</div>
                    <h3 class="meal-title">فشار صحي</h3>
                    <p class="meal-description">فشار طازج محضر بطريقة صحية بدون زيوت مضافة</p>
                    <div class="meal-price">5 <span>ريال</span></div>
                    <div class="meal-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="meal-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="meal-icon">🧁</div>
                    <h3 class="meal-title">كيك صحي</h3>
                    <p class="meal-description">كيك محضر من دقيق القمح الكامل والفواكه الطازجة</p>
                    <div class="meal-price">7 <span>ريال</span></div>
                    <div class="meal-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    </div>
                </div>

                <div class="meal-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="meal-icon">🥤</div>
                    <h3 class="meal-title">عصير طبيعي</h3>
                    <p class="meal-description">عصير طازج محضر من الفواكه الموسمية الطبيعية</p>
                    <div class="meal-price">6 <span>ريال</span></div>
                    <div class="meal-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                </div>
            </div>

            <div class="meals-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="pages/auth/login.html" class="cta-btn">
                    <i class="fas fa-shopping-cart"></i>
                    <span>اطلب الآن</span>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section المحسن -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="about-content">
                <div class="about-info" data-aos="fade-up">
                    <div class="section-badge">
                        <i class="fas fa-info-circle"></i>
                        <span>من نحن</span>
                    </div>
                    <h2 class="section-title">فواصل النجاح للخدمات الإعاشة</h2>
                    <p class="about-description">
                        نسعى لتقديم أفضل الخدمات الغذائية للمدارس مع التركيز على جودة الطعام وتنوعه وقيمته الغذائية.
                        نؤمن بأن التغذية السليمة هي أساس النجاح الأكاديمي.
                    </p>

                    <div class="about-features">
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>جودة عالية مضمونة</span>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>تنوع في الوجبات</span>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>قيمة غذائية متوازنة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section المحسن -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <div class="section-badge">
                    <i class="fas fa-phone"></i>
                    <span>تواصل معنا</span>
                </div>
                <h2 class="section-title">نحن هنا لمساعدتك</h2>
                <p class="section-subtitle">تواصل معنا للإجابة على استفساراتك أو لمعرفة المزيد عن خدماتنا</p>
            </div>

            <div class="contact-grid">
                <div class="contact-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>الموقع</h3>
                    <p>الرياض، المملكة العربية السعودية</p>
                </div>

                <div class="contact-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h3>الهاتف</h3>
                    <p>+966 12 345 6789</p>
                </div>

                <div class="contact-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>البريد الإلكتروني</h3>
                    <p><EMAIL></p>
                </div>
            </div>

            <div class="contact-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="pages/auth/login.html" class="cta-btn">
                    <i class="fas fa-comments"></i>
                    <span>ابدأ المحادثة</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>فواصل النجاح للخدمات الإعاشة</h2>
                    <p>نظام متكامل لإدارة المقاصف المدرسية بكفاءة عالية وتقنيات حديثة.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="#features">المميزات</a></li>
                        <li><a href="#meals">الوجبات</a></li>
                        <li><a href="#about">عن المقصف</a></li>
                        <li><a href="#contact">اتصل بنا</a></li>
                    </ul>
                </div>

                <div class="footer-links">
                    <h3>صفحات النظام</h3>
                    <ul>
                        <li><a href="pages/auth/login.html">تسجيل الدخول</a></li>
                        <li><a href="pages/admin/dashboard.html">لوحة تحكم المدير</a></li>
                        <li><a href="pages/staff/dashboard.html">لوحة تحكم العاملين</a></li>
                        <li><a href="pages/student/dashboard.html">صفحة الطالب</a></li>
                        <li><a href="pages/parent/dashboard.html">صفحة ولي الأمر</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 فواصل النجاح للخدمات الإعاشة. جميع الحقوق محفوظة.</p>

                <div class="developer-info">
                    <div class="developer-info-text">
                        <i class="fas fa-code"></i>
                        <span>تطوير:</span>
                        <strong>محمد الأشرافي جابر</strong>
                    </div>
                    <div class="developer-info-text">
                        <i class="fas fa-phone"></i>
                        <a href="tel:0532969067" dir="ltr">0532969067</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-manager.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        // تهيئة مكتبة AOS للتحريكات
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // تحريك الأرقام في قسم الإحصائيات
        function animateStats() {
            const stats = document.querySelectorAll('.stat-value');

            stats.forEach(stat => {
                const target = parseInt(stat.textContent);
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    stat.textContent = Math.floor(current) + (stat.id === 'schools-count' || stat.id === 'meals-count' ? '+' : '+');
                    if (current >= target) {
                        clearInterval(timer);
                        stat.textContent = target + '+';
                    }
                }, 10);
            });
        }

        // تشغيل التحريك عند الوصول إلى قسم الإحصائيات
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        });

        const aboutSection = document.querySelector('.about-stats');
        if (aboutSection) {
            observer.observe(aboutSection);
        }

        // ===== تحسينات متقدمة للهواتف المحمولة =====

        // التبديل بين القائمة المفتوحة والمغلقة في الشاشات الصغيرة
        const menuToggle = document.querySelector('.menu-toggle');
        const mainNav = document.querySelector('.main-nav');

        if (menuToggle && mainNav) {
            // إضافة تأثيرات متقدمة للقائمة
            menuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const isActive = mainNav.classList.contains('active');

                if (isActive) {
                    closeMenu();
                } else {
                    openMenu();
                }
            });

            // فتح القائمة مع تأثيرات
            function openMenu() {
                mainNav.classList.add('active');
                menuToggle.querySelector('i').classList.remove('fa-bars');
                menuToggle.querySelector('i').classList.add('fa-times');
                document.body.style.overflow = 'hidden'; // منع التمرير

                // إضافة تأثير للعناصر
                const menuItems = mainNav.querySelectorAll('li');
                menuItems.forEach((item, index) => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(50px)';
                    setTimeout(() => {
                        item.style.transition = 'all 0.3s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, index * 100);
                });

                // إضافة مستمع للنقر خارج القائمة
                setTimeout(() => {
                    document.addEventListener('click', closeMenuOnOutsideClick);
                }, 100);
            }

            // إغلاق القائمة مع تأثيرات
            function closeMenu() {
                mainNav.classList.remove('active');
                menuToggle.querySelector('i').classList.remove('fa-times');
                menuToggle.querySelector('i').classList.add('fa-bars');
                document.body.style.overflow = ''; // إعادة التمرير

                // إزالة مستمع النقر خارج القائمة
                document.removeEventListener('click', closeMenuOnOutsideClick);
            }

            // إغلاق القائمة عند النقر خارجها
            function closeMenuOnOutsideClick(e) {
                if (!mainNav.contains(e.target) && !menuToggle.contains(e.target)) {
                    closeMenu();
                }
            }

            // إغلاق القائمة عند النقر على رابط
            const menuLinks = mainNav.querySelectorAll('a');
            menuLinks.forEach(link => {
                link.addEventListener('click', () => {
                    closeMenu();
                });
            });

            // إغلاق القائمة بمفتاح Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && mainNav.classList.contains('active')) {
                    closeMenu();
                }
            });
        }

        // تحسين التفاعل باللمس للبطاقات
        function initTouchInteractions() {
            const cards = document.querySelectorAll('.feature-card, .meal-card, .contact-card');

            cards.forEach(card => {
                let touchStartY = 0;
                let touchStartX = 0;

                card.addEventListener('touchstart', (e) => {
                    touchStartY = e.touches[0].clientY;
                    touchStartX = e.touches[0].clientX;
                    card.style.transform = 'scale(0.98)';
                }, { passive: true });

                card.addEventListener('touchend', (e) => {
                    card.style.transform = '';

                    const touchEndY = e.changedTouches[0].clientY;
                    const touchEndX = e.changedTouches[0].clientX;
                    const deltaY = Math.abs(touchEndY - touchStartY);
                    const deltaX = Math.abs(touchEndX - touchStartX);

                    // إذا كان اللمس قصير (نقرة وليس تمرير)
                    if (deltaY < 10 && deltaX < 10) {
                        // إضافة تأثير النقر
                        card.style.animation = 'vibrate 0.2s ease-in-out';
                        setTimeout(() => {
                            card.style.animation = '';
                        }, 200);
                    }
                }, { passive: true });

                card.addEventListener('touchcancel', () => {
                    card.style.transform = '';
                }, { passive: true });
            });
        }

        // تحسين التمرير السلس للروابط الداخلية
        function initSmoothScrolling() {
            const links = document.querySelectorAll('a[href^="#"]');

            links.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    const targetId = link.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        const headerHeight = document.querySelector('.header').offsetHeight;
                        const targetPosition = targetElement.offsetTop - headerHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // تحسين الأداء للهواتف
        function initPerformanceOptimizations() {
            // تأخير تحميل الصور
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));

            // تحسين الحركات للهواتف الضعيفة
            if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
                document.body.classList.add('low-performance');
            }
        }

        // اكتشاف نوع الجهاز
        function detectDeviceType() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isTablet = /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);

            if (isMobile) {
                document.body.classList.add('mobile-device');
            }
            if (isTablet) {
                document.body.classList.add('tablet-device');
            }

            // تحسين الخطوط للأجهزة المحمولة
            if (isMobile || isTablet) {
                document.body.style.webkitTextSizeAdjust = '100%';
                document.body.style.textSizeAdjust = '100%';
            }
        }

        // تحسين التوجه (Portrait/Landscape)
        function handleOrientationChange() {
            const handleResize = () => {
                // إعادة حساب الارتفاعات
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);

                // إغلاق القائمة عند تغيير التوجه
                if (mainNav && mainNav.classList.contains('active')) {
                    mainNav.classList.remove('active');
                    if (menuToggle) {
                        menuToggle.querySelector('i').classList.remove('fa-times');
                        menuToggle.querySelector('i').classList.add('fa-bars');
                    }
                    document.body.style.overflow = '';
                }
            };

            window.addEventListener('resize', handleResize);
            window.addEventListener('orientationchange', () => {
                setTimeout(handleResize, 100);
            });

            // تشغيل مرة واحدة في البداية
            handleResize();
        }

        // تهيئة جميع التحسينات
        function initMobileEnhancements() {
            detectDeviceType();
            initTouchInteractions();
            initSmoothScrolling();
            initPerformanceOptimizations();
            handleOrientationChange();

            // إضافة مستمع للتحميل الكامل
            window.addEventListener('load', () => {
                document.body.classList.add('loaded');
            });
        }

        // تشغيل التحسينات عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initMobileEnhancements);
        } else {
            initMobileEnhancements();
        }

        // تسجيل Service Worker للـ PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('Service Worker registered successfully:', registration.scope);

                        // التحقق من وجود تحديثات
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // إظهار رسالة التحديث للمستخدم
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch((error) => {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }

        // إظهار إشعار التحديث
        function showUpdateNotification() {
            const updateBanner = document.createElement('div');
            updateBanner.className = 'update-banner';
            updateBanner.innerHTML = `
                <div class="update-content">
                    <i class="fas fa-download"></i>
                    <span>تحديث جديد متاح!</span>
                    <button onclick="updateApp()" class="update-btn">تحديث</button>
                    <button onclick="dismissUpdate()" class="dismiss-btn">لاحقاً</button>
                </div>
            `;
            document.body.appendChild(updateBanner);

            setTimeout(() => {
                updateBanner.classList.add('show');
            }, 100);
        }

        // تحديث التطبيق
        function updateApp() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then((registration) => {
                    if (registration && registration.waiting) {
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                    }
                });
            }
        }

        // إخفاء إشعار التحديث
        function dismissUpdate() {
            const updateBanner = document.querySelector('.update-banner');
            if (updateBanner) {
                updateBanner.classList.remove('show');
                setTimeout(() => {
                    updateBanner.remove();
                }, 300);
            }
        }

        // إضافة إشعار التثبيت للـ PWA
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // إظهار زر التثبيت
            showInstallButton();
        });

        // إظهار زر التثبيت
        function showInstallButton() {
            const installBanner = document.createElement('div');
            installBanner.className = 'install-banner';
            installBanner.innerHTML = `
                <div class="install-content">
                    <i class="fas fa-mobile-alt"></i>
                    <span>ثبت التطبيق على جهازك</span>
                    <button onclick="installApp()" class="install-btn">تثبيت</button>
                    <button onclick="dismissInstall()" class="dismiss-btn">لا شكراً</button>
                </div>
            `;
            document.body.appendChild(installBanner);

            setTimeout(() => {
                installBanner.classList.add('show');
            }, 2000);
        }

        // تثبيت التطبيق
        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    deferredPrompt = null;
                    dismissInstall();
                });
            }
        }

        // إخفاء إشعار التثبيت
        function dismissInstall() {
            const installBanner = document.querySelector('.install-banner');
            if (installBanner) {
                installBanner.classList.remove('show');
                setTimeout(() => {
                    installBanner.remove();
                }, 300);
            }
        }

        // معالجة نموذج الاتصال
        const contactForm = document.getElementById('contact-form');

        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('تم إرسال رسالتك بنجاح! سنقوم بالرد عليك في أقرب وقت ممكن.');
                contactForm.reset();
            });
        }

        // تأثيرات الشريط العلوي المتقدمة
        const header = document.querySelector('.header');
        const loginBtn = document.querySelector('.header .btn-primary');

        // تأثير التمرير للشريط العلوي
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // تأثير النقر على زر تسجيل الدخول
        if (loginBtn) {
            loginBtn.addEventListener('click', function(e) {
                // إنشاء تأثير الموجة
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        }

        // تأثير تحريك العنوان مع الماوس
        const heroTitle = document.querySelector('.hero-title');
        if (heroTitle) {
            let mouseX = 0;
            let mouseY = 0;

            document.addEventListener('mousemove', function(e) {
                mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
                mouseY = (e.clientY / window.innerHeight - 0.5) * 2;

                heroTitle.style.transform = `translate(${mouseX * 3}px, ${mouseY * 3}px)`;
            });
        }

        // تأثيرات اللوجو في الشريط العلوي
        const headerLogo = document.querySelector('.header .logo');
        if (headerLogo) {
            headerLogo.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon) {
                    icon.style.animation = 'headerLogoAnimation 1s ease-in-out';
                    setTimeout(() => {
                        icon.style.animation = 'headerLogoAnimation 3s ease-in-out infinite';
                    }, 1000);
                }
            });
        }


    </script>

    <style>
        /* تأثير الموجة للزر */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* أنماط اللوجو في الشريط العلوي */
        .header .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .header .logo a {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: inherit;
        }

        .header .logo h1 {
            font-size: 1rem;
            margin: 0;
            line-height: 1.2;
            color: var(--text-color);
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 280px;
        }

        .header .logo i {
            font-size: 2rem;
            color: #4caf50;
            flex-shrink: 0;
            filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            animation: headerLogoAnimation 3s ease-in-out infinite;
            transition: all 0.3s ease;
            transform: rotate(-10deg);
        }

        .header .logo i:hover {
            transform: rotate(-5deg) scale(1.1);
            filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
        }

        /* تأثير متقدم للشعار الرئيسي */
        @keyframes headerLogoAnimation {
            0%, 100% {
                transform: rotate(-10deg) scale(1);
                filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            }
            25% {
                transform: rotate(-12deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
            50% {
                transform: rotate(-15deg) scale(1.05);
                filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
            }
            75% {
                transform: rotate(-18deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
        }

        /* تجاوب للشاشات المختلفة */
        @media (max-width: 768px) {
            .header .logo h1 {
                font-size: 0.8rem;
                max-width: 200px;
            }

            .header .logo i {
                font-size: 1.6rem;
                transform: rotate(-8deg);
            }
        }

        @media (max-width: 480px) {
            .header .logo {
                height: 45px;
                padding: 5px 8px;
                margin-left: 10px;
            }

            .header .logo h1 {
                font-size: 0.9rem;
                max-width: 160px;
            }

            .header .logo i {
                font-size: 1.8rem;
                transform: rotate(-6deg);
            }
        }

        @media (max-width: 320px) {
            .header .logo {
                height: 40px;
                padding: 4px 6px;
                margin-left: 8px;
            }

            .header .logo h1 {
                font-size: 0.8rem;
                max-width: 120px;
            }

            .header .logo i {
                font-size: 1.5rem;
                transform: rotate(-5deg);
            }
        }

        /* ===== تحسينات متقدمة للهواتف المحمولة ===== */

        /* تحسين القائمة المحمولة */
        .menu-toggle {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1001;
        }

        .menu-toggle:hover {
            background: rgba(46, 125, 50, 0.1);
            transform: scale(1.1);
        }

        .menu-toggle:active {
            transform: scale(0.95);
        }

        .menu-toggle i {
            font-size: 1.5rem;
            color: var(--primary-color, #2e7d32);
            transition: all 0.3s ease;
        }

        /* تحسين القائمة للهواتف */
        @media (max-width: 768px) {
            .menu-toggle {
                display: block;
            }

            .main-nav {
                position: fixed;
                top: 0;
                right: -100%;
                width: 280px;
                height: 100vh;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                z-index: 1000;
                padding: 80px 0 20px 0;
                overflow-y: auto;
            }

            .main-nav.active {
                right: 0;
            }

            .main-nav ul {
                flex-direction: column;
                gap: 0;
                padding: 0 20px;
            }

            .main-nav li {
                width: 100%;
                margin-bottom: 5px;
            }

            .main-nav li a {
                display: block;
                padding: 15px 20px;
                border-radius: 12px;
                font-size: 1.1rem;
                font-weight: 500;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .main-nav li a::before {
                content: '';
                position: absolute;
                top: 0;
                right: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(46, 125, 50, 0.1), transparent);
                transition: right 0.5s ease;
            }

            .main-nav li a:hover::before {
                right: 100%;
            }

            .main-nav li a:hover,
            .main-nav li a.active {
                background: var(--primary-color);
                color: white;
                transform: translateX(-5px);
                box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
            }

            .main-nav li a.btn {
                background: linear-gradient(135deg, var(--primary-color), var(--dark-color, #1b5e20));
                color: white;
                margin-top: 10px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(46, 125, 50, 0.4);
            }

            .main-nav li a.btn:hover {
                transform: translateX(-5px) translateY(-2px);
                box-shadow: 0 8px 25px rgba(46, 125, 50, 0.5);
            }

            /* خلفية شفافة عند فتح القائمة */
            .main-nav.active::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                z-index: -1;
                animation: fadeIn 0.3s ease;
            }
        }

        /* تحسينات اللمس والتفاعل */
        .hero-btn,
        .cta-btn,
        .feature-card,
        .meal-card,
        .contact-card {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        /* تحسين الأزرار للمس */
        .hero-btn,
        .cta-btn {
            min-height: 48px;
            min-width: 48px;
            position: relative;
            overflow: hidden;
        }

        .hero-btn:active,
        .cta-btn:active {
            transform: scale(0.98);
        }

        /* تحسين البطاقات للمس */
        .feature-card,
        .meal-card,
        .contact-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .feature-card:active,
        .meal-card:active,
        .contact-card:active {
            transform: scale(0.98);
        }

        /* تحسين التمرير السلس */
        html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        /* تحسين الخطوط للهواتف */
        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
                line-height: 1.2;
            }

            .hero-subtitle {
                font-size: 1rem;
                line-height: 1.4;
            }

            .section-title {
                font-size: 1.8rem;
                line-height: 1.3;
            }

            .section-subtitle {
                font-size: 0.95rem;
                line-height: 1.4;
            }

            .feature-title,
            .meal-title {
                font-size: 1.1rem;
            }

            .feature-description,
            .meal-description {
                font-size: 0.9rem;
                line-height: 1.4;
            }
        }

        /* تحسين المسافات للهواتف الصغيرة */
        @media (max-width: 320px) {
            .container {
                padding: 0 10px;
            }

            .hero-section {
                padding: 60px 0 40px;
            }

            .features-section,
            .meals-section,
            .about-section,
            .contact-section {
                padding: 40px 0;
            }

            .hero-features {
                gap: 15px;
            }

            .hero-feature {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            .hero-buttons {
                gap: 10px;
                flex-direction: column;
            }

            .hero-btn {
                width: 100%;
                justify-content: center;
            }

            .hero-stats {
                gap: 15px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }
        }

        /* تحسين الشبكات للهواتف */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .meals-showcase {
                flex-direction: column;
                gap: 20px;
            }

            .contact-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .footer-content {
                flex-direction: column;
                gap: 30px;
                text-align: center;
            }
        }

        /* تحسين الحركات للهواتف */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* تحسين الأداء للهواتف */
        .hero-background,
        .hero-shapes {
            will-change: transform;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        /* تحسين التباين للهواتف */
        @media (prefers-contrast: high) {
            .hero-btn,
            .cta-btn {
                border: 2px solid currentColor;
            }

            .feature-card,
            .meal-card,
            .contact-card {
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
        }

        /* تحسين الوضع المظلم للهواتف */
        @media (prefers-color-scheme: dark) {
            .main-nav {
                background: rgba(30, 30, 30, 0.98);
                color: white;
            }

            .main-nav li a {
                color: white;
            }

            .main-nav li a:hover,
            .main-nav li a.active {
                background: var(--primary-color);
            }
        }

        /* تحسين التوجه الأفقي للهواتف */
        @media (max-width: 768px) and (orientation: landscape) {
            .hero-section {
                padding: 40px 0 30px;
            }

            .hero-content {
                max-width: 600px;
                margin: 0 auto;
            }

            .hero-features {
                justify-content: center;
            }

            .hero-buttons {
                flex-direction: row;
                justify-content: center;
            }
        }

        /* إضافة تأثيرات الاهتزاز للتفاعل */
        @keyframes vibrate {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        .hero-btn:active,
        .cta-btn:active {
            animation: vibrate 0.1s ease-in-out;
        }

        /* تحسين إمكانية الوصول للهواتف */
        @media (max-width: 768px) {
            .header .logo h1 {
                font-size: 0.9rem;
            }

            .header .logo i {
                font-size: 1.8rem;
            }

            /* تحسين حجم النقر */
            .main-nav li a,
            .hero-btn,
            .cta-btn {
                min-height: 44px;
                min-width: 44px;
            }

            /* تحسين التباعد */
            .hero-features {
                gap: 10px;
            }

            .hero-feature {
                padding: 10px 15px;
            }
        }

        /* ===== أنماط إشعارات PWA ===== */

        .update-banner,
        .install-banner {
            position: fixed;
            bottom: -100px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color, #1b5e20));
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 90%;
            width: 400px;
        }

        .update-banner.show,
        .install-banner.show {
            bottom: 20px;
        }

        .update-content,
        .install-content {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .update-content i,
        .install-content i {
            font-size: 1.5rem;
            color: var(--secondary-color, #f9a825);
            animation: pulse 2s infinite;
        }

        .update-content span,
        .install-content span {
            flex: 1;
            font-weight: 500;
            min-width: 150px;
        }

        .update-btn,
        .install-btn,
        .dismiss-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .update-btn:hover,
        .install-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .dismiss-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .update-btn:active,
        .install-btn:active,
        .dismiss-btn:active {
            transform: scale(0.95);
        }

        /* تحسينات للأجهزة الضعيفة */
        .low-performance * {
            animation-duration: 0.1s !important;
            transition-duration: 0.1s !important;
        }

        .low-performance .hero-background,
        .low-performance .hero-shapes {
            display: none;
        }

        /* تحسينات للشاشات الصغيرة جداً */
        @media (max-width: 280px) {
            .update-banner,
            .install-banner {
                width: 95%;
                padding: 12px 15px;
            }

            .update-content,
            .install-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .update-btn,
            .install-btn,
            .dismiss-btn {
                width: 100%;
                margin: 2px 0;
            }
        }

        /* تحسين الأداء للحركات */
        .hero-background,
        .hero-shapes,
        .shape {
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            will-change: transform;
        }

        /* تحسين التمرير للهواتف */
        body.mobile-device {
            -webkit-overflow-scrolling: touch;
            overflow-scrolling: touch;
        }

        /* تحسين الخطوط للأجهزة عالية الدقة */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            body {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }
        }

        /* تحسين الألوان للشاشات OLED */
        @media (color-gamut: p3) {
            :root {
                --primary-color: color(display-p3 0.18 0.49 0.20);
                --secondary-color: color(display-p3 0.98 0.66 0.15);
            }
        }

        /* تحسين الطاقة للأجهزة المحمولة */
        @media (prefers-reduced-motion: reduce) {
            .hero-background,
            .hero-shapes,
            .shape {
                animation: none !important;
            }
        }

        /* تحسين الوضع الليلي */
        @media (prefers-color-scheme: dark) {
            .update-banner,
            .install-banner {
                background: linear-gradient(135deg, #1a4d1f, #0d2818);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        }

        /* تحسين إمكانية الوصول */
        @media (prefers-reduced-transparency: reduce) {
            .main-nav,
            .update-banner,
            .install-banner {
                backdrop-filter: none;
                -webkit-backdrop-filter: none;
                background: rgba(255, 255, 255, 0.95);
            }
        }

        /* تحسين التباين العالي */
        @media (prefers-contrast: high) {
            .update-banner,
            .install-banner {
                border: 2px solid white;
            }

            .update-btn,
            .install-btn,
            .dismiss-btn {
                border: 2px solid white;
                background: transparent;
            }
        }

        /* تحسين الحركة المخفضة */
        @media (prefers-reduced-motion: reduce) {
            .update-banner,
            .install-banner {
                transition: none;
            }

            .update-banner.show,
            .install-banner.show {
                animation: none;
            }
        }
    </style>
</body>
</html>