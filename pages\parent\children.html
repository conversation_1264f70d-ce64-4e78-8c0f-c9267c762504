<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأبناء - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .child-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #eee;
        }

        .child-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .child-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .child-header h3 {
            font-size: 1.2rem;
            margin: 0;
        }

        .child-header .badge {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .child-info {
            padding: 15px;
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .info-item i {
            width: 20px;
            margin-left: 10px;
            color: var(--primary-color);
        }

        .info-item span {
            font-weight: 500;
        }

        .child-balance {
            background-color: #f9f9f9;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #eee;
        }

        .balance-amount {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .child-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: none;
            margin-bottom: 5px;
        }

        .action-btn i {
            margin-left: 5px;
        }

        .add-balance-btn {
            background-color: var(--primary-color);
            color: white;
        }

        .add-balance-btn:hover {
            background-color: var(--dark-color);
        }

        .view-orders-btn {
            background-color: var(--secondary-color);
            color: var(--text-color);
        }

        .view-orders-btn:hover {
            background-color: #f57f17;
        }

        .manage-allergies-btn {
            background-color: #e91e63;
            color: white;
        }

        .manage-allergies-btn:hover {
            background-color: #c2185b;
        }

        /* تنسيقات الحساسيات */
        .allergies-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .allergies-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .allergies-header h3 {
            font-size: 1.1rem;
            color: var(--primary-color);
            margin: 0;
        }

        .allergies-list {
            margin-top: 10px;
        }

        .allergy-item {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            border-right: 3px solid;
        }

        .allergy-item.food {
            border-right-color: #e91e63;
        }

        .allergy-item.medical {
            border-right-color: #2196f3;
        }

        .allergy-item.health {
            border-right-color: #ff9800;
        }

        .allergy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .allergy-name {
            font-weight: 700;
            display: flex;
            align-items: center;
        }

        .allergy-name i {
            margin-left: 5px;
            font-size: 0.9rem;
        }

        .allergy-type {
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 10px;
            color: white;
        }

        .allergy-type.food {
            background-color: #e91e63;
        }

        .allergy-type.medical {
            background-color: #2196f3;
        }

        .allergy-type.health {
            background-color: #ff9800;
        }

        .allergy-severity {
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 5px;
        }

        .allergy-severity.low {
            background-color: #4caf50;
            color: white;
        }

        .allergy-severity.medium {
            background-color: #ff9800;
            color: white;
        }

        .allergy-severity.high {
            background-color: #f44336;
            color: white;
        }

        .allergy-description {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .allergy-instructions {
            margin-top: 5px;
            font-size: 0.9rem;
            padding: 5px;
            background-color: #fff3e0;
            border-radius: 3px;
        }

        .allergy-actions {
            margin-top: 5px;
            display: flex;
            justify-content: flex-end;
            gap: 5px;
        }

        .allergy-action-btn {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            cursor: pointer;
            border: none;
        }

        .edit-allergy-btn {
            background-color: #2196f3;
            color: white;
        }

        .delete-allergy-btn {
            background-color: #f44336;
            color: white;
        }

        .child-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* تنسيقات النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
            border: none;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--primary-color);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 10px;
        }

        .form-actions button {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-family: 'Tajawal', sans-serif;
        }

        .cancel-btn {
            background-color: #f5f5f5;
            color: #333;
        }

        .save-btn {
            background-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .children-grid {
                grid-template-columns: 1fr;
            }

            .child-stats {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>ولي الأمر</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="children.html" class="active"><i class="fas fa-child"></i> الأبناء</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>إدارة الأبناء</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>قائمة الأبناء</h2>
                <div class="children-grid" id="children-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>
        </div>
    </section>

    <!-- نافذة إضافة/تعديل الحساسية -->
    <div id="allergy-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="allergy-modal-title">إضافة حساسية جديدة</h2>
                <button class="close-btn">&times;</button>
            </div>
            <form id="allergy-form">
                <input type="hidden" id="allergy-id">
                <input type="hidden" id="child-id">

                <div class="form-group">
                    <label for="allergy-type">نوع الحساسية</label>
                    <select id="allergy-type" required>
                        <option value="">اختر نوع الحساسية</option>
                        <option value="food">حساسية من الطعام</option>
                        <option value="medical">حساسية من الأدوية</option>
                        <option value="health">حالة صحية</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="allergy-name">اسم المادة المسببة للحساسية</label>
                    <input type="text" id="allergy-name" required placeholder="مثال: الفول السوداني، البيض، الحليب">
                </div>

                <div class="form-group">
                    <label for="allergy-description">وصف الحساسية وأعراضها</label>
                    <textarea id="allergy-description" rows="3" required placeholder="وصف تفصيلي للحساسية والأعراض التي تظهر عند التعرض لها"></textarea>
                </div>

                <div class="form-group">
                    <label for="allergy-severity">درجة خطورة الحساسية</label>
                    <select id="allergy-severity" required>
                        <option value="">اختر درجة الخطورة</option>
                        <option value="low">بسيطة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">شديدة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="allergy-instructions">الإجراءات الواجب اتخاذها في حالة التعرض</label>
                    <textarea id="allergy-instructions" rows="3" required placeholder="الإجراءات التي يجب اتخاذها في حالة التعرض للمادة المسببة للحساسية"></textarea>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn">إلغاء</button>
                    <button type="submit" class="save-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="delete-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <button class="close-btn">&times;</button>
            </div>
            <p>هل أنت متأكد من حذف هذه الحساسية؟</p>
            <div class="form-actions">
                <button type="button" class="cancel-btn">إلغاء</button>
                <button type="button" id="confirm-delete-btn" class="save-btn" style="background-color: #f44336;">حذف</button>
            </div>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة لعرض قائمة الحساسيات
        function renderAllergiesList(child) {
            if (!child.allergies || child.allergies.length === 0) {
                return '<p>لا توجد حساسيات مسجلة.</p>';
            }

            let html = '';
            child.allergies.forEach(allergy => {
                const typeText = getAllergyTypeText(allergy.type);
                const severityText = getAllergySeverityText(allergy.severity);

                html += `
                    <div class="allergy-item ${allergy.type}">
                        <div class="allergy-header">
                            <div class="allergy-name">
                                ${getAllergyTypeIcon(allergy.type)} ${allergy.name}
                            </div>
                            <div>
                                <span class="allergy-type ${allergy.type}">${typeText}</span>
                                <span class="allergy-severity ${allergy.severity}">${severityText}</span>
                            </div>
                        </div>
                        <div class="allergy-description">${allergy.description}</div>
                        <div class="allergy-instructions">
                            <strong>الإجراءات:</strong> ${allergy.instructions}
                        </div>
                        <div class="allergy-actions">
                            <button class="allergy-action-btn edit-allergy-btn" data-child-id="${child.id}" data-allergy-id="${allergy.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="allergy-action-btn delete-allergy-btn" data-child-id="${child.id}" data-allergy-id="${allergy.id}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                `;
            });

            return html;
        }

        // وظيفة للحصول على نص نوع الحساسية
        function getAllergyTypeText(type) {
            switch(type) {
                case 'food':
                    return 'طعام';
                case 'medical':
                    return 'دواء';
                case 'health':
                    return 'حالة صحية';
                default:
                    return 'غير معروف';
            }
        }

        // وظيفة للحصول على نص درجة الخطورة
        function getAllergySeverityText(severity) {
            switch(severity) {
                case 'low':
                    return 'بسيطة';
                case 'medium':
                    return 'متوسطة';
                case 'high':
                    return 'شديدة';
                default:
                    return 'غير معروف';
            }
        }

        // وظيفة للحصول على أيقونة نوع الحساسية
        function getAllergyTypeIcon(type) {
            switch(type) {
                case 'food':
                    return '<i class="fas fa-utensils"></i>';
                case 'medical':
                    return '<i class="fas fa-pills"></i>';
                case 'health':
                    return '<i class="fas fa-heartbeat"></i>';
                default:
                    return '<i class="fas fa-question-circle"></i>';
            }
        }

        // وظيفة لإضافة حساسية جديدة
        function addAllergy(childId) {
            // إعادة تعيين النموذج
            document.getElementById('allergy-form').reset();
            document.getElementById('allergy-id').value = '';
            document.getElementById('child-id').value = childId;
            document.getElementById('allergy-modal-title').textContent = 'إضافة حساسية جديدة';

            // عرض النافذة المنبثقة
            document.getElementById('allergy-modal').style.display = 'flex';
        }

        // وظيفة لتعديل حساسية موجودة
        function editAllergy(childId, allergyId) {
            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child || !child.allergies) {
                alert('لم يتم العثور على الطفل أو الحساسية!');
                return;
            }

            const allergy = child.allergies.find(a => a.id === allergyId);

            if (!allergy) {
                alert('لم يتم العثور على الحساسية!');
                return;
            }

            // تعبئة النموذج بتفاصيل الحساسية
            document.getElementById('allergy-id').value = allergy.id;
            document.getElementById('child-id').value = childId;
            document.getElementById('allergy-type').value = allergy.type;
            document.getElementById('allergy-name').value = allergy.name;
            document.getElementById('allergy-description').value = allergy.description;
            document.getElementById('allergy-severity').value = allergy.severity;
            document.getElementById('allergy-instructions').value = allergy.instructions;

            document.getElementById('allergy-modal-title').textContent = 'تعديل الحساسية';

            // عرض النافذة المنبثقة
            document.getElementById('allergy-modal').style.display = 'flex';
        }

        // وظيفة لحذف حساسية
        function deleteAllergy(childId, allergyId) {
            // تخزين معرف الطفل والحساسية للحذف
            document.getElementById('confirm-delete-btn').setAttribute('data-child-id', childId);
            document.getElementById('confirm-delete-btn').setAttribute('data-allergy-id', allergyId);

            // عرض نافذة التأكيد
            document.getElementById('delete-modal').style.display = 'flex';
        }

        // وظيفة لحفظ الحساسية (إضافة أو تعديل)
        function saveAllergy(event) {
            event.preventDefault();

            const allergyId = document.getElementById('allergy-id').value;
            const childId = parseInt(document.getElementById('child-id').value);
            const type = document.getElementById('allergy-type').value;
            const name = document.getElementById('allergy-name').value;
            const description = document.getElementById('allergy-description').value;
            const severity = document.getElementById('allergy-severity').value;
            const instructions = document.getElementById('allergy-instructions').value;

            // التحقق من صحة البيانات
            if (!type || !name || !description || !severity || !instructions) {
                alert('يرجى ملء جميع الحقول المطلوبة!');
                return;
            }

            const db = getDatabase();
            const childIndex = db.users.findIndex(u => u.id === childId);

            if (childIndex === -1) {
                alert('لم يتم العثور على الطفل!');
                return;
            }

            // التأكد من وجود مصفوفة الحساسيات
            if (!db.users[childIndex].allergies) {
                db.users[childIndex].allergies = [];
            }

            if (allergyId) {
                // تعديل حساسية موجودة
                const allergyIndex = db.users[childIndex].allergies.findIndex(a => a.id === parseInt(allergyId));

                if (allergyIndex === -1) {
                    alert('لم يتم العثور على الحساسية!');
                    return;
                }

                db.users[childIndex].allergies[allergyIndex] = {
                    ...db.users[childIndex].allergies[allergyIndex],
                    type,
                    name,
                    description,
                    severity,
                    instructions
                };

                alert('تم تعديل الحساسية بنجاح!');
            } else {
                // إضافة حساسية جديدة
                const newAllergyId = db.users[childIndex].allergies.length > 0 ?
                    Math.max(...db.users[childIndex].allergies.map(a => a.id)) + 1 : 1;

                db.users[childIndex].allergies.push({
                    id: newAllergyId,
                    type,
                    name,
                    description,
                    severity,
                    instructions
                });

                alert('تم إضافة الحساسية بنجاح!');
            }

            // حفظ التغييرات في قاعدة البيانات
            saveDatabase(db);

            // إغلاق النافذة المنبثقة
            document.getElementById('allergy-modal').style.display = 'none';

            // تحديث عرض الحساسيات
            const allergiesList = document.getElementById(`allergies-list-${childId}`);
            if (allergiesList) {
                allergiesList.innerHTML = renderAllergiesList(db.users[childIndex]);

                // إعادة إضافة مستمعي الأحداث للأزرار
                attachAllergyButtonListeners();
            }
        }

        // وظيفة لإضافة مستمعي الأحداث لأزرار الحساسيات
        function attachAllergyButtonListeners() {
            // أزرار إضافة الحساسية
            document.querySelectorAll('.add-allergy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const childId = parseInt(this.getAttribute('data-id'));
                    addAllergy(childId);
                });
            });

            // أزرار إدارة الحساسيات
            document.querySelectorAll('.manage-allergies-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const childId = parseInt(this.getAttribute('data-id'));
                    const allergiesSection = this.closest('.child-card').querySelector('.allergies-section');

                    if (allergiesSection.style.display === 'block') {
                        allergiesSection.style.display = 'none';
                    } else {
                        allergiesSection.style.display = 'block';
                    }
                });
            });

            // أزرار تعديل الحساسية
            document.querySelectorAll('.edit-allergy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const childId = parseInt(this.getAttribute('data-child-id'));
                    const allergyId = parseInt(this.getAttribute('data-allergy-id'));
                    editAllergy(childId, allergyId);
                });
            });

            // أزرار حذف الحساسية
            document.querySelectorAll('.delete-allergy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const childId = parseInt(this.getAttribute('data-child-id'));
                    const allergyId = parseInt(this.getAttribute('data-allergy-id'));
                    deleteAllergy(childId, allergyId);
                });
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is parent
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Get children
            const children = db.users.filter(user => currentUser.children.includes(user.id));

            // Populate children grid
            const childrenGrid = document.getElementById('children-grid');

            if (children.length === 0) {
                childrenGrid.innerHTML = '<p>لا يوجد أبناء مسجلين.</p>';
            } else {
                children.forEach(child => {
                    const school = db.schools.find(s => s.id === child.schoolId);

                    // Get child orders
                    const orders = db.orders.filter(order => order.userId === child.id);
                    const totalSpent = orders.reduce((sum, order) => sum + order.totalPrice, 0);
                    const completedOrders = orders.filter(order => order.status === 'completed').length;

                    const childCard = document.createElement('div');
                    childCard.className = 'child-card';
                    childCard.innerHTML = `
                        <div class="child-header">
                            <h3>${child.name}</h3>
                            <span class="badge">${getGradeText(child.grade)}</span>
                        </div>
                        <div class="child-info">
                            <div class="info-item">
                                <i class="fas fa-id-card"></i>
                                <span>رقم نور: ${child.noorId || 'غير متوفر'}</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-school"></i>
                                <span>المدرسة: ${school ? school.name : 'غير معروف'}</span>
                            </div>
                            <div class="child-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${child.balance || 0}</div>
                                    <div class="stat-label">الرصيد (ريال)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${totalSpent}</div>
                                    <div class="stat-label">إجمالي المصروفات (ريال)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${completedOrders}</div>
                                    <div class="stat-label">عدد الطلبات المكتملة</div>
                                </div>
                            </div>
                        </div>
                        <div class="child-balance">
                            <div class="child-actions">
                                <button class="action-btn add-balance-btn" data-id="${child.id}"><i class="fas fa-plus"></i> إضافة رصيد</button>
                                <button class="action-btn view-orders-btn" data-id="${child.id}"><i class="fas fa-shopping-cart"></i> عرض الطلبات</button>
                                <button class="action-btn manage-allergies-btn" data-id="${child.id}"><i class="fas fa-allergies"></i> إدارة الحساسيات</button>
                            </div>
                        </div>

                        <!-- قسم الحساسيات -->
                        <div class="allergies-section">
                            <div class="allergies-header">
                                <h3>الحساسيات والحالات الصحية</h3>
                                <button class="action-btn add-allergy-btn" data-id="${child.id}"><i class="fas fa-plus"></i> إضافة</button>
                            </div>
                            <div class="allergies-list" id="allergies-list-${child.id}">
                                ${renderAllergiesList(child)}
                            </div>
                        </div>
                    `;
                    childrenGrid.appendChild(childCard);
                });
            }

            // Add Balance Buttons
            document.querySelectorAll('.add-balance-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const childId = parseInt(this.getAttribute('data-id'));
                    const child = db.users.find(u => u.id === childId);

                    if (child) {
                        const amount = prompt(`أدخل المبلغ الذي تريد إضافته لرصيد ${child.name}:`);

                        if (amount && !isNaN(amount) && amount > 0) {
                            // Update child balance
                            child.balance = (child.balance || 0) + parseInt(amount);

                            // Update in database
                            const childIndex = db.users.findIndex(u => u.id === childId);
                            if (childIndex !== -1) {
                                db.users[childIndex].balance = child.balance;

                                // Add notification
                                const newNotification = {
                                    id: db.notifications.length + 1,
                                    userId: childId,
                                    title: 'إضافة رصيد',
                                    message: `قام والدك بإضافة ${amount} ريال إلى رصيدك`,
                                    isRead: false,
                                    date: new Date().toISOString().split('T')[0]
                                };
                                db.notifications.push(newNotification);

                                saveDatabase(db);

                                alert(`تم إضافة ${amount} ريال إلى رصيد ${child.name} بنجاح!`);
                                window.location.reload();
                            }
                        } else if (amount) {
                            alert('الرجاء إدخال مبلغ صحيح!');
                        }
                    }
                });
            });

            // View Orders Buttons
            document.querySelectorAll('.view-orders-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const childId = this.getAttribute('data-id');
                    window.location.href = `orders.html?childId=${childId}`;
                });
            });

            // إخفاء أقسام الحساسيات عند التحميل
            document.querySelectorAll('.allergies-section').forEach(section => {
                section.style.display = 'none';
            });

            // إضافة مستمعي الأحداث لأزرار الحساسيات
            attachAllergyButtonListeners();

            // إضافة مستمع الحدث لنموذج الحساسية
            document.getElementById('allergy-form').addEventListener('submit', saveAllergy);

            // إضافة مستمعي الأحداث لأزرار إغلاق النوافذ المنبثقة
            document.querySelectorAll('.close-btn, .cancel-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.getElementById('allergy-modal').style.display = 'none';
                    document.getElementById('delete-modal').style.display = 'none';
                });
            });

            // إضافة مستمع الحدث لزر تأكيد الحذف
            document.getElementById('confirm-delete-btn').addEventListener('click', function() {
                const childId = parseInt(this.getAttribute('data-child-id'));
                const allergyId = parseInt(this.getAttribute('data-allergy-id'));

                const db = getDatabase();
                const childIndex = db.users.findIndex(u => u.id === childId);

                if (childIndex === -1 || !db.users[childIndex].allergies) {
                    alert('لم يتم العثور على الطفل أو الحساسية!');
                    return;
                }

                const allergyIndex = db.users[childIndex].allergies.findIndex(a => a.id === allergyId);

                if (allergyIndex === -1) {
                    alert('لم يتم العثور على الحساسية!');
                    return;
                }

                // حذف الحساسية
                db.users[childIndex].allergies.splice(allergyIndex, 1);

                // حفظ التغييرات في قاعدة البيانات
                saveDatabase(db);

                // إغلاق نافذة التأكيد
                document.getElementById('delete-modal').style.display = 'none';

                // تحديث عرض الحساسيات
                const allergiesList = document.getElementById(`allergies-list-${childId}`);
                if (allergiesList) {
                    allergiesList.innerHTML = renderAllergiesList(db.users[childIndex]);

                    // إعادة إضافة مستمعي الأحداث للأزرار
                    attachAllergyButtonListeners();
                }

                alert('تم حذف الحساسية بنجاح!');
            });

            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });

        // Helper function to get grade text
        function getGradeText(grade) {
            switch(grade) {
                case '1':
                    return 'الصف الأول';
                case '2':
                    return 'الصف الثاني';
                case '3':
                    return 'الصف الثالث';
                case '4':
                    return 'الصف الرابع';
                case '5':
                    return 'الصف الخامس';
                case '6':
                    return 'الصف السادس';
                default:
                    return 'غير معروف';
            }
        }
    </script>
</body>
</html>
