<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العاملين - فواصل النجاح</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        /* متغيرات CSS */
        :root {
            --primary-color: #2e7d32;
            --dark-color: #1b5e20;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --info-color: #2196f3;
            --light-color: #f8f9fa;
            --border-color: #e0e0e0;
            --text-color: #333;
            --text-muted: #666;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        /* إعادة تعيين الأنماط */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-color);
        }

        /* رابط مركز الاختبارات */
        .test-center-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
            transition: var(--transition);
            box-shadow: var(--shadow);
        }

        .test-center-link:hover {
            background: var(--dark-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }

        /* الحاوي الرئيسي */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* الهيدر */
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .header p {
            color: var(--text-muted);
            font-size: 1rem;
        }

        .user-info {
            text-align: left;
            color: var(--text-muted);
        }

        .user-info strong {
            color: var(--primary-color);
        }

        /* شريط التنقل */
        .nav-links {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-links a {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: var(--dark-color);
            transform: translateY(-2px);
        }

        /* البطاقة الرئيسية */
        .main-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .card-header h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
        }

        .add-btn {
            background: linear-gradient(45deg, var(--primary-color), var(--success-color));
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 3px 10px rgba(46, 125, 50, 0.3);
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.4);
        }

        /* شريط البحث والفلاتر */
        .search-filter {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
            align-items: end;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 16px;
            transition: var(--transition);
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(46, 125, 50, 0.2);
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 14px;
        }

        .filter-group select {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: white;
            font-size: 14px;
            transition: var(--transition);
        }

        .filter-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(46, 125, 50, 0.2);
        }

        /* الجدول */
        .table-container {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
            color: white;
            padding: 15px 12px;
            text-align: right;
            font-weight: 600;
            font-size: 14px;
            white-space: nowrap;
        }

        table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        table tbody tr:hover {
            background-color: rgba(46, 125, 50, 0.05);
        }

        table tbody tr:last-child td {
            border-bottom: none;
        }

        /* شارات الحالة */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #e8f5e9;
            color: var(--primary-color);
        }

        .status-inactive {
            background-color: #ffebee;
            color: var(--danger-color);
        }

        /* شارات نوع العامل */
        .staff-type-badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-cashier {
            background: #e3f2fd;
            color: var(--info-color);
        }

        .type-cook {
            background: #fff3e0;
            color: var(--warning-color);
        }

        .type-manager {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-muted);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .edit-btn:hover {
            background: rgba(33, 150, 243, 0.1);
            color: var(--info-color);
        }

        .delete-btn:hover {
            background: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        .toggle-btn:hover {
            background: rgba(255, 152, 0, 0.1);
            color: var(--warning-color);
        }

        /* التصفح */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 2px solid var(--border-color);
            background: white;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            min-width: 40px;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow: auto;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 50px auto;
            padding: 0;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 600px;
            animation: slideIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 25px;
        }

        /* النماذج */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(46, 125, 50, 0.2);
        }

        .form-group input:disabled {
            background-color: var(--light-color);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        /* الصلاحيات */
        .permissions-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 10px;
            padding: 15px;
            background: var(--light-color);
            border-radius: var(--border-radius);
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            transition: var(--transition);
            cursor: pointer;
        }

        .permission-item:hover {
            background-color: rgba(46, 125, 50, 0.05);
            border-color: var(--primary-color);
        }

        .permission-item input[type="checkbox"] {
            width: auto;
            margin: 0 10px 0 0;
            transform: scale(1.2);
        }

        .permission-item label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
        }

        /* أزرار النماذج */
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .cancel-btn {
            background-color: var(--light-color);
            color: var(--text-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 12px 25px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
        }

        .cancel-btn:hover {
            background-color: var(--border-color);
        }

        .save-btn {
            background: linear-gradient(45deg, var(--primary-color), var(--success-color));
            color: white;
            border: none;
            border-radius: var(--border-radius);
            padding: 12px 25px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            box-shadow: 0 3px 10px rgba(46, 125, 50, 0.3);
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.4);
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--border-color);
            margin-bottom: 20px;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .empty-state p {
            margin-bottom: 20px;
        }

        /* التصميم المتجاوب */
        @media (max-width: 1024px) {
            .search-filter {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .search-box {
                grid-column: 1 / -1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .nav-links {
                flex-direction: column;
                gap: 10px;
            }

            .nav-links a {
                justify-content: center;
            }

            .card-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .search-filter {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .table-responsive {
                font-size: 12px;
            }

            table th,
            table td {
                padding: 8px 6px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .modal-content {
                width: 95%;
                margin: 20px auto;
            }

            .modal-body {
                padding: 20px;
            }

            .permissions-container {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
                gap: 10px;
            }

            .test-center-link {
                position: static;
                display: inline-block;
                margin-bottom: 15px;
            }
        }

        @media (max-width: 480px) {
            .pagination {
                flex-wrap: wrap;
                gap: 5px;
            }

            .pagination button {
                padding: 6px 10px;
                font-size: 12px;
                min-width: 35px;
            }

            .action-btn {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
            outline: none;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .cancel-btn {
            background-color: #f5f5f5;
            color: #333;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn:hover {
            background-color: #e0e0e0;
        }

        .save-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .save-btn:hover {
            background-color: var(--dark-color);
        }

        .permissions-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .permission-item:hover {
            background-color: #f9f9f9;
        }

        .permission-item input {
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                margin-bottom: 20px;
            }

            .search-filter {
                flex-direction: column;
            }

            .search-box, .filter-group {
                width: 100%;
            }

            .permissions-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- رابط مركز الاختبارات -->
    <a href="../../tests/index.html" class="test-center-link">
        <i class="fas fa-flask"></i> مركز الاختبارات
    </a>

    <div class="container">
        <!-- الهيدر -->
        <div class="header">
            <div>
                <h1><i class="fas fa-user-tie"></i> إدارة العاملين</h1>
                <p>إدارة شاملة لحسابات العاملين في النظام</p>
            </div>
            <div class="user-info">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
                <br>
                <small id="current-date"></small>
            </div>
        </div>

        <!-- شريط التنقل -->
        <div class="nav-links">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
            <a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a>
            <a href="staff.html" class="active"><i class="fas fa-user-tie"></i> إدارة العاملين</a>
            <a href="schools.html"><i class="fas fa-school"></i> إدارة المدارس</a>
            <a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a>
            <a href="../../tests/staff-management.html"><i class="fas fa-vial"></i> اختبار العاملين</a>
        </div>

        <!-- البطاقة الرئيسية -->
        <div class="main-card">
            <div class="card-header">
                <h2>قائمة العاملين</h2>
                <button id="add-staff-btn" class="add-btn">
                    <i class="fas fa-plus"></i> إضافة عامل جديد
                </button>
            </div>

            <!-- شريط البحث والفلاتر -->
            <div class="search-filter">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="البحث عن عامل...">
                </div>
                <div class="filter-group">
                    <label for="staff-type-filter">نوع العامل:</label>
                    <select id="staff-type-filter">
                        <option value="all">الكل</option>
                        <option value="cashier">أمين الصندوق</option>
                        <option value="cook">طباخ</option>
                        <option value="manager">مدير</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="school-filter">المدرسة:</label>
                    <select id="school-filter">
                        <option value="all">الكل</option>
                        <!-- سيتم ملء هذا القسم ديناميكيًا -->
                    </select>
                </div>
                <div class="filter-group">
                    <label for="status-filter">الحالة:</label>
                    <select id="status-filter">
                        <option value="all">الكل</option>
                        <option value="active">مفعل</option>
                        <option value="inactive">غير مفعل</option>
                    </select>
                </div>
            </div>

            <!-- الجدول -->
            <div class="table-container">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المستخدم</th>
                                <th>الاسم</th>
                                <th>نوع العامل</th>
                                <th>المدرسة</th>
                                <th>الصلاحيات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="staff-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>

                <!-- حالة فارغة -->
                <div class="empty-state" id="empty-state" style="display: none;">
                    <i class="fas fa-user-friends"></i>
                    <h3>لا يوجد عاملين</h3>
                    <p>لم يتم العثور على أي عاملين في النظام</p>
                    <button class="add-btn" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> إضافة أول عامل
                    </button>
                </div>
            </div>

            <!-- التصفح -->
            <div class="pagination" id="pagination">
                <!-- سيتم ملء هذا القسم ديناميكيًا -->
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل عامل -->
    <div class="modal" id="staff-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة عامل جديد</h3>
                <button class="close-btn" type="button">&times;</button>
            </div>
            <div class="modal-body">
                <form id="staff-form">
                    <input type="hidden" id="staff-id">

                    <div class="form-group">
                        <label for="staff-username">اسم المستخدم *</label>
                        <input type="text" id="staff-username" required placeholder="أدخل اسم المستخدم">
                    </div>

                    <div class="form-group">
                        <label for="staff-password">كلمة المرور *</label>
                        <input type="password" id="staff-password" required placeholder="أدخل كلمة المرور">
                    </div>

                    <div class="form-group">
                        <label for="staff-name">الاسم الكامل *</label>
                        <input type="text" id="staff-name" required placeholder="أدخل الاسم الكامل">
                    </div>

                    <div class="form-group">
                        <label for="staff-type">نوع العامل *</label>
                        <select id="staff-type" required>
                            <option value="">اختر نوع العامل</option>
                            <option value="cashier">أمين الصندوق</option>
                            <option value="cook">طباخ</option>
                            <option value="manager">مدير</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="staff-school">المدرسة *</label>
                        <select id="staff-school" required>
                            <option value="">اختر المدرسة</option>
                            <!-- سيتم ملء هذا القسم ديناميكيًا -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="staff-status">الحالة *</label>
                        <select id="staff-status" required>
                            <option value="active">مفعل</option>
                            <option value="inactive">غير مفعل</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>الصلاحيات</label>
                        <div class="permissions-container" id="permissions-container">
                            <div class="permission-item">
                                <input type="checkbox" id="perm-sell" value="sell">
                                <label for="perm-sell">البيع</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-view_products" value="view_products">
                                <label for="perm-view_products">عرض المنتجات</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-manage_products" value="manage_products">
                                <label for="perm-manage_products">إدارة المنتجات</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-view_orders" value="view_orders">
                                <label for="perm-view_orders">عرض الطلبات</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-manage_orders" value="manage_orders">
                                <label for="perm-manage_orders">إدارة الطلبات</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-view_students" value="view_students">
                                <label for="perm-view_students">عرض الطلاب</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="perm-view_reports" value="view_reports">
                                <label for="perm-view_reports">عرض التقارير</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="cancel-btn">إلغاء</button>
                        <button type="submit" class="save-btn">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <button class="close-btn" type="button">&times;</button>
            </div>
            <div class="modal-body">
                <p style="text-align: center; margin: 20px 0; font-size: 16px;">
                    هل أنت متأكد من حذف هذا العامل؟<br>
                    <strong style="color: var(--danger-color);">لا يمكن التراجع عن هذا الإجراء.</strong>
                </p>
                <div class="form-actions">
                    <button type="button" class="cancel-btn">إلغاء</button>
                    <button type="button" id="confirm-delete-btn" class="save-btn" style="background: var(--danger-color);">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // متغيرات عامة
        let currentPage = 1;
        const itemsPerPage = 10;
        let staffList = [];
        let filteredStaff = [];
        let currentStaffId = null;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // التحقق من صلاحيات المستخدم
                checkUserAuthorization();

                // تعيين التاريخ الحالي
                setCurrentDate();

                // تحميل بيانات المدارس
                loadSchools();

                // تحميل بيانات العاملين
                loadStaffData();

                // إضافة مستمعي الأحداث
                addEventListeners();

                console.log('تم تحميل صفحة إدارة العاملين بنجاح');
            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showNotification('حدث خطأ في تحميل الصفحة', 'error');
            }
        });

        // التحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم
                let currentUser = null;

                // البحث في sessionStorage أولاً
                const userDataString = sessionStorage.getItem('currentUser');
                if (userDataString) {
                    currentUser = JSON.parse(userDataString);
                }

                // إذا لم توجد، البحث في localStorage
                if (!currentUser) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'admin' && userId) {
                        const db = getDatabase();
                        currentUser = db.users.find(u => u.id === parseInt(userId) && u.role === 'admin');
                    }
                }

                // التحقق من الصلاحيات
                if (!currentUser || currentUser.role !== 'admin') {
                    showNotification('غير مصرح لك بالوصول إلى هذه الصفحة!', 'error');
                    setTimeout(() => {
                        window.location.href = '../auth/login.html';
                    }, 2000);
                    return false;
                }

                // تحديث اسم المستخدم
                document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
                return true;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                showNotification('حدث خطأ في التحقق من الصلاحيات', 'error');
                return false;
            }
        }

        // تعيين التاريخ الحالي
        function setCurrentDate() {
            try {
                const now = new Date();
                const options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            } catch (error) {
                console.error('خطأ في تعيين التاريخ:', error);
            }
        }

        // تحميل بيانات المدارس
        function loadSchools() {
            try {
                const db = getDatabase();
                const schools = db.schools || [];

                // ملء قائمة المدارس في فلتر البحث
                let schoolFilterOptions = '<option value="all">الكل</option>';
                let schoolSelectOptions = '<option value="">اختر المدرسة</option>';

                schools.forEach(school => {
                    schoolFilterOptions += `<option value="${school.id}">${school.name}</option>`;
                    schoolSelectOptions += `<option value="${school.id}">${school.name}</option>`;
                });

                document.getElementById('school-filter').innerHTML = schoolFilterOptions;
                document.getElementById('staff-school').innerHTML = schoolSelectOptions;
            } catch (error) {
                console.error('خطأ في تحميل بيانات المدارس:', error);
                showNotification('خطأ في تحميل بيانات المدارس', 'error');
            }
        }

        // تحميل بيانات العاملين
        function loadStaffData() {
            try {
                const db = getDatabase();
                staffList = db.users.filter(user => user.role === 'staff') || [];
                applyFilters();
            } catch (error) {
                console.error('خطأ في تحميل بيانات العاملين:', error);
                showNotification('خطأ في تحميل بيانات العاملين', 'error');
            }
        }

        // تطبيق الفلاتر
        function applyFilters() {
            try {
                const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
                const staffTypeValue = document.getElementById('staff-type-filter').value;
                const schoolValue = document.getElementById('school-filter').value;
                const statusValue = document.getElementById('status-filter').value;

                filteredStaff = staffList.filter(staff => {
                    const matchesSearch = staff.name.toLowerCase().includes(searchTerm) ||
                                        staff.username.toLowerCase().includes(searchTerm);
                    const matchesType = staffTypeValue === 'all' || staff.staffType === staffTypeValue;
                    const matchesSchool = schoolValue === 'all' || staff.schoolId === parseInt(schoolValue);
                    const matchesStatus = statusValue === 'all' || staff.status === statusValue;

                    return matchesSearch && matchesType && matchesSchool && matchesStatus;
                });

                currentPage = 1;
                renderStaffTable();
                renderPagination();
            } catch (error) {
                console.error('خطأ في تطبيق الفلاتر:', error);
            }
        }

        // عرض جدول العاملين
        function renderStaffTable() {
            try {
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, filteredStaff.length);
                const currentPageItems = filteredStaff.slice(startIndex, endIndex);
                const staffTable = document.getElementById('staff-table');
                const emptyState = document.getElementById('empty-state');

                if (currentPageItems.length === 0) {
                    staffTable.innerHTML = '';
                    emptyState.style.display = 'block';
                    return;
                }

                emptyState.style.display = 'none';

                const tableRows = currentPageItems.map((staff, index) => {
                    const db = getDatabase();
                    const school = db.schools.find(s => s.id === staff.schoolId);
                    const schoolName = school ? school.name : 'غير محدد';

                    const staffTypeText = {
                        'cashier': 'أمين الصندوق',
                        'cook': 'طباخ',
                        'manager': 'مدير'
                    }[staff.staffType] || staff.staffType;

                    const permissionsText = staff.permissions ? staff.permissions.slice(0, 3).join(', ') +
                                          (staff.permissions.length > 3 ? '...' : '') : '';

                    const statusClass = staff.status === 'active' ? 'status-active' : 'status-inactive';
                    const statusText = staff.status === 'active' ? 'مفعل' : 'غير مفعل';

                    return `
                        <tr>
                            <td>${startIndex + index + 1}</td>
                            <td>${staff.username}</td>
                            <td>${staff.name}</td>
                            <td><span class="staff-type-badge type-${staff.staffType}">${staffTypeText}</span></td>
                            <td>${schoolName}</td>
                            <td title="${staff.permissions ? staff.permissions.join(', ') : ''}">${permissionsText}</td>
                            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn edit-btn" onclick="editStaff(${staff.id})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn toggle-btn" onclick="toggleStaffStatus(${staff.id})" title="تغيير الحالة">
                                        <i class="fas fa-toggle-${staff.status === 'active' ? 'on' : 'off'}"></i>
                                    </button>
                                    <button class="action-btn delete-btn" onclick="deleteStaff(${staff.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                staffTable.innerHTML = tableRows;
            } catch (error) {
                console.error('خطأ في عرض جدول العاملين:', error);
                showNotification('خطأ في عرض البيانات', 'error');
            }
        }

        // عرض التصفح
        function renderPagination() {
            try {
                const totalPages = Math.ceil(filteredStaff.length / itemsPerPage);
                const pagination = document.getElementById('pagination');

                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // زر السابق
                paginationHTML += `
                    <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                `;

                // أرقام الصفحات
                for (let i = 1; i <= totalPages; i++) {
                    if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                        paginationHTML += `
                            <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                                ${i}
                            </button>
                        `;
                    } else if (i === currentPage - 2 || i === currentPage + 2) {
                        paginationHTML += '<span>...</span>';
                    }
                }

                // زر التالي
                paginationHTML += `
                    <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                `;

                pagination.innerHTML = paginationHTML;
            } catch (error) {
                console.error('خطأ في عرض التصفح:', error);
            }
        }

        // تغيير الصفحة
        function changePage(page) {
            const totalPages = Math.ceil(filteredStaff.length / itemsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderStaffTable();
                renderPagination();
            }
        }

        // إضافة مستمعي الأحداث
        function addEventListeners() {
            try {
                // البحث والفلاتر
                document.getElementById('search-input').addEventListener('input', applyFilters);
                document.getElementById('staff-type-filter').addEventListener('change', applyFilters);
                document.getElementById('school-filter').addEventListener('change', applyFilters);
                document.getElementById('status-filter').addEventListener('change', applyFilters);

                // زر إضافة عامل
                document.getElementById('add-staff-btn').addEventListener('click', openAddModal);

                // النوافذ المنبثقة
                document.querySelectorAll('.close-btn, .cancel-btn').forEach(btn => {
                    btn.addEventListener('click', closeModals);
                });

                // نموذج العامل
                document.getElementById('staff-form').addEventListener('submit', saveStaff);

                // زر تأكيد الحذف
                document.getElementById('confirm-delete-btn').addEventListener('click', confirmDelete);

                // إغلاق النوافذ عند النقر خارجها
                window.addEventListener('click', function(event) {
                    const staffModal = document.getElementById('staff-modal');
                    const deleteModal = document.getElementById('delete-modal');
                    if (event.target === staffModal) staffModal.style.display = 'none';
                    if (event.target === deleteModal) deleteModal.style.display = 'none';
                });

                // تحديث الصلاحيات عند تغيير نوع العامل
                document.getElementById('staff-type').addEventListener('change', updatePermissions);

            } catch (error) {
                console.error('خطأ في إضافة مستمعي الأحداث:', error);
            }
        }

        // فتح نافذة إضافة عامل
        function openAddModal() {
            try {
                currentStaffId = null;
                document.getElementById('modal-title').textContent = 'إضافة عامل جديد';
                document.getElementById('staff-form').reset();
                document.getElementById('staff-password').disabled = false;
                document.getElementById('staff-password').required = true;

                // إعادة تعيين الصلاحيات
                document.querySelectorAll('#permissions-container input[type="checkbox"]').forEach(cb => {
                    cb.checked = false;
                });

                document.getElementById('staff-modal').style.display = 'block';
            } catch (error) {
                console.error('خطأ في فتح نافذة الإضافة:', error);
            }
        }

        // تعديل عامل
        function editStaff(staffId) {
            try {
                const staff = staffList.find(s => s.id === staffId);
                if (!staff) {
                    showNotification('لم يتم العثور على العامل', 'error');
                    return;
                }

                currentStaffId = staffId;
                document.getElementById('modal-title').textContent = 'تعديل بيانات العامل';

                // ملء النموذج
                document.getElementById('staff-username').value = staff.username;
                document.getElementById('staff-password').value = '';
                document.getElementById('staff-password').disabled = true;
                document.getElementById('staff-password').required = false;
                document.getElementById('staff-name').value = staff.name;
                document.getElementById('staff-type').value = staff.staffType;
                document.getElementById('staff-school').value = staff.schoolId;
                document.getElementById('staff-status').value = staff.status || 'active';

                // تحديد الصلاحيات
                document.querySelectorAll('#permissions-container input[type="checkbox"]').forEach(cb => {
                    cb.checked = staff.permissions && staff.permissions.includes(cb.value);
                });

                document.getElementById('staff-modal').style.display = 'block';
            } catch (error) {
                console.error('خطأ في تعديل العامل:', error);
                showNotification('خطأ في تعديل العامل', 'error');
            }
        }

        // حذف عامل
        function deleteStaff(staffId) {
            currentStaffId = staffId;
            document.getElementById('delete-modal').style.display = 'block';
        }

        // تأكيد الحذف
        function confirmDelete() {
            try {
                if (!currentStaffId) return;

                const db = getDatabase();
                const staffIndex = db.users.findIndex(u => u.id === currentStaffId && u.role === 'staff');

                if (staffIndex === -1) {
                    showNotification('لم يتم العثور على العامل', 'error');
                    return;
                }

                db.users.splice(staffIndex, 1);
                saveDatabase(db);

                closeModals();
                loadStaffData();
                showNotification('تم حذف العامل بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في حذف العامل:', error);
                showNotification('خطأ في حذف العامل', 'error');
            }
        }

        // تغيير حالة العامل
        function toggleStaffStatus(staffId) {
            try {
                const db = getDatabase();
                const staffIndex = db.users.findIndex(u => u.id === staffId && u.role === 'staff');

                if (staffIndex === -1) {
                    showNotification('لم يتم العثور على العامل', 'error');
                    return;
                }

                const currentStatus = db.users[staffIndex].status;
                db.users[staffIndex].status = currentStatus === 'active' ? 'inactive' : 'active';

                saveDatabase(db);
                loadStaffData();

                const newStatus = db.users[staffIndex].status === 'active' ? 'مفعل' : 'غير مفعل';
                showNotification(`تم تغيير حالة العامل إلى: ${newStatus}`, 'success');
            } catch (error) {
                console.error('خطأ في تغيير حالة العامل:', error);
                showNotification('خطأ في تغيير حالة العامل', 'error');
            }
        }

        // حفظ بيانات العامل
        function saveStaff(event) {
            event.preventDefault();

            try {
                const username = document.getElementById('staff-username').value.trim();
                const password = document.getElementById('staff-password').value.trim();
                const name = document.getElementById('staff-name').value.trim();
                const staffType = document.getElementById('staff-type').value;
                const schoolId = parseInt(document.getElementById('staff-school').value);
                const status = document.getElementById('staff-status').value;

                // جمع الصلاحيات
                const permissions = [];
                document.querySelectorAll('#permissions-container input[type="checkbox"]:checked').forEach(cb => {
                    permissions.push(cb.value);
                });

                // التحقق من البيانات
                if (!username || !name || !staffType || !schoolId) {
                    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                const db = getDatabase();

                // التحقق من تكرار اسم المستخدم
                const existingUser = db.users.find(user =>
                    user.username === username && (!currentStaffId || user.id !== currentStaffId)
                );

                if (existingUser) {
                    showNotification('اسم المستخدم موجود بالفعل', 'error');
                    return;
                }

                if (currentStaffId) {
                    // تحديث عامل موجود
                    const staffIndex = db.users.findIndex(user => user.id === currentStaffId);
                    if (staffIndex === -1) {
                        showNotification('لم يتم العثور على العامل', 'error');
                        return;
                    }

                    db.users[staffIndex] = {
                        ...db.users[staffIndex],
                        username,
                        name,
                        staffType,
                        schoolId,
                        status,
                        permissions
                    };

                    if (password) {
                        db.users[staffIndex].password = password;
                    }
                } else {
                    // إضافة عامل جديد
                    if (!password) {
                        showNotification('يرجى إدخال كلمة المرور', 'error');
                        return;
                    }

                    const newId = Math.max(...db.users.map(user => user.id), 0) + 1;
                    const newStaff = {
                        id: newId,
                        username,
                        password,
                        name,
                        role: 'staff',
                        staffType,
                        schoolId,
                        status,
                        permissions
                    };

                    db.users.push(newStaff);
                }

                saveDatabase(db);
                closeModals();
                loadStaffData();

                const message = currentStaffId ? 'تم تحديث بيانات العامل بنجاح' : 'تم إضافة العامل بنجاح';
                showNotification(message, 'success');
            } catch (error) {
                console.error('خطأ في حفظ العامل:', error);
                showNotification('خطأ في حفظ بيانات العامل', 'error');
            }
        }

        // إغلاق النوافذ المنبثقة
        function closeModals() {
            document.getElementById('staff-modal').style.display = 'none';
            document.getElementById('delete-modal').style.display = 'none';
        }

        // تحديث الصلاحيات حسب نوع العامل
        function updatePermissions() {
            const staffType = document.getElementById('staff-type').value;
            const defaultPermissions = {
                'cashier': ['sell', 'view_products', 'view_students'],
                'cook': ['view_products', 'view_orders'],
                'manager': ['sell', 'view_products', 'view_students', 'view_reports', 'manage_products', 'manage_orders']
            };

            const permissions = defaultPermissions[staffType] || [];

            document.querySelectorAll('#permissions-container input[type="checkbox"]').forEach(cb => {
                cb.checked = permissions.includes(cb.value);
            });
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 10000;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // إضافة أنماط الحركة للإشعارات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
