<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تخصيص المظهر - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .theme-container {
            max-width: 1000px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .theme-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .theme-header h2 {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .theme-header p {
            color: #666;
        }
        
        .theme-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .theme-option {
            border: 2px solid transparent;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .theme-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }
        
        .theme-option.active {
            border-color: var(--primary-color);
        }
        
        .theme-preview {
            height: 150px;
            display: flex;
            flex-direction: column;
        }
        
        .theme-header-preview {
            height: 30%;
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: white;
            font-weight: 500;
        }
        
        .theme-body-preview {
            height: 70%;
            display: flex;
        }
        
        .theme-sidebar-preview {
            width: 30%;
            height: 100%;
        }
        
        .theme-content-preview {
            width: 70%;
            height: 100%;
            background-color: #f5f5f5;
            padding: 10px;
        }
        
        .theme-content-preview .card {
            background-color: white;
            height: 40px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .theme-name {
            text-align: center;
            padding: 15px;
            font-weight: 500;
            font-size: 1.1rem;
            border-top: 1px solid #eee;
        }
        
        .custom-theme-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .custom-theme-section h3 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .color-pickers {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .color-picker {
            margin-bottom: 15px;
        }
        
        .color-picker label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .color-picker input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .theme-preview-live {
            margin-top: 30px;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--dark-color);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #eee;
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .back-link i {
            margin-left: 5px;
        }
        
        @media (max-width: 768px) {
            .theme-container {
                padding: 20px;
                margin: 20px;
            }
            
            .theme-options {
                grid-template-columns: 1fr;
            }
            
            .color-pickers {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Theme Customization Section -->
    <div class="theme-container">
        <a href="javascript:history.back()" class="back-link"><i class="fas fa-arrow-right"></i> العودة</a>
        
        <div class="theme-header">
            <h2>تخصيص المظهر</h2>
            <p>اختر المظهر المفضل لديك أو قم بإنشاء مظهر مخصص</p>
        </div>
        
        <div class="theme-options" id="theme-options">
            <!-- سيتم ملء هذا القسم ديناميكيًا -->
        </div>
        
        <div class="custom-theme-section">
            <h3>إنشاء مظهر مخصص</h3>
            <div class="color-pickers">
                <div class="color-picker">
                    <label for="primary-color">اللون الرئيسي</label>
                    <input type="color" id="primary-color" value="#2e7d32">
                </div>
                <div class="color-picker">
                    <label for="secondary-color">اللون الثانوي</label>
                    <input type="color" id="secondary-color" value="#f9a825">
                </div>
                <div class="color-picker">
                    <label for="dark-color">اللون الداكن</label>
                    <input type="color" id="dark-color" value="#1b5e20">
                </div>
                <div class="color-picker">
                    <label for="light-color">اللون الفاتح</label>
                    <input type="color" id="light-color" value="#e8f5e9">
                </div>
            </div>
            
            <div class="theme-preview-live" id="theme-preview-live">
                <div class="theme-preview">
                    <div class="theme-header-preview" id="header-preview">
                        العنوان
                    </div>
                    <div class="theme-body-preview">
                        <div class="theme-sidebar-preview" id="sidebar-preview"></div>
                        <div class="theme-content-preview">
                            <div class="card"></div>
                            <div class="card"></div>
                        </div>
                    </div>
                </div>
                <div class="theme-name">معاينة المظهر المخصص</div>
            </div>
        </div>
        
        <div class="actions">
            <button id="apply-theme" class="btn btn-primary">تطبيق المظهر</button>
            <button id="save-theme" class="btn btn-secondary">حفظ كمظهر جديد</button>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser) {
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            
            // Get database
            const db = getDatabase();
            
            // Get current theme
            const currentThemeId = getCurrentTheme();
            
            // Populate theme options
            const themeOptions = document.getElementById('theme-options');
            db.settings.themes.forEach(theme => {
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.setAttribute('data-id', theme.id);
                
                // Set active theme
                if (theme.id === currentThemeId) {
                    themeOption.classList.add('active');
                    
                    // Set color picker values to current theme
                    document.getElementById('primary-color').value = theme.primaryColor;
                    document.getElementById('secondary-color').value = theme.secondaryColor;
                    document.getElementById('dark-color').value = theme.darkColor;
                    document.getElementById('light-color').value = theme.lightColor;
                    
                    updatePreview();
                }
                
                themeOption.innerHTML = `
                    <div class="theme-preview">
                        <div class="theme-header-preview" style="background-color: ${theme.primaryColor};">
                            العنوان
                        </div>
                        <div class="theme-body-preview">
                            <div class="theme-sidebar-preview" style="background-color: ${theme.darkColor};"></div>
                            <div class="theme-content-preview">
                                <div class="card"></div>
                                <div class="card"></div>
                            </div>
                        </div>
                    </div>
                    <div class="theme-name">${theme.name}</div>
                `;
                
                themeOptions.appendChild(themeOption);
            });
            
            // Theme selection
            document.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.theme-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Get selected theme
                    const themeId = parseInt(this.getAttribute('data-id'));
                    const theme = db.settings.themes.find(t => t.id === themeId);
                    
                    // Update color pickers
                    document.getElementById('primary-color').value = theme.primaryColor;
                    document.getElementById('secondary-color').value = theme.secondaryColor;
                    document.getElementById('dark-color').value = theme.darkColor;
                    document.getElementById('light-color').value = theme.lightColor;
                    
                    updatePreview();
                });
            });
            
            // Color picker change events
            document.getElementById('primary-color').addEventListener('input', updatePreview);
            document.getElementById('secondary-color').addEventListener('input', updatePreview);
            document.getElementById('dark-color').addEventListener('input', updatePreview);
            document.getElementById('light-color').addEventListener('input', updatePreview);
            
            // Update preview function
            function updatePreview() {
                const primaryColor = document.getElementById('primary-color').value;
                const darkColor = document.getElementById('dark-color').value;
                
                document.getElementById('header-preview').style.backgroundColor = primaryColor;
                document.getElementById('sidebar-preview').style.backgroundColor = darkColor;
            }
            
            // Apply theme button
            document.getElementById('apply-theme').addEventListener('click', function() {
                const selectedTheme = document.querySelector('.theme-option.active');
                
                if (selectedTheme) {
                    const themeId = parseInt(selectedTheme.getAttribute('data-id'));
                    applyTheme(themeId);
                    alert('تم تطبيق المظهر بنجاح!');
                    window.location.reload();
                } else {
                    // Apply custom theme
                    const primaryColor = document.getElementById('primary-color').value;
                    const secondaryColor = document.getElementById('secondary-color').value;
                    const darkColor = document.getElementById('dark-color').value;
                    const lightColor = document.getElementById('light-color').value;
                    
                    // Apply custom colors
                    document.documentElement.style.setProperty('--primary-color', primaryColor);
                    document.documentElement.style.setProperty('--secondary-color', secondaryColor);
                    document.documentElement.style.setProperty('--dark-color', darkColor);
                    document.documentElement.style.setProperty('--light-color', lightColor);
                    
                    alert('تم تطبيق المظهر المخصص بنجاح!');
                }
            });
            
            // Save theme button
            document.getElementById('save-theme').addEventListener('click', function() {
                const themeName = prompt('أدخل اسم المظهر الجديد:');
                
                if (themeName) {
                    const primaryColor = document.getElementById('primary-color').value;
                    const secondaryColor = document.getElementById('secondary-color').value;
                    const darkColor = document.getElementById('dark-color').value;
                    const lightColor = document.getElementById('light-color').value;
                    
                    // Create new theme
                    const newTheme = {
                        id: db.settings.themes.length + 1,
                        name: themeName,
                        primaryColor: primaryColor,
                        secondaryColor: secondaryColor,
                        darkColor: darkColor,
                        lightColor: lightColor
                    };
                    
                    // Add to database
                    db.settings.themes.push(newTheme);
                    saveDatabase(db);
                    
                    alert('تم حفظ المظهر الجديد بنجاح!');
                    window.location.reload();
                }
            });
        });
    </script>
</body>
</html>
