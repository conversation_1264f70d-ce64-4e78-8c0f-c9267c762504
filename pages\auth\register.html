<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2e7d32">
    <meta name="description" content="صفحة إنشاء حساب جديد لنظام سمارت مقصف - نظام إدارة المقاصف المدرسية">
    <meta name="author" content="سمارت مقصف">
    <title>إنشاء حساب جديد - سمارت مقصف</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- تعريف الأنماط الأساسية قبل تحميل ملفات CSS الخارجية -->
    <style>
        /* تعريف المتغيرات الأساسية */
        :root {
            --primary-color: #2e7d32;
            --secondary-color: #f9a825;
            --dark-color: #1b5e20;
            --light-color: #e8f5e9;
            --text-color: #333;
            --white-color: #fff;
            --beta-color: #ff5722;
            --beta-dark-color: #e64a19;
        }

        /* أنماط أساسية لمنع FOUC (Flash of Unstyled Content) */
        body {
            opacity: 1;
            visibility: visible;
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 25%, #e0f2f1 50%, #f3e5f5 75%, #fff3e0 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 30px 0 0 0; /* إضافة مساحة للشريط التجريبي */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            position: relative;
            overflow-x: hidden;
        }

        /* تأثير الخلفية المتحركة */
        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* عناصر هندسية في الخلفية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(46, 125, 50, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(249, 168, 37, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(46, 125, 50, 0.05) 0%, transparent 50%);
            z-index: -2;
            animation: float 20s ease-in-out infinite;
        }

        /* عناصر هندسية إضافية */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(45deg, transparent 40%, rgba(46, 125, 50, 0.03) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(249, 168, 37, 0.03) 50%, transparent 60%);
            z-index: -1;
            animation: rotate 30s linear infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) scale(1);
            }
            50% {
                transform: translateY(-20px) scale(1.05);
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* شريط تجريبي */
        .beta-banner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(45deg, var(--beta-dark-color), var(--beta-color));
            color: white;
            text-align: center;
            padding: 5px 0;
            font-weight: bold;
            z-index: 2000;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);
            animation: pulse 2s infinite;
        }

        .beta-banner span {
            display: inline-block;
            padding: 1px 8px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            margin: 0 4px;
            font-size: 0.8rem;
            transform: rotate(-2deg);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* تحسين توافق المتصفحات */
        html, body {
            width: 100%;
            height: 100%;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            overflow-x: hidden;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
        }

        /* تحسينات للطباعة */
        @media print {
            .beta-banner,
            .header,
            .auth-features,
            .geometric-shapes,
            .particles,
            .zoom-controls {
                display: none !important;
            }

            .auth-container {
                box-shadow: none !important;
                border: 1px solid #000 !important;
                transform: none !important;
            }

            .auth-left {
                background: #f5f5f5 !important;
            }
        }

        /* تأثيرات حركية */
        @-webkit-keyframes fadeIn {
            from { opacity: 0; -webkit-transform: translateY(20px); }
            to { opacity: 1; -webkit-transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @-webkit-keyframes fadeOut {
            from { opacity: 1; -webkit-transform: translate(-50%, -50%); }
            to { opacity: 0; -webkit-transform: translate(-50%, -60%); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, -50%); }
            to { opacity: 0; transform: translate(-50%, -60%); }
        }

        @-webkit-keyframes pulse {
            0% { -webkit-transform: scale(1); }
            50% { -webkit-transform: scale(1.05); }
            100% { -webkit-transform: scale(1); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .success-message, .error-message {
            animation: fadeIn 0.5s ease forwards;
            -webkit-animation: fadeIn 0.5s ease forwards;
        }

        /* تنبيه تحت الحقول */
        .form-warning {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.3);
            border-radius: 6px;
            color: #e65100;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-warning i {
            color: #ff9800;
            font-size: 0.9rem;
        }
    </style>

    <!-- Google Fonts - تحميل مسبق -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome - تحميل مسبق -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <style>
        /* تحسينات خاصة بصفحة إنشاء الحساب */

        /* عناصر هندسية متحركة */
        .geometric-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: floatShapes 15s ease-in-out infinite;
        }

        .shape-1 {
            top: 10%;
            left: 10%;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape-2 {
            top: 20%;
            right: 15%;
            width: 40px;
            height: 40px;
            background: var(--secondary-color);
            transform: rotate(45deg);
            animation-delay: 2s;
        }

        .shape-3 {
            bottom: 30%;
            left: 20%;
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 20px;
            animation-delay: 4s;
        }

        .shape-4 {
            bottom: 15%;
            right: 25%;
            width: 50px;
            height: 50px;
            background: var(--secondary-color);
            border-radius: 50%;
            animation-delay: 6s;
        }

        .shape-5 {
            top: 50%;
            left: 5%;
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            transform: rotate(45deg);
            animation-delay: 8s;
        }

        .shape-6 {
            top: 70%;
            right: 10%;
            width: 70px;
            height: 70px;
            background: var(--secondary-color);
            border-radius: 35px;
            animation-delay: 10s;
        }

        @keyframes floatShapes {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.1;
            }
            25% {
                transform: translateY(-20px) rotate(90deg);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-10px) rotate(180deg);
                opacity: 0.15;
            }
            75% {
                transform: translateY(-30px) rotate(270deg);
                opacity: 0.25;
            }
        }

        /* تحسين عرض الصفحة على الأجهزة المختلفة */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
            position: relative;
            z-index: 1;
        }

        /* تحسينات إضافية للتجاوب */
        * {
            box-sizing: border-box;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        /* منع التمرير الأفقي */
        html, body {
            overflow-x: hidden;
        }

        /* تحسين الخطوط للشاشات الصغيرة */
        @media (max-width: 480px) {
            html {
                font-size: 14px;
            }
        }

        @media (max-width: 320px) {
            html {
                font-size: 13px;
            }
        }

        /* جسيمات متحركة */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -3;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.3;
            animation: particleFloat 20s linear infinite;
        }

        .particle:nth-child(2n) {
            background: var(--secondary-color);
            animation-duration: 25s;
        }

        .particle:nth-child(3n) {
            width: 6px;
            height: 6px;
            animation-duration: 30s;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* تحسين حجم الشريط العلوي */
        .header {
            padding: 12px 0;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .header .container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
        }

        .header .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .header .logo a {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: inherit;
        }

        .header .logo h1 {
            font-size: 1rem;
            margin: 0;
            line-height: 1.2;
            color: var(--text-color);
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 280px;
        }

        .header .logo i {
            font-size: 2rem;
            color: #4caf50;
            flex-shrink: 0;
            filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            animation: headerLogoAnimation 3s ease-in-out infinite;
            transition: all 0.3s ease;
            transform: rotate(-10deg);
        }

        .header .logo i:hover {
            transform: rotate(-5deg) scale(1.1);
            filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
        }

        /* تأثير متقدم للشعار الرئيسي */
        @keyframes headerLogoAnimation {
            0%, 100% {
                transform: rotate(-10deg) scale(1);
                filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
            }
            25% {
                transform: rotate(-12deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
            50% {
                transform: rotate(-15deg) scale(1.05);
                filter: drop-shadow(0 0 12px rgba(76, 175, 80, 0.8));
            }
            75% {
                transform: rotate(-18deg) scale(1.02);
                filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.7));
            }
        }

        /* قسم تسجيل الدخول */
        .auth-section {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            min-height: calc(100vh - 60px);
            padding: 20px 20px;
            padding-top: 30px;
            padding-bottom: 30px;
        }

        /* تحسينات حاوية إنشاء الحساب - عرض كامل */
        .auth-container {
            max-width: 800px;
            width: 95%;
            margin: 0 auto;
            padding: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 10;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            -webkit-transition: opacity 0.3s ease, visibility 0.3s ease;
            min-height: auto;
            overflow: visible;
        }

        /* رأس النموذج المتقدم */
        .page-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
        }

        .page-header h1 {
            font-size: 1.8rem;
            margin: 0 0 8px 0;
            font-weight: 700;
            color: var(--primary-color);
            letter-spacing: 0.3px;
            line-height: 1.2;
            position: relative;
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .page-header p {
            font-size: 0.95rem;
            margin: 0;
            color: #666;
            font-weight: 400;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .auth-header h2 {
            font-size: 1.5rem;
            margin: 0 0 6px 0;
            color: var(--text-color);
            font-weight: 700;
            letter-spacing: 0.3px;
        }

        .auth-header p {
            font-size: 0.85rem;
            margin: 0;
            color: #666;
            font-weight: 400;
        }

        /* محدد نوع المستخدم المتقدم - مصغر */
        .user-type-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 25px;
            justify-content: center;
        }

        .user-type-option {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(46, 125, 50, 0.15);
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(8px);
            min-height: 90px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            user-select: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .user-type-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .user-type-option:hover::before {
            left: 100%;
        }

        .user-type-option:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.2);
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 1);
        }

        .user-type-option.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4caf50 100%);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 6px 18px rgba(46, 125, 50, 0.3);
        }

        .user-type-option.active::before {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        .user-icon-wrapper {
            position: relative;
            margin-bottom: 8px;
        }

        .icon-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, rgba(46, 125, 50, 0.15) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .user-type-option:hover .icon-glow {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.3);
        }

        .user-type-option.active .icon-glow {
            background: radial-gradient(circle, rgba(255, 255, 255, 0.25) 0%, transparent 70%);
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.5);
        }

        .user-icon-wrapper i {
            font-size: 1.8rem;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            color: var(--primary-color);
        }

        .user-type-option:hover .user-icon-wrapper i {
            transform: scale(1.1) rotate(3deg);
            animation: iconPulse 0.8s ease infinite;
        }

        .user-type-option.active .user-icon-wrapper i {
            color: white;
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
            transform: scale(1.05);
        }

        @keyframes iconPulse {
            0%, 100% {
                transform: scale(1.1) rotate(3deg);
                filter: drop-shadow(0 0 6px rgba(46, 125, 50, 0.5));
            }
            50% {
                transform: scale(1.15) rotate(-3deg);
                filter: drop-shadow(0 0 12px rgba(46, 125, 50, 0.7));
            }
        }

        .user-content span {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color);
            display: block;
            margin-bottom: 3px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .user-type-option.active .user-content span {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .user-description {
            font-size: 0.7rem;
            opacity: 0.75;
            font-weight: 400;
            line-height: 1.2;
        }

        /* النموذج المصغر */
        .auth-form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 18px;
            position: relative;
        }

        .form-group label {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.85rem;
            gap: 6px;
        }

        .form-group label i {
            font-size: 0.9rem;
            color: var(--primary-color);
            width: 16px;
            text-align: center;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 0.9rem;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px 15px 10px 35px;
            border: 2px solid rgba(233, 236, 239, 0.6);
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(3px);
        }

        .form-group select {
            padding-left: 35px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.08);
            transform: translateY(-1px);
            background: white;
        }

        .form-group input:focus + .input-icon,
        .form-group select:focus + .input-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        .form-group input::placeholder {
            color: #aaa;
            font-style: italic;
            font-size: 13px;
        }

        /* تأثيرات خاصة للأيقونات */
        .input-icon.fas {
            filter: drop-shadow(0 0 3px rgba(46, 125, 50, 0.3));
        }

        .form-group:hover .input-icon {
            color: var(--secondary-color);
            transform: translateY(-50%) scale(1.05);
        }

        /* صفوف الحقول الأفقية */
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 18px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        /* تحسينات للحقول الجانبية */
        .form-row .form-group:first-child {
            margin-right: 0;
        }

        .form-row .form-group:last-child {
            margin-left: 0;
        }

        /* زر الإرسال المصغر */
        .btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4caf50 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(46, 125, 50, 0.25);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn i {
            font-size: 1rem;
        }

        /* تذييل النموذج */
        .auth-footer {
            text-align: center;
            margin-top: 15px;
        }

        .auth-footer p {
            font-size: 0.8rem;
            color: #666;
            margin: 0;
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-footer a:hover {
            color: #4caf50;
            text-decoration: underline;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .auth-container {
                max-width: 95%;
                padding: 25px 20px;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .page-header p {
                font-size: 0.85rem;
            }

            .user-type-selector {
                grid-template-columns: 1fr;
                gap: 10px;
                margin-bottom: 20px;
            }

            .user-type-option {
                min-height: 75px;
                padding: 12px 8px;
            }

            .user-icon-wrapper i {
                font-size: 1.5rem;
            }

            .user-content span {
                font-size: 0.8rem;
            }

            .user-description {
                font-size: 0.65rem;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-group label {
                font-size: 0.8rem;
            }

            .form-group input,
            .form-group select {
                padding: 8px 12px 8px 30px;
                font-size: 13px;
            }

            /* تحويل الصفوف إلى عمودي في الأجهزة المحمولة */
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-row .form-group {
                margin-bottom: 15px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .header .logo h1 {
                font-size: 0.9rem;
            }

            .header .logo i {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .auth-container {
                border-radius: 12px;
                max-width: 98%;
                padding: 20px 15px;
            }

            .page-header h1 {
                font-size: 1.3rem;
            }

            .page-header p {
                font-size: 0.8rem;
            }

            .user-type-option {
                min-height: 65px;
                padding: 10px 6px;
            }

            .user-icon-wrapper i {
                font-size: 1.3rem;
            }

            .user-content span {
                font-size: 0.75rem;
            }

            .user-description {
                font-size: 0.6rem;
            }

            .form-group {
                margin-bottom: 12px;
            }

            .form-group label {
                font-size: 0.75rem;
            }

            .form-group input,
            .form-group select {
                padding: 7px 10px 7px 28px;
                font-size: 12px;
            }

            /* تحويل الصفوف إلى عمودي في الأجهزة الصغيرة */
            .form-row {
                flex-direction: column;
                gap: 12px;
            }

            .form-row .form-group {
                margin-bottom: 12px;
            }

            .btn {
                padding: 9px 18px;
                font-size: 0.85rem;
            }

            .header .logo h1 {
                font-size: 0.8rem;
            }

            .header .logo i {
                font-size: 1.3rem;
            }
        }

        /* رسائل النجاح والخطأ المتقدمة */
        .advanced-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            max-width: 500px;
            width: 90%;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            animation: messageSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .success-message {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(46, 125, 50, 0.95) 100%);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .error-message {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .message-content {
            display: flex;
            align-items: flex-start;
            padding: 25px;
            color: white;
            position: relative;
        }

        .message-icon {
            flex-shrink: 0;
            margin-left: 15px;
            font-size: 2rem;
            opacity: 0.9;
        }

        .message-text {
            flex: 1;
        }

        .message-text h4 {
            margin: 0 0 8px 0;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .message-text p {
            margin: 0;
            font-size: 0.95rem;
            line-height: 1.5;
            opacity: 0.9;
        }

        .message-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        @keyframes messageSlideIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1) translateY(0);
            }
        }

        /* تأثيرات التحميل للزر */
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- شريط تجريبي -->
    <div class="beta-banner">
        🚀 <span>نسخة تجريبية</span> - نظام سمارت مقصف قيد التطوير
    </div>

    <!-- الشريط العلوي -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="../../index.html">
                    <i class="fas fa-utensils"></i>
                    <h1>سمارت مقصف</h1>
                </a>
            </div>
        </div>
    </header>

    <!-- العناصر الهندسية المتحركة -->
    <div class="geometric-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
    </div>

    <!-- الجسيمات المتحركة -->
    <div class="particles" id="particles"></div>

    <!-- قسم إنشاء الحساب -->
    <section class="auth-section">
        <div class="auth-container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <h1>إنشاء حساب جديد</h1>
                <p>اختر نوع الحساب وأدخل بياناتك</p>
            </div>

            <!-- محدد نوع المستخدم المتقدم -->
            <div class="user-type-selector">
                <div class="user-type-option active" data-type="student" onclick="selectUserType('student')">
                    <div class="user-icon-wrapper">
                        <div class="icon-glow"></div>
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="user-content">
                        <span>طالب</span>
                        <div class="user-description">الطلاب والدارسين</div>
                    </div>
                </div>

                <div class="user-type-option" data-type="parent" onclick="selectUserType('parent')">
                    <div class="user-icon-wrapper">
                        <div class="icon-glow"></div>
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="user-content">
                        <span>ولي أمر</span>
                        <div class="user-description">أولياء الأمور</div>
                    </div>
                </div>

                <div class="user-type-option" data-type="school" onclick="selectUserType('school')">
                    <div class="user-icon-wrapper">
                        <div class="icon-glow"></div>
                        <i class="fas fa-school"></i>
                    </div>
                    <div class="user-content">
                        <span>مدرسة</span>
                        <div class="user-description">إدارة المدارس</div>
                    </div>
                </div>
            </div>

            <!-- نموذج التسجيل -->
            <form class="auth-form" id="registerForm">
                <!-- الصف الأول: الاسم الكامل واسم المستخدم -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="fullName">
                            <i class="fas fa-user"></i>
                            الاسم الكامل
                        </label>
                        <div class="input-wrapper">
                            <input type="text" id="fullName" name="fullName" placeholder="أدخل اسمك الكامل" required>
                            <i class="fas fa-user input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-at"></i>
                            اسم المستخدم
                        </label>
                        <div class="input-wrapper">
                            <input type="text" id="username" name="username" placeholder="اختر اسم مستخدم فريد" required>
                            <i class="fas fa-at input-icon"></i>
                        </div>
                    </div>
                </div>

                <!-- الصف الثاني: البريد الإلكتروني وكلمة المرور -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </label>
                        <div class="input-wrapper">
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                            <i class="fas fa-envelope input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="input-wrapper">
                            <input type="password" id="password" name="password" placeholder="أدخل كلمة مرور قوية" required>
                            <i class="fas fa-lock input-icon"></i>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn">
                    <i class="fas fa-user-plus"></i>
                    إنشاء الحساب
                </button>
            </form>

            <div class="auth-footer">
                <p>لديك حساب بالفعل؟ <a href="login.html">تسجيل الدخول</a></p>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // موضع عشوائي
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // متغير لتخزين نوع المستخدم المحدد
        let selectedUserType = 'student';

        // دالة اختيار نوع المستخدم
        function selectUserType(type) {
            selectedUserType = type;

            // إزالة active من جميع الخيارات
            document.querySelectorAll('.user-type-option').forEach(option => {
                option.classList.remove('active');
            });

            // إضافة active للخيار المحدد
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // تحديث النموذج حسب نوع المستخدم
            updateFormFields(type);
        }

        // دالة تحديث حقول النموذج
        function updateFormFields(type) {
            const form = document.getElementById('registerForm');

            // إزالة الحقول الإضافية إن وجدت
            const extraFields = form.querySelectorAll('.extra-field');
            extraFields.forEach(field => field.remove());

            // إضافة حقول خاصة حسب نوع المستخدم
            if (type === 'student') {
                addStudentFields(form);
            } else if (type === 'parent') {
                addParentFields(form);
            } else if (type === 'school') {
                addSchoolFields(form);
            }
        }

        // إضافة حقول الطالب
        function addStudentFields(form) {
            // إنشاء صف للحقول
            const rowDiv = document.createElement('div');
            rowDiv.className = 'form-row extra-field';

            const schoolField = createFormGroup('school', 'المدرسة', 'select', 'fas fa-school');
            const gradeField = createFormGroup('grade', 'الصف الدراسي', 'select', 'fas fa-graduation-cap');

            // إضافة خيارات المدارس
            const schoolSelect = schoolField.querySelector('select');
            schoolSelect.innerHTML = `
                <option value="">اختر المدرسة</option>
                <option value="1">مدرسة الأمل الابتدائية</option>
                <option value="2">مدرسة النور المتوسطة</option>
                <option value="3">مدرسة المستقبل الثانوية</option>
            `;

            // إضافة خيارات الصفوف
            const gradeSelect = gradeField.querySelector('select');
            gradeSelect.innerHTML = `
                <option value="">اختر الصف</option>
                <option value="1">الصف الأول</option>
                <option value="2">الصف الثاني</option>
                <option value="3">الصف الثالث</option>
                <option value="4">الصف الرابع</option>
                <option value="5">الصف الخامس</option>
                <option value="6">الصف السادس</option>
            `;

            // إضافة الحقول للصف
            rowDiv.appendChild(schoolField);
            rowDiv.appendChild(gradeField);

            const noorIdField = createFormGroup('noorId', 'رقم نور الطالب', 'text', 'fas fa-id-card');

            insertBeforeSubmit(form, [rowDiv, noorIdField]);
        }

        // إضافة حقول ولي الأمر
        function addParentFields(form) {
            // إنشاء صف للحقول
            const rowDiv = document.createElement('div');
            rowDiv.className = 'form-row extra-field';

            const phoneField = createFormGroup('phone', 'رقم الهاتف', 'tel', 'fas fa-phone');
            const residenceIdField = createFormGroup('residenceId', 'رقم الإقامة', 'text', 'fas fa-id-badge');

            // إضافة الحقول للصف
            rowDiv.appendChild(phoneField);
            rowDiv.appendChild(residenceIdField);

            // إضافة حقل رقم نور الطالب في صف منفصل مع تنبيه
            const studentNoorField = createFormGroup('studentNoorId', 'رقم نور الطالب (مطلوب)', 'text', 'fas fa-id-card');

            // إضافة تنبيه تحت الحقل
            const warningDiv = document.createElement('div');
            warningDiv.className = 'form-warning';
            warningDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <span>يجب أن يكون الطالب مسجلاً في النظام مسبقاً</span>
            `;
            studentNoorField.appendChild(warningDiv);

            insertBeforeSubmit(form, [rowDiv, studentNoorField]);
        }

        // إضافة حقول المدرسة
        function addSchoolFields(form) {
            // إنشاء صف للحقول
            const rowDiv = document.createElement('div');
            rowDiv.className = 'form-row extra-field';

            const schoolNameField = createFormGroup('schoolName', 'اسم المدرسة', 'text', 'fas fa-university');
            const phoneField = createFormGroup('phone', 'رقم الهاتف', 'tel', 'fas fa-phone');

            // إضافة الحقول للصف
            rowDiv.appendChild(schoolNameField);
            rowDiv.appendChild(phoneField);

            const addressField = createFormGroup('address', 'العنوان', 'text', 'fas fa-map-marker-alt');

            insertBeforeSubmit(form, [rowDiv, addressField]);
        }

        // دالة إنشاء مجموعة حقل مع أيقونات
        function createFormGroup(name, label, type, icon = 'fas fa-edit') {
            const div = document.createElement('div');
            div.className = 'form-group';

            if (type === 'select') {
                div.innerHTML = `
                    <label for="${name}">
                        <i class="${icon}"></i>
                        ${label}
                    </label>
                    <div class="input-wrapper">
                        <select id="${name}" name="${name}" required></select>
                        <i class="${icon} input-icon"></i>
                    </div>
                `;
            } else {
                const placeholder = getPlaceholder(name, type);
                div.innerHTML = `
                    <label for="${name}">
                        <i class="${icon}"></i>
                        ${label}
                    </label>
                    <div class="input-wrapper">
                        <input type="${type}" id="${name}" name="${name}" placeholder="${placeholder}" required>
                        <i class="${icon} input-icon"></i>
                    </div>
                `;
            }

            return div;
        }

        // دالة للحصول على placeholder مناسب
        function getPlaceholder(name, type) {
            const placeholders = {
                'school': 'اختر المدرسة',
                'grade': 'اختر الصف الدراسي',
                'noorId': 'أدخل رقم نور الطالب',
                'phone': 'أدخل رقم الهاتف',
                'residenceId': 'أدخل رقم الإقامة',
                'studentNoorId': 'أدخل رقم نور الطالب',
                'schoolName': 'أدخل اسم المدرسة',
                'address': 'أدخل العنوان'
            };
            return placeholders[name] || 'أدخل البيانات';
        }

        // دالة للحصول على نص الصف
        function getGradeText(grade) {
            const grades = {
                '1': 'الصف الأول',
                '2': 'الصف الثاني',
                '3': 'الصف الثالث',
                '4': 'الصف الرابع',
                '5': 'الصف الخامس',
                '6': 'الصف السادس',
                1: 'الصف الأول',
                2: 'الصف الثاني',
                3: 'الصف الثالث',
                4: 'الصف الرابع',
                5: 'الصف الخامس',
                6: 'الصف السادس'
            };
            return grades[grade] || `الصف ${grade}` || 'غير محدد';
        }

        // دالة إدراج الحقول قبل زر الإرسال
        function insertBeforeSubmit(form, fields) {
            const submitButton = form.querySelector('button[type="submit"]');
            fields.forEach(field => {
                form.insertBefore(field, submitButton);
            });
        }

        // نظام إدارة المستخدمين المتقدم - متوافق مع قاعدة البيانات الرئيسية
        class UserManager {
            constructor() {
                this.storageKey = 'cafeteriaDB';
            }

            // الحصول على قاعدة البيانات
            getDatabase() {
                try {
                    const db = localStorage.getItem(this.storageKey);
                    return db ? JSON.parse(db) : this.createDefaultDatabase();
                } catch (error) {
                    console.error('خطأ في قراءة قاعدة البيانات:', error);
                    return this.createDefaultDatabase();
                }
            }

            // إنشاء قاعدة بيانات افتراضية
            createDefaultDatabase() {
                const defaultDB = {
                    users: [],
                    schools: [],
                    products: [],
                    orders: [],
                    preorders: [],
                    notifications: [],
                    settings: {
                        themes: []
                    }
                };
                this.saveDatabase(defaultDB);
                return defaultDB;
            }

            // حفظ قاعدة البيانات
            saveDatabase(db) {
                localStorage.setItem(this.storageKey, JSON.stringify(db));
            }

            // حفظ مستخدم جديد
            saveUser(userData) {
                try {
                    console.log('بدء عملية حفظ المستخدم:', userData);

                    // التحقق من صحة البيانات الأساسية
                    if (!userData.username || !userData.password || !userData.fullName) {
                        throw new Error('البيانات الأساسية مفقودة (اسم المستخدم، كلمة المرور، الاسم الكامل)');
                    }

                    if (!userData.userType) {
                        throw new Error('نوع المستخدم غير محدد');
                    }

                    const db = this.getDatabase();
                    console.log('قاعدة البيانات الحالية:', db);

                    // التأكد من وجود مصفوفة المستخدمين
                    if (!db.users || !Array.isArray(db.users)) {
                        console.log('إنشاء مصفوفة مستخدمين جديدة');
                        db.users = [];
                    }

                    // التحقق من وجود اسم المستخدم أو البريد الإلكتروني
                    const existingUser = db.users.find(user =>
                        user.username === userData.username ||
                        (userData.email && user.email === userData.email)
                    );

                    if (existingUser) {
                        throw new Error('اسم المستخدم أو البريد الإلكتروني موجود بالفعل');
                    }

                    // إنشاء معرف فريد
                    const newId = db.users.length > 0 ? Math.max(...db.users.map(u => u.id || 0), 0) + 1 : 1;

                    // إنشاء المستخدم الجديد بالتنسيق المتوافق
                    const newUser = {
                        id: newId,
                        username: userData.username,
                        password: userData.password, // كلمة مرور عادية (حسب تفضيل المستخدم)
                        role: userData.userType, // تحويل userType إلى role
                        name: userData.fullName,
                        email: userData.email || '',
                        phone: userData.phone || '',
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        // إضافة حقول خاصة حسب نوع المستخدم
                        ...(userData.userType === 'student' && {
                            schoolId: userData.school || 1,
                            grade: userData.grade || '',
                            noorId: userData.noorId || '',
                            balance: 0,
                            allergies: []
                        }),
                        ...(userData.userType === 'parent' && {
                            studentNoorId: userData.studentNoorId || ''
                        }),
                        ...(userData.userType === 'school' && {
                            schoolName: userData.schoolName || '',
                            address: userData.address || ''
                        }),
                        ...(userData.userType === 'staff' && {
                            schoolId: userData.school || 1,
                            staffType: 'cashier',
                            permissions: ['sell', 'view_products', 'view_students']
                        })
                    };

                    console.log('المستخدم الجديد قبل الحفظ:', newUser);

                    // إضافة المستخدم إلى قاعدة البيانات
                    db.users.push(newUser);
                    console.log('تم إضافة المستخدم إلى المصفوفة، العدد الحالي:', db.users.length);

                    // حفظ قاعدة البيانات
                    this.saveDatabase(db);
                    console.log('تم حفظ قاعدة البيانات');

                    // التحقق من الحفظ
                    const savedDb = this.getDatabase();
                    const savedUser = savedDb.users.find(u => u.id === newUser.id);

                    if (!savedUser) {
                        throw new Error('فشل في التحقق من حفظ المستخدم في قاعدة البيانات');
                    }

                    console.log('تم إنشاء المستخدم بنجاح:', newUser);
                    console.log('تم التحقق من الحفظ بنجاح');
                    return { success: true, user: newUser };
                } catch (error) {
                    console.error('خطأ في حفظ المستخدم:', error);
                    console.error('تفاصيل الخطأ:', error.stack);
                    return { success: false, error: error.message };
                }
            }

            // البحث عن مستخدم
            findUser(username, password, userType) {
                const db = this.getDatabase();
                return db.users.find(user =>
                    (user.username === username || user.email === username) &&
                    user.password === password &&
                    user.role === userType &&
                    user.status === 'active'
                );
            }

            // الحصول على جميع المستخدمين
            getAllUsers() {
                const db = this.getDatabase();
                return db.users || [];
            }

            // حفظ المستخدم الحالي (متوافق مع نظام تسجيل الدخول)
            setCurrentUser(user) {
                const userSession = {
                    id: user.id,
                    username: user.username,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    schoolId: user.schoolId,
                    loginTime: new Date().toISOString()
                };
                sessionStorage.setItem('currentUser', JSON.stringify(userSession));
                localStorage.setItem('currentUserRole', user.role);
                localStorage.setItem('currentUserId', user.id.toString());
            }
        }

        // إنشاء مثيل من مدير المستخدمين
        const userManager = new UserManager();

        // معالج إرسال النموذج المبسط والمؤكد
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            if (!form) {
                console.error('لم يتم العثور على نموذج التسجيل');
                return;
            }

            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('تم إرسال النموذج');

                // الحصول على الزر
                const submitBtn = this.querySelector('button[type="submit"]');
                if (!submitBtn) {
                    console.error('لم يتم العثور على زر الإرسال');
                    return;
                }

                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
                submitBtn.disabled = true;

                // جمع البيانات بطريقة مباشرة
                const userData = {
                    username: document.getElementById('username')?.value || '',
                    password: document.getElementById('password')?.value || '',
                    fullName: document.getElementById('fullName')?.value || '',
                    email: document.getElementById('email')?.value || '',
                    userType: selectedUserType || 'student',
                    // جمع البيانات الإضافية حسب نوع المستخدم
                    noorId: document.getElementById('noorId')?.value || '',
                    grade: document.getElementById('grade')?.value || '',
                    school: document.getElementById('school')?.value || '',
                    studentNoorId: document.getElementById('studentNoorId')?.value || '',
                    residenceId: document.getElementById('residenceId')?.value || '',
                    schoolName: document.getElementById('schoolName')?.value || '',
                    address: document.getElementById('address')?.value || '',
                    phone: document.getElementById('phone')?.value || ''
                };

                console.log('بيانات المستخدم المجمعة:', userData);

                // التحقق من البيانات الأساسية
                if (!userData.username.trim()) {
                    alert('يرجى إدخال اسم المستخدم');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                if (!userData.password.trim()) {
                    alert('يرجى إدخال كلمة المرور');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                if (!userData.fullName.trim()) {
                    alert('يرجى إدخال الاسم الكامل');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                // التحقق من البيانات الخاصة بكل نوع مستخدم
                if (userData.userType === 'student' && !userData.noorId.trim()) {
                    alert('يرجى إدخال رقم نور الطالب');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                if (userData.userType === 'parent' && !userData.studentNoorId.trim()) {
                    alert('يرجى إدخال رقم نور الطالب المرتبط بولي الأمر');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                if (userData.userType === 'school' && !userData.schoolName.trim()) {
                    alert('يرجى إدخال اسم المدرسة');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                // محاولة حفظ المستخدم بطريقة مباشرة
                try {
                    console.log('بدء عملية الحفظ المباشر...');

                    // الحصول على قاعدة البيانات
                    let db;
                    try {
                        db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                    } catch {
                        db = {};
                    }

                    // التأكد من وجود مصفوفة المستخدمين
                    if (!db.users || !Array.isArray(db.users)) {
                        db.users = [];
                    }

                    // التحقق من عدم وجود اسم المستخدم
                    const existingUser = db.users.find(u => u.username === userData.username);
                    if (existingUser) {
                        alert('اسم المستخدم موجود بالفعل');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        return;
                    }

                    // التحقق من عدم وجود البريد الإلكتروني (إذا تم إدخاله)
                    if (userData.email.trim()) {
                        const existingEmail = db.users.find(u => u.email === userData.email.trim());
                        if (existingEmail) {
                            alert('البريد الإلكتروني موجود بالفعل');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }
                    }

                    // التحقق من تفرد رقم نور للطلاب
                    if (userData.userType === 'student' && userData.noorId.trim()) {
                        const existingNoorId = db.users.find(u =>
                            u.role === 'student' && u.noorId === userData.noorId.trim()
                        );
                        if (existingNoorId) {
                            alert(`رقم نور ${userData.noorId} موجود بالفعل للطالب: ${existingNoorId.name}`);
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }
                    }

                    // التحقق من وجود الطالب المرتبط بولي الأمر (إجباري)
                    if (userData.userType === 'parent') {
                        if (!userData.studentNoorId || !userData.studentNoorId.trim()) {
                            alert('يجب إدخال رقم نور الطالب لإتمام تسجيل ولي الأمر');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }

                        const relatedStudent = db.users.find(u =>
                            u.role === 'student' && u.noorId === userData.studentNoorId.trim()
                        );
                        if (!relatedStudent) {
                            alert(`لم يتم العثور على طالب برقم نور: ${userData.studentNoorId}\nيجب أن يكون الطالب مسجلاً في النظام أولاً`);
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }

                        // التحقق من عدم وجود ولي أمر آخر لنفس الطالب
                        const existingParent = db.users.find(u =>
                            u.role === 'parent' && u.studentNoorId === userData.studentNoorId.trim()
                        );
                        if (existingParent) {
                            alert(`يوجد ولي أمر آخر مسجل للطالب "${relatedStudent.name}" برقم نور: ${userData.studentNoorId}\nلا يمكن تسجيل أكثر من ولي أمر واحد للطالب نفسه`);
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }

                        // عرض معلومات الطالب للتأكيد
                        const confirmMessage = `هل أنت متأكد من أنك ولي أمر الطالب:\nالاسم: ${relatedStudent.name}\nرقم نور: ${relatedStudent.noorId}\nالصف: ${getGradeText(relatedStudent.grade)}`;
                        if (!confirm(confirmMessage)) {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }
                    }

                    // إنشاء المستخدم الجديد
                    const newId = db.users.length > 0 ? Math.max(...db.users.map(u => u.id || 0)) + 1 : 1;
                    const newUser = {
                        id: newId,
                        username: userData.username.trim(),
                        password: userData.password.trim(),
                        role: userData.userType,
                        name: userData.fullName.trim(),
                        email: userData.email.trim(),
                        phone: userData.phone.trim(),
                        status: 'active',
                        createdAt: new Date().toISOString()
                    };

                    // إضافة البيانات الخاصة حسب نوع المستخدم
                    if (userData.userType === 'student') {
                        newUser.noorId = userData.noorId.trim();
                        newUser.grade = userData.grade.trim();
                        newUser.schoolId = userData.school ? parseInt(userData.school) : 1;
                        newUser.balance = 0;
                        newUser.allergies = [];
                    } else if (userData.userType === 'parent') {
                        newUser.studentNoorId = userData.studentNoorId.trim();
                        newUser.residenceId = userData.residenceId.trim();
                        // ربط ولي الأمر بالطالب
                        const relatedStudent = db.users.find(u =>
                            u.role === 'student' && u.noorId === userData.studentNoorId.trim()
                        );
                        if (relatedStudent) {
                            newUser.studentId = relatedStudent.id;
                            newUser.studentName = relatedStudent.name;

                            // ربط الطالب بولي الأمر أيضاً
                            relatedStudent.parentId = newUser.id;
                            relatedStudent.parentName = newUser.name;
                            relatedStudent.parentNoorId = newUser.residenceId;
                        }
                    } else if (userData.userType === 'school') {
                        newUser.schoolName = userData.schoolName.trim();
                        newUser.address = userData.address.trim();
                    } else if (userData.userType === 'staff') {
                        newUser.schoolId = userData.school ? parseInt(userData.school) : 1;
                        newUser.staffType = 'cashier';
                        newUser.permissions = ['sell', 'view_products', 'view_students'];
                    }

                    console.log('المستخدم الجديد:', newUser);

                    // إضافة المستخدم وحفظ قاعدة البيانات
                    db.users.push(newUser);
                    localStorage.setItem('cafeteriaDB', JSON.stringify(db));

                    console.log('تم حفظ المستخدم بنجاح');

                    // عرض رسالة نجاح مفصلة
                    let successMessage = `تم إنشاء الحساب بنجاح!\nاسم المستخدم: ${newUser.username}\nالنوع: ${newUser.role}`;

                    if (newUser.noorId) {
                        successMessage += `\nرقم نور: ${newUser.noorId}`;
                    }

                    if (newUser.studentNoorId) {
                        successMessage += `\nرقم نور الطالب: ${newUser.studentNoorId}`;
                        if (newUser.studentName) {
                            successMessage += `\nاسم الطالب: ${newUser.studentName}`;
                        }
                    }

                    alert(successMessage);

                    // إعادة تعيين النموذج
                    form.reset();
                    selectedUserType = 'student';

                    // إعادة تفعيل الزر
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;

                    // التوجيه لصفحة تسجيل الدخول
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);

                } catch (error) {
                    console.error('خطأ في إنشاء الحساب:', error);
                    alert('حدث خطأ أثناء إنشاء الحساب: ' + error.message);

                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        });

        // تحميل الصفحة وتهيئة النموذج
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء الجسيمات
            createParticles();

            // تحديث النموذج للنوع الافتراضي
            updateFormFields('student');
        });
    </script>
</body>
</html>
