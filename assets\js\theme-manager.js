/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * مدير السمات والتخصيصات
 *
 * هذا الملف مسؤول عن إدارة سمات النظام وتخصيصات واجهة المستخدم
 * بما في ذلك الألوان والخطوط والتأثيرات الحركية والوضع الليلي/النهاري
 */

// كائن مدير السمات
const ThemeManager = {
    // الإعدادات الافتراضية
    defaultSettings: {
        // الألوان الأساسية
        colors: {
            primary: '#2e7d32',
            secondary: '#f9a825',
            dark: '#1b5e20',
            light: '#e8f5e9',
            text: '#333333',
            background: '#ffffff',
            surface: '#f5f5f5'
        },
        // الخطوط
        fonts: {
            main: 'Tajawal',
            size: 'medium', // small, medium, large
            weight: 'normal' // light, normal, bold
        },
        // التأثيرات الحركية
        animations: {
            speed: 'normal', // slow, normal, fast
            enabled: true
        },
        // الوضع الليلي
        darkMode: {
            enabled: false,
            auto: false, // تفعيل تلقائي حسب وقت الجهاز
            startTime: '19:00', // وقت بدء الوضع الليلي (للوضع التلقائي)
            endTime: '06:00' // وقت انتهاء الوضع الليلي (للوضع التلقائي)
        }
    },

    // الإعدادات الحالية
    currentSettings: {},

    // تهيئة مدير السمات
    init() {
        console.log('بدء تهيئة مدير السمات...');

        try {
            // تحميل الإعدادات من التخزين المحلي أو استخدام الإعدادات الافتراضية
            this.loadSettings();
            console.log('تم تحميل الإعدادات بنجاح');

            // تطبيق الإعدادات الحالية
            this.applySettings();
            console.log('تم تطبيق الإعدادات بنجاح');

            // إعداد مستمعي الأحداث
            this.setupEventListeners();
            console.log('تم إعداد مستمعي الأحداث بنجاح');

            // التحقق من الوضع التلقائي للوضع الليلي
            if (this.currentSettings.darkMode && this.currentSettings.darkMode.auto) {
                this.checkAutoDarkMode();
                console.log('تم التحقق من الوضع التلقائي للوضع الليلي');
            }

            // إضافة زر تبديل الوضع الليلي/النهاري إلى الشريط العلوي
            setTimeout(() => {
                this.addDarkModeToggle();
                console.log('تم إضافة زر تبديل الوضع الليلي/النهاري');
            }, 500); // تأخير إضافة الزر لضمان تحميل الشريط العلوي

            console.log('تم تهيئة مدير السمات بنجاح');
            return true;
        } catch (error) {
            console.error('حدث خطأ أثناء تهيئة مدير السمات:', error);

            // محاولة استخدام الإعدادات الافتراضية في حالة حدوث خطأ
            try {
                this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
                this.applySettings();
                console.log('تم استخدام الإعدادات الافتراضية بسبب حدوث خطأ');
            } catch (fallbackError) {
                console.error('فشل في استخدام الإعدادات الافتراضية:', fallbackError);
            }

            return false;
        }
    },

    // تحميل الإعدادات من التخزين المحلي
    loadSettings() {
        try {
            // محاولة تحميل الإعدادات من التخزين المحلي
            const savedSettings = localStorage.getItem('themeSettings');

            if (savedSettings) {
                this.currentSettings = JSON.parse(savedSettings);
                console.log('تم تحميل إعدادات السمة من التخزين المحلي');
            } else {
                // محاولة استخدام الإعدادات من قاعدة البيانات
                const db = window.getDatabase ? window.getDatabase() : null;

                if (db && db.settings && db.settings.customization) {
                    console.log('تم تحميل إعدادات السمة من قاعدة البيانات');
                    this.currentSettings = JSON.parse(JSON.stringify(db.settings.customization));
                } else {
                    // استخدام الإعدادات الافتراضية إذا لم يتم العثور على إعدادات محفوظة
                    this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
                    console.log('تم استخدام إعدادات السمة الافتراضية');
                }

                // حفظ الإعدادات في التخزين المحلي
                this.saveSettings();
            }
        } catch (error) {
            console.error('خطأ في تحميل إعدادات السمة:', error);
            // استخدام الإعدادات الافتراضية في حالة حدوث خطأ
            this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
        }
    },

    // حفظ الإعدادات في التخزين المحلي
    saveSettings() {
        try {
            // حفظ الإعدادات في التخزين المحلي
            localStorage.setItem('themeSettings', JSON.stringify(this.currentSettings));

            // تحديث الإعدادات في قاعدة البيانات إذا كانت متاحة
            if (window.getDatabase && window.saveDatabase) {
                const db = window.getDatabase();
                if (db && db.settings) {
                    db.settings.customization = JSON.parse(JSON.stringify(this.currentSettings));
                    window.saveDatabase(db);
                    console.log('تم حفظ إعدادات السمة في قاعدة البيانات');
                }
            }

            console.log('تم حفظ إعدادات السمة في التخزين المحلي');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ إعدادات السمة:', error);
            return false;
        }
    },

    // تطبيق الإعدادات الحالية على الصفحة
    applySettings() {
        // تطبيق الألوان
        this.applyColors();

        // تطبيق الخطوط
        this.applyFonts();

        // تطبيق التأثيرات الحركية
        this.applyAnimations();

        // تطبيق الوضع الليلي/النهاري
        this.applyDarkMode();

        console.log('تم تطبيق إعدادات السمة بنجاح');
    },

    // تطبيق إعدادات الألوان
    applyColors() {
        try {
            // التحقق من وجود إعدادات الألوان
            if (!this.currentSettings || !this.currentSettings.colors) {
                console.error('إعدادات الألوان غير متوفرة');
                return;
            }

            const colors = this.currentSettings.colors;

            // التحقق من وجود جميع الألوان المطلوبة
            if (!colors.primary || !colors.secondary || !colors.dark ||
                !colors.light || !colors.text || !colors.background) {
                console.error('بعض الألوان غير متوفرة في الإعدادات');
                return;
            }

            // تعيين متغيرات CSS للألوان
            document.documentElement.style.setProperty('--primary-color', colors.primary);
            document.documentElement.style.setProperty('--secondary-color', colors.secondary);
            document.documentElement.style.setProperty('--dark-color', colors.dark);
            document.documentElement.style.setProperty('--light-color', colors.light);
            document.documentElement.style.setProperty('--text-color', colors.text);
            document.documentElement.style.setProperty('--background-color', colors.background);
            document.documentElement.style.setProperty('--surface-color', colors.surface || colors.background);

            console.log('تم تطبيق إعدادات الألوان بنجاح');
        } catch (error) {
            console.error('حدث خطأ أثناء تطبيق إعدادات الألوان:', error);
        }
    },

    // تطبيق إعدادات الخطوط
    applyFonts() {
        try {
            // التحقق من وجود إعدادات الخطوط
            if (!this.currentSettings || !this.currentSettings.fonts) {
                console.error('إعدادات الخطوط غير متوفرة');
                return;
            }

            const fonts = this.currentSettings.fonts;

            // التحقق من وجود جميع إعدادات الخطوط المطلوبة
            if (!fonts.main) {
                console.error('نوع الخط الرئيسي غير متوفر في الإعدادات');
                return;
            }

            // تعيين متغيرات CSS للخطوط
            document.documentElement.style.setProperty('--main-font', fonts.main);

            // تطبيق حجم الخط
            let fontSize = '16px'; // الحجم الافتراضي (متوسط)

            if (fonts.size) {
                switch (fonts.size) {
                    case 'small':
                        fontSize = '14px';
                        break;
                    case 'large':
                        fontSize = '18px';
                        break;
                    default:
                        fontSize = '16px'; // متوسط
                }
            }

            document.documentElement.style.setProperty('--base-font-size', fontSize);

            // تطبيق وزن الخط
            let fontWeight = '400'; // الوزن الافتراضي (عادي)

            if (fonts.weight) {
                switch (fonts.weight) {
                    case 'light':
                        fontWeight = '300';
                        break;
                    case 'bold':
                        fontWeight = '700';
                        break;
                    default:
                        fontWeight = '400'; // عادي
                }
            }

            document.documentElement.style.setProperty('--base-font-weight', fontWeight);

            // تطبيق الخط على عنصر body
            document.body.style.fontFamily = fonts.main + ', Tajawal, sans-serif';
            document.body.style.fontSize = fontSize;
            document.body.style.fontWeight = fontWeight;

            console.log('تم تطبيق إعدادات الخطوط بنجاح');
        } catch (error) {
            console.error('حدث خطأ أثناء تطبيق إعدادات الخطوط:', error);
        }
    },

    // تطبيق إعدادات التأثيرات الحركية
    applyAnimations() {
        try {
            // التحقق من وجود إعدادات التأثيرات الحركية
            if (!this.currentSettings || !this.currentSettings.animations) {
                console.error('إعدادات التأثيرات الحركية غير متوفرة');
                return;
            }

            const animations = this.currentSettings.animations;

            // تعيين متغيرات CSS للتأثيرات الحركية
            let transitionSpeed = '0.3s'; // السرعة الافتراضية (عادية)

            if (animations.speed) {
                switch (animations.speed) {
                    case 'slow':
                        transitionSpeed = '0.5s';
                        break;
                    case 'fast':
                        transitionSpeed = '0.2s';
                        break;
                    default:
                        transitionSpeed = '0.3s'; // عادية
                }
            }

            document.documentElement.style.setProperty('--transition-speed', transitionSpeed);
            document.documentElement.style.setProperty('--animation-speed', animations.speed === 'slow' ? '1.5' :
                                                                          animations.speed === 'fast' ? '0.7' : '1');

            // تفعيل/تعطيل التأثيرات الحركية
            if (animations.enabled !== undefined) {
                if (animations.enabled) {
                    document.body.classList.remove('no-animations');
                } else {
                    document.body.classList.add('no-animations');
                }
            } else {
                // الافتراضي: تفعيل التأثيرات الحركية
                document.body.classList.remove('no-animations');
            }

            console.log('تم تطبيق إعدادات التأثيرات الحركية بنجاح');
        } catch (error) {
            console.error('حدث خطأ أثناء تطبيق إعدادات التأثيرات الحركية:', error);
        }
    },

    // تطبيق إعدادات الوضع الليلي/النهاري
    applyDarkMode() {
        try {
            // التحقق من وجود إعدادات الوضع الليلي/النهاري
            if (!this.currentSettings || !this.currentSettings.darkMode) {
                console.error('إعدادات الوضع الليلي/النهاري غير متوفرة');
                return;
            }

            const darkMode = this.currentSettings.darkMode;

            // تطبيق الوضع الليلي/النهاري
            if (darkMode.enabled) {
                document.body.classList.add('dark-mode');
                document.documentElement.setAttribute('data-theme', 'dark');
                this.updateDarkModeToggle(true);
            } else {
                document.body.classList.remove('dark-mode');
                document.documentElement.setAttribute('data-theme', 'light');
                this.updateDarkModeToggle(false);
            }

            // تحديث لون الشريط العلوي في المتصفح (إذا كان مدعوماً)
            const metaThemeColor = document.querySelector('meta[name="theme-color"]');
            if (metaThemeColor) {
                metaThemeColor.setAttribute('content',
                    darkMode.enabled ?
                    this.currentSettings.colors.dark :
                    this.currentSettings.colors.primary
                );
            }

            console.log('تم تطبيق إعدادات الوضع الليلي/النهاري بنجاح');
        } catch (error) {
            console.error('حدث خطأ أثناء تطبيق إعدادات الوضع الليلي/النهاري:', error);
        }
    },

    // تبديل الوضع الليلي/النهاري
    toggleDarkMode() {
        this.currentSettings.darkMode.enabled = !this.currentSettings.darkMode.enabled;
        this.applyDarkMode();
        this.saveSettings();

        // عرض رسالة تأكيد
        console.log(`تم ${this.currentSettings.darkMode.enabled ? 'تفعيل' : 'تعطيل'} الوضع الليلي`);

        // إظهار رسالة للمستخدم
        const message = document.createElement('div');
        message.className = 'theme-message';
        message.textContent = `تم ${this.currentSettings.darkMode.enabled ? 'تفعيل' : 'تعطيل'} الوضع الليلي`;
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.right = '20px';
        message.style.padding = '10px 20px';
        message.style.backgroundColor = this.currentSettings.darkMode.enabled ? '#333' : '#f5f5f5';
        message.style.color = this.currentSettings.darkMode.enabled ? '#fff' : '#333';
        message.style.borderRadius = '5px';
        message.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '9999';
        message.style.opacity = '0';
        message.style.transition = 'opacity 0.3s ease';

        document.body.appendChild(message);

        // إظهار الرسالة
        setTimeout(() => {
            message.style.opacity = '1';
        }, 10);

        // إخفاء الرسالة بعد 3 ثوانٍ
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(message);
            }, 300);
        }, 3000);
    },

    // التحقق من الوضع التلقائي للوضع الليلي
    checkAutoDarkMode() {
        if (!this.currentSettings.darkMode.auto) return;

        const now = new Date();
        const currentHour = now.getHours();
        const currentMinutes = now.getMinutes();
        const currentTime = `${currentHour}:${currentMinutes}`;

        const startTime = this.currentSettings.darkMode.startTime.split(':');
        const endTime = this.currentSettings.darkMode.endTime.split(':');

        const startHour = parseInt(startTime[0]);
        const startMinutes = parseInt(startTime[1]);
        const endHour = parseInt(endTime[0]);
        const endMinutes = parseInt(endTime[1]);

        // التحقق مما إذا كان الوقت الحالي ضمن نطاق الوضع الليلي
        let shouldEnableDarkMode = false;

        if (startHour > endHour) {
            // إذا كان وقت البدء بعد وقت الانتهاء (مثل 19:00 إلى 06:00)
            shouldEnableDarkMode = (currentHour > startHour || (currentHour === startHour && currentMinutes >= startMinutes)) ||
                                  (currentHour < endHour || (currentHour === endHour && currentMinutes <= endMinutes));
        } else {
            // إذا كان وقت البدء قبل وقت الانتهاء (مثل 19:00 إلى 23:00)
            shouldEnableDarkMode = (currentHour > startHour || (currentHour === startHour && currentMinutes >= startMinutes)) &&
                                  (currentHour < endHour || (currentHour === endHour && currentMinutes <= endMinutes));
        }

        // تطبيق الوضع الليلي إذا كان ضمن النطاق الزمني
        if (shouldEnableDarkMode !== this.currentSettings.darkMode.enabled) {
            this.currentSettings.darkMode.enabled = shouldEnableDarkMode;
            this.applyDarkMode();
        }
    },

    // إضافة زر تبديل الوضع الليلي/النهاري إلى الشريط العلوي
    addDarkModeToggle() {
        // التحقق من وجود الشريط العلوي
        const header = document.querySelector('.header .container');
        if (!header) {
            console.warn('لم يتم العثور على الشريط العلوي لإضافة زر الوضع الليلي/النهاري');
            return;
        }

        // التحقق من عدم وجود الزر مسبقًا
        if (document.getElementById('dark-mode-toggle')) {
            console.log('زر الوضع الليلي/النهاري موجود بالفعل');
            return;
        }

        // إنشاء زر التبديل
        const toggleButton = document.createElement('button');
        toggleButton.id = 'dark-mode-toggle';
        toggleButton.className = 'dark-mode-toggle';
        toggleButton.setAttribute('aria-label', 'تبديل الوضع الليلي/النهاري');
        toggleButton.innerHTML = this.currentSettings.darkMode.enabled ?
            '<i class="fas fa-sun"></i>' :
            '<i class="fas fa-moon"></i>';

        // إضافة حدث النقر
        toggleButton.addEventListener('click', () => {
            this.toggleDarkMode();
        });

        // إضافة الزر إلى الشريط العلوي
        // إضافة الزر قبل عنصر user-menu إذا كان موجوداً، أو في نهاية الشريط العلوي
        const userMenu = header.querySelector('.user-menu');
        if (userMenu) {
            header.insertBefore(toggleButton, userMenu);
        } else {
            header.appendChild(toggleButton);
        }

        console.log('تم إضافة زر الوضع الليلي/النهاري بنجاح');
    },

    // تحديث زر تبديل الوضع الليلي/النهاري
    updateDarkModeToggle(isDarkMode) {
        const toggleButton = document.getElementById('dark-mode-toggle');
        if (!toggleButton) return;

        toggleButton.innerHTML = isDarkMode ?
            '<i class="fas fa-sun"></i>' :
            '<i class="fas fa-moon"></i>';
    },

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // التحقق من الوضع التلقائي للوضع الليلي كل دقيقة
        if (this.currentSettings.darkMode.auto) {
            setInterval(() => {
                this.checkAutoDarkMode();
            }, 60000); // كل دقيقة
        }
    }
};

// تصدير الكائن للاستخدام العام
window.ThemeManager = ThemeManager;

// تهيئة مدير السمات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تأخير التهيئة قليلاً لضمان تحميل جميع الموارد
    setTimeout(() => {
        try {
            console.log('جاري تهيئة مدير السمات...');
            ThemeManager.init();
            console.log('تم تهيئة مدير السمات بنجاح');
        } catch (error) {
            console.error('حدث خطأ أثناء تهيئة مدير السمات:', error);
        }
    }, 100);
});
