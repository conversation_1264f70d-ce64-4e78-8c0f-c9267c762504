<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تدفق تسجيل دخول العاملين - نظام المقصف المدرسي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2e7d32;
        }
        .test-results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            color: #4caf50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .warning {
            color: #ff9800;
            font-weight: bold;
        }
        .info {
            color: #2196f3;
            font-weight: bold;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #2e7d32;
            background: white;
            border-radius: 5px;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a, .test-btn {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .nav-links a:hover, .test-btn:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        .test-btn.secondary {
            background: #1976d2;
        }
        .test-btn.secondary:hover {
            background: #1565c0;
        }
        .test-btn.danger {
            background: #f44336;
        }
        .test-btn.danger:hover {
            background: #d32f2f;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2e7d32;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #2e7d32;
            box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 0;
            list-style: none;
        }
        .step-indicator li {
            flex: 1;
            text-align: center;
            position: relative;
            padding: 10px;
            background: #e0e0e0;
            color: #666;
            border-radius: 5px;
            margin: 0 5px;
        }
        .step-indicator li.active {
            background: #2e7d32;
            color: white;
        }
        .step-indicator li.completed {
            background: #4caf50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 اختبار تدفق تسجيل دخول العاملين</h1>
            <p>اختبار شامل لعملية تسجيل دخول العاملين والتوجيه إلى نقطة البيع</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="../pages/staff/login.html">🔑 صفحة دخول العاملين</a>
            <a href="../pages/staff/pos-simple.html">🛒 نقطة البيع</a>
            <a href="staff-management.html">👥 إدارة العاملين</a>
        </div>

        <!-- مؤشر الخطوات -->
        <ul class="step-indicator">
            <li id="step1" class="active">1. فحص النظام</li>
            <li id="step2">2. إنشاء حساب تجريبي</li>
            <li id="step3">3. اختبار تسجيل الدخول</li>
            <li id="step4">4. التحقق من التوجيه</li>
        </ul>

        <!-- نتائج الاختبار -->
        <div class="test-results" id="test-results">
            <h2>نتائج الاختبار</h2>
            <div id="test-output">
                <div class="info">جاري تشغيل الاختبارات...</div>
            </div>
        </div>

        <!-- قسم إنشاء حساب تجريبي -->
        <div class="test-section">
            <h2>إنشاء حساب عامل تجريبي</h2>
            <div class="form-group">
                <label for="test-username">اسم المستخدم:</label>
                <input type="text" id="test-username" value="test_staff" placeholder="أدخل اسم المستخدم">
            </div>
            <div class="form-group">
                <label for="test-password">كلمة المرور:</label>
                <input type="password" id="test-password" value="123456" placeholder="أدخل كلمة المرور">
            </div>
            <div class="form-group">
                <label for="test-name">الاسم الكامل:</label>
                <input type="text" id="test-name" value="عامل تجريبي" placeholder="أدخل الاسم الكامل">
            </div>
            <button class="test-btn" onclick="createTestStaff()">إنشاء حساب تجريبي</button>
            <button class="test-btn secondary" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <button class="test-btn danger" onclick="clearTestData()">مسح البيانات التجريبية</button>
        </div>

        <!-- قسم اختبار التدفق -->
        <div class="test-section">
            <h2>اختبار التدفق الكامل</h2>
            <button class="test-btn" onclick="runFullFlowTest()">تشغيل الاختبار الكامل</button>
            <button class="test-btn secondary" onclick="checkCurrentUser()">فحص المستخدم الحالي</button>
            <button class="test-btn secondary" onclick="simulateLogin()">محاكاة تسجيل الدخول</button>
        </div>

        <!-- قسم التشخيص -->
        <div class="test-section">
            <h2>أدوات التشخيص</h2>
            <button class="test-btn secondary" onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <button class="test-btn secondary" onclick="checkStorage()">فحص التخزين</button>
            <button class="test-btn secondary" onclick="checkRedirection()">فحص آلية التوجيه</button>
            <div id="diagnostic-results" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentStep = 1;

        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            runInitialTests();
        });

        function updateStep(step) {
            // إزالة الفئات من جميع الخطوات
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
            currentStep = step;
        }

        function runInitialTests() {
            const output = document.getElementById('test-output');
            testResults = [];

            // اختبار 1: فحص وجود قاعدة البيانات
            try {
                const db = getDatabase();
                if (db && db.users) {
                    testResults.push('<div class="test-item success">✓ قاعدة البيانات متوفرة</div>');
                    testResults.push(`<div class="test-item info">📊 عدد المستخدمين: ${db.users.length}</div>`);
                } else {
                    testResults.push('<div class="test-item error">✗ قاعدة البيانات غير متوفرة</div>');
                }
            } catch (error) {
                testResults.push('<div class="test-item error">✗ خطأ في قاعدة البيانات</div>');
            }

            // اختبار 2: فحص صفحة تسجيل الدخول
            fetch('../pages/staff/login.html')
                .then(response => {
                    if (response.ok) {
                        testResults.push('<div class="test-item success">✓ صفحة تسجيل دخول العاملين متوفرة</div>');
                    } else {
                        testResults.push('<div class="test-item error">✗ صفحة تسجيل دخول العاملين غير متوفرة</div>');
                    }
                    updateResults();
                })
                .catch(error => {
                    testResults.push('<div class="test-item error">✗ خطأ في الوصول لصفحة تسجيل الدخول</div>');
                    updateResults();
                });

            // اختبار 3: فحص صفحة نقطة البيع
            fetch('../pages/staff/pos-simple.html')
                .then(response => {
                    if (response.ok) {
                        testResults.push('<div class="test-item success">✓ صفحة نقطة البيع متوفرة</div>');
                    } else {
                        testResults.push('<div class="test-item error">✗ صفحة نقطة البيع غير متوفرة</div>');
                    }
                    updateResults();
                })
                .catch(error => {
                    testResults.push('<div class="test-item error">✗ خطأ في الوصول لصفحة نقطة البيع</div>');
                    updateResults();
                });

            // اختبار 4: فحص التخزين المحلي
            try {
                localStorage.setItem('test', 'test');
                sessionStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                sessionStorage.removeItem('test');
                testResults.push('<div class="test-item success">✓ التخزين المحلي يعمل</div>');
            } catch (error) {
                testResults.push('<div class="test-item error">✗ التخزين المحلي لا يعمل</div>');
            }

            updateResults();
            updateStep(1);
        }

        function updateResults() {
            const output = document.getElementById('test-output');
            output.innerHTML = testResults.join('');
        }

        function getDatabase() {
            try {
                const data = localStorage.getItem('cafeteriaDB');
                return data ? JSON.parse(data) : null;
            } catch (error) {
                return null;
            }
        }

        function createTestStaff() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            const name = document.getElementById('test-name').value;

            if (!username || !password || !name) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            try {
                let db = getDatabase();
                if (!db) {
                    db = { users: [], products: [], transactions: [] };
                }

                // التحقق من وجود المستخدم
                const existingUser = db.users.find(u => u.username === username);
                if (existingUser) {
                    alert('اسم المستخدم موجود بالفعل');
                    return;
                }

                // إنشاء المستخدم الجديد
                const newUser = {
                    id: Date.now(),
                    username: username,
                    password: password,
                    name: name,
                    role: 'staff',
                    status: 'active',
                    permissions: ['sell', 'view_products'],
                    createdAt: new Date().toISOString()
                };

                db.users.push(newUser);
                localStorage.setItem('cafeteriaDB', JSON.stringify(db));

                testResults.push(`<div class="test-item success">✓ تم إنشاء حساب العامل: ${name}</div>`);
                updateResults();
                updateStep(2);

                alert('تم إنشاء الحساب التجريبي بنجاح!');
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                alert('حدث خطأ في إنشاء الحساب');
            }
        }

        function testLogin() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;

            if (!username || !password) {
                alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            // محاكاة عملية تسجيل الدخول
            try {
                const db = getDatabase();
                if (!db || !db.users) {
                    alert('قاعدة البيانات غير متوفرة');
                    return;
                }

                const user = db.users.find(u => u.username === username && u.role === 'staff');
                if (!user) {
                    alert('المستخدم غير موجود');
                    return;
                }

                if (user.password !== password) {
                    alert('كلمة المرور غير صحيحة');
                    return;
                }

                if (user.status !== 'active') {
                    alert('الحساب غير مفعل');
                    return;
                }

                // حفظ بيانات المستخدم
                sessionStorage.setItem('currentUser', JSON.stringify(user));
                localStorage.setItem('currentUserRole', user.role);
                localStorage.setItem('currentUserId', user.id.toString());

                testResults.push(`<div class="test-item success">✓ تم تسجيل الدخول بنجاح للمستخدم: ${user.name}</div>`);
                updateResults();
                updateStep(3);

                alert('تم تسجيل الدخول بنجاح! سيتم توجيهك إلى نقطة البيع...');

                // التوجيه إلى نقطة البيع
                setTimeout(() => {
                    window.location.href = '../pages/staff/pos-simple.html';
                }, 2000);

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                alert('حدث خطأ في تسجيل الدخول');
            }
        }

        function simulateLogin() {
            // محاكاة تسجيل دخول سريع
            const testUser = {
                id: 999,
                username: 'test_staff',
                name: 'عامل تجريبي',
                role: 'staff',
                status: 'active',
                permissions: ['sell', 'view_products']
            };

            sessionStorage.setItem('currentUser', JSON.stringify(testUser));
            localStorage.setItem('currentUserRole', testUser.role);
            localStorage.setItem('currentUserId', testUser.id.toString());

            alert('تم محاكاة تسجيل الدخول! سيتم توجيهك إلى نقطة البيع...');
            setTimeout(() => {
                window.location.href = '../pages/staff/pos-simple.html';
            }, 1000);
        }

        function checkCurrentUser() {
            const diagnosticDiv = document.getElementById('diagnostic-results');
            let results = '<h3>فحص المستخدم الحالي:</h3>';

            try {
                const sessionUser = sessionStorage.getItem('currentUser');
                const localRole = localStorage.getItem('currentUserRole');
                const localId = localStorage.getItem('currentUserId');

                if (sessionUser) {
                    const user = JSON.parse(sessionUser);
                    results += `<div class="success">✓ مستخدم في sessionStorage: ${user.name} (${user.role})</div>`;
                } else {
                    results += `<div class="warning">⚠️ لا يوجد مستخدم في sessionStorage</div>`;
                }

                if (localRole) {
                    results += `<div class="info">📋 دور المستخدم: ${localRole}</div>`;
                }

                if (localId) {
                    results += `<div class="info">🆔 معرف المستخدم: ${localId}</div>`;
                }

            } catch (error) {
                results += `<div class="error">✗ خطأ في فحص المستخدم: ${error.message}</div>`;
            }

            diagnosticDiv.innerHTML = results;
        }

        function checkDatabase() {
            const diagnosticDiv = document.getElementById('diagnostic-results');
            let results = '<h3>فحص قاعدة البيانات:</h3>';

            try {
                const db = getDatabase();
                if (db) {
                    results += `<div class="success">✓ قاعدة البيانات متوفرة</div>`;
                    results += `<div class="info">👥 عدد المستخدمين: ${db.users ? db.users.length : 0}</div>`;
                    
                    if (db.users) {
                        const staffUsers = db.users.filter(u => u.role === 'staff');
                        results += `<div class="info">👨‍💼 عدد العاملين: ${staffUsers.length}</div>`;
                        
                        staffUsers.forEach(user => {
                            results += `<div class="info">• ${user.name} (${user.username}) - ${user.status}</div>`;
                        });
                    }
                } else {
                    results += `<div class="error">✗ قاعدة البيانات غير متوفرة</div>`;
                }
            } catch (error) {
                results += `<div class="error">✗ خطأ في قاعدة البيانات: ${error.message}</div>`;
            }

            diagnosticDiv.innerHTML = results;
        }

        function checkStorage() {
            const diagnosticDiv = document.getElementById('diagnostic-results');
            let results = '<h3>فحص التخزين:</h3>';

            try {
                // فحص localStorage
                const localStorageWorks = typeof(Storage) !== "undefined";
                results += localStorageWorks ? 
                    '<div class="success">✓ localStorage متوفر</div>' : 
                    '<div class="error">✗ localStorage غير متوفر</div>';

                // فحص sessionStorage
                const sessionStorageWorks = typeof(Storage) !== "undefined";
                results += sessionStorageWorks ? 
                    '<div class="success">✓ sessionStorage متوفر</div>' : 
                    '<div class="error">✗ sessionStorage غير متوفر</div>';

                // عرض محتويات التخزين
                results += '<h4>محتويات localStorage:</h4>';
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    results += `<div class="info">• ${key}</div>`;
                }

                results += '<h4>محتويات sessionStorage:</h4>';
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    results += `<div class="info">• ${key}</div>`;
                }

            } catch (error) {
                results += `<div class="error">✗ خطأ في فحص التخزين: ${error.message}</div>`;
            }

            diagnosticDiv.innerHTML = results;
        }

        function checkRedirection() {
            const diagnosticDiv = document.getElementById('diagnostic-results');
            let results = '<h3>فحص آلية التوجيه:</h3>';

            // فحص وجود الصفحات
            const pages = [
                { name: 'صفحة دخول العاملين', url: '../pages/staff/login.html' },
                { name: 'صفحة نقطة البيع', url: '../pages/staff/pos-simple.html' }
            ];

            Promise.all(pages.map(page => 
                fetch(page.url).then(response => ({
                    name: page.name,
                    url: page.url,
                    available: response.ok
                }))
            )).then(results => {
                let output = '<h3>فحص آلية التوجيه:</h3>';
                results.forEach(result => {
                    output += result.available ? 
                        `<div class="success">✓ ${result.name} متوفرة</div>` :
                        `<div class="error">✗ ${result.name} غير متوفرة</div>`;
                });
                
                output += '<div class="info">🔄 آلية التوجيه: pos-simple.html</div>';
                diagnosticDiv.innerHTML = output;
            }).catch(error => {
                diagnosticDiv.innerHTML = `<div class="error">✗ خطأ في فحص الصفحات: ${error.message}</div>`;
            });
        }

        function runFullFlowTest() {
            alert('سيتم تشغيل الاختبار الكامل...');
            
            // إنشاء حساب تجريبي
            document.getElementById('test-username').value = 'test_flow_' + Date.now();
            document.getElementById('test-password').value = '123456';
            document.getElementById('test-name').value = 'عامل اختبار التدفق';
            
            createTestStaff();
            
            setTimeout(() => {
                testLogin();
            }, 1000);
        }

        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح البيانات من التخزين
                sessionStorage.clear();
                localStorage.removeItem('currentUser');
                localStorage.removeItem('currentUserRole');
                localStorage.removeItem('currentUserId');
                localStorage.removeItem('posCart');
                
                // إعادة تعيين النموذج
                document.getElementById('test-username').value = 'test_staff';
                document.getElementById('test-password').value = '123456';
                document.getElementById('test-name').value = 'عامل تجريبي';
                
                alert('تم مسح البيانات التجريبية');
                location.reload();
            }
        }
    </script>
</body>
</html>
