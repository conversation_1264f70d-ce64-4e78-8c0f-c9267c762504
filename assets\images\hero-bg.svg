<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <rect width="1200" height="600" fill="url(#paint0_linear)" />
  
  <!-- Pattern Overlay -->
  <g opacity="0.1">
    <!-- Repeating Food Icons -->
    <g transform="translate(100, 100)">
      <path d="M50 0C22.3858 0 0 22.3858 0 50C0 77.6142 22.3858 100 50 100C77.6142 100 100 77.6142 100 50C100 22.3858 77.6142 0 50 0ZM50 90C27.9086 90 10 72.0914 10 50C10 27.9086 27.9086 10 50 10C72.0914 10 90 27.9086 90 50C90 72.0914 72.0914 90 50 90Z" fill="white" />
      <path d="M30 30C30 30 40 20 50 20C60 20 70 30 70 30L80 50H20L30 30Z" fill="white" />
    </g>
    
    <g transform="translate(300, 200)">
      <path d="M40 10L60 10L70 30L50 60L30 30L40 10Z" fill="white" />
      <path d="M20 40L80 40L90 60L10 60L20 40Z" fill="white" />
    </g>
    
    <g transform="translate(500, 100)">
      <circle cx="50" cy="50" r="40" stroke="white" stroke-width="5" />
      <path d="M30 40C30 40 40 30 50 30C60 30 70 40 70 40" stroke="white" stroke-width="5" />
      <path d="M30 60C30 60 40 50 50 50C60 50 70 60 70 60" stroke="white" stroke-width="5" />
    </g>
    
    <g transform="translate(700, 200)">
      <rect x="20" y="20" width="60" height="60" rx="10" stroke="white" stroke-width="5" />
      <circle cx="50" cy="50" r="20" fill="white" />
    </g>
    
    <g transform="translate(900, 100)">
      <path d="M20 40C20 40 30 20 50 20C70 20 80 40 80 40L90 60L10 60L20 40Z" fill="white" />
      <path d="M40 70C40 70 45 65 50 65C55 65 60 70 60 70" stroke="white" stroke-width="3" />
    </g>
    
    <!-- Repeating Patterns in Bottom Half -->
    <g transform="translate(100, 350)">
      <circle cx="50" cy="50" r="30" stroke="white" stroke-width="5" />
      <path d="M35 50L45 60L65 40" stroke="white" stroke-width="5" />
    </g>
    
    <g transform="translate(300, 400)">
      <path d="M10 10L90 10L50 90L10 10Z" stroke="white" stroke-width="5" />
      <circle cx="50" cy="40" r="15" fill="white" />
    </g>
    
    <g transform="translate(500, 350)">
      <rect x="20" y="20" width="60" height="60" rx="30" stroke="white" stroke-width="5" />
      <path d="M35 50H65" stroke="white" stroke-width="5" />
      <path d="M50 35V65" stroke="white" stroke-width="5" />
    </g>
    
    <g transform="translate(700, 400)">
      <path d="M50 10C50 10 10 30 10 50C10 70 30 90 50 90C70 90 90 70 90 50C90 30 50 10 50 10Z" stroke="white" stroke-width="5" />
      <path d="M30 40L50 60L70 40" stroke="white" stroke-width="5" />
    </g>
    
    <g transform="translate(900, 350)">
      <circle cx="30" cy="30" r="20" fill="white" />
      <circle cx="70" cy="30" r="20" fill="white" />
      <path d="M20 60C20 60 35 80 50 80C65 80 80 60 80 60" stroke="white" stroke-width="5" />
    </g>
  </g>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="1200" y2="600" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2e7d32" />
      <stop offset="1" stop-color="#1b5e20" />
    </linearGradient>
  </defs>
</svg>
