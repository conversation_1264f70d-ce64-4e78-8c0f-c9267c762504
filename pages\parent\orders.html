<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات الأبناء - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }
        
        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .filter-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-group label {
            font-weight: 500;
        }
        
        .filter-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }
        
        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        table tr {
            border-bottom: 1px solid #eee;
        }
        
        table tr:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }
        
        .status-pending {
            background-color: #ffeaa7;
            color: #d35400;
        }
        
        .status-processing {
            background-color: #81ecec;
            color: #00b894;
        }
        
        .status-completed {
            background-color: #55efc4;
            color: #00b894;
        }
        
        .status-cancelled {
            background-color: #fab1a0;
            color: #d63031;
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            border: none;
        }
        
        .view-btn {
            background-color: #2196f3;
            color: white;
        }
        
        .cancel-btn {
            background-color: #e74c3c;
            color: white;
        }
        
        .order-details {
            display: none;
            background-color: #f9f9f9;
            padding: 15px;
            margin-top: 10px;
            border-radius: 5px;
        }
        
        .order-products {
            margin-top: 10px;
        }
        
        .product-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #ddd;
        }
        
        .product-item:last-child {
            border-bottom: none;
        }
        
        .summary-card {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .summary-item {
            text-align: center;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .summary-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }
        
        .logout-btn i {
            margin-left: 8px;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .filter-section {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>ولي الأمر</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="children.html"><i class="fas fa-child"></i> الأبناء</a></li>
                <li><a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1>طلبات الأبناء</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>
            
            <div class="content-card">
                <h2>ملخص الطلبات</h2>
                <div class="summary-grid" id="summary-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>
            
            <div class="content-card">
                <h2>قائمة الطلبات</h2>
                <div class="filter-section">
                    <div class="filter-group">
                        <label for="child-filter">الابن:</label>
                        <select id="child-filter">
                            <option value="all">الكل</option>
                            <!-- سيتم ملء هذا القسم ديناميكيًا -->
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="status-filter">الحالة:</label>
                        <select id="status-filter">
                            <option value="all">الكل</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="processing">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                </div>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الابن</th>
                                <th>المنتجات</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is parent
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            
            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            
            // Get database
            const db = getDatabase();
            
            // Get children
            const children = db.users.filter(user => currentUser.children.includes(user.id));
            
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const childIdParam = urlParams.get('childId');
            
            // Populate child filter
            const childFilter = document.getElementById('child-filter');
            children.forEach(child => {
                const option = document.createElement('option');
                option.value = child.id;
                option.textContent = child.name;
                if (childIdParam && childIdParam == child.id) {
                    option.selected = true;
                }
                childFilter.appendChild(option);
            });
            
            // Get orders for children
            const childrenIds = currentUser.children;
            let orders = db.orders.filter(order => childrenIds.includes(order.userId));
            
            // Filter by child if specified in URL
            if (childIdParam) {
                orders = orders.filter(order => order.userId == childIdParam);
            }
            
            // Calculate summary statistics
            const totalOrders = orders.length;
            const pendingOrders = orders.filter(order => order.status === 'pending').length;
            const completedOrders = orders.filter(order => order.status === 'completed').length;
            const totalSpent = orders.reduce((sum, order) => sum + order.totalPrice, 0);
            
            // Populate summary grid
            const summaryGrid = document.getElementById('summary-grid');
            summaryGrid.innerHTML = `
                <div class="summary-item">
                    <div class="summary-value">${totalOrders}</div>
                    <div class="summary-label">إجمالي الطلبات</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">${pendingOrders}</div>
                    <div class="summary-label">الطلبات قيد الانتظار</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">${completedOrders}</div>
                    <div class="summary-label">الطلبات المكتملة</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">${totalSpent}</div>
                    <div class="summary-label">إجمالي المصروفات (ريال)</div>
                </div>
            `;
            
            // Function to update orders table based on filters
            function updateOrdersTable() {
                const selectedChildId = childFilter.value;
                const selectedStatus = document.getElementById('status-filter').value;
                
                // Filter orders
                let filteredOrders = orders;
                if (selectedChildId !== 'all') {
                    filteredOrders = filteredOrders.filter(order => order.userId == selectedChildId);
                }
                if (selectedStatus !== 'all') {
                    filteredOrders = filteredOrders.filter(order => order.status === selectedStatus);
                }
                
                // Populate orders table
                const ordersTable = document.getElementById('orders-table');
                ordersTable.innerHTML = '';
                
                if (filteredOrders.length === 0) {
                    ordersTable.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد طلبات.</td></tr>';
                } else {
                    filteredOrders.forEach(order => {
                        const child = db.users.find(u => u.id === order.userId);
                        
                        // Get product names
                        const productNames = order.products.map(p => {
                            const product = db.products.find(prod => prod.id === p.productId);
                            return `${product ? product.name : 'منتج غير معروف'} (${p.quantity})`;
                        }).join(', ');
                        
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${order.id}</td>
                            <td>${child ? child.name : 'غير معروف'}</td>
                            <td>${productNames}</td>
                            <td>${order.totalPrice} ريال</td>
                            <td>${order.date}</td>
                            <td><span class="status-badge status-${order.status}">${getOrderStatusText(order.status)}</span></td>
                            <td>
                                <button class="action-btn view-btn" data-id="${order.id}">عرض</button>
                                ${order.status === 'pending' ? `<button class="action-btn cancel-btn" data-id="${order.id}">إلغاء</button>` : ''}
                            </td>
                        `;
                        ordersTable.appendChild(row);
                        
                        // Create order details row
                        const detailsRow = document.createElement('tr');
                        detailsRow.className = 'order-details-row';
                        detailsRow.style.display = 'none';
                        
                        // Get detailed product information
                        const productDetails = order.products.map(p => {
                            const product = db.products.find(prod => prod.id === p.productId);
                            return `
                                <div class="product-item">
                                    <div>${product ? product.name : 'منتج غير معروف'}</div>
                                    <div>${p.quantity} × ${product ? product.price : 0} = ${p.quantity * (product ? product.price : 0)} ريال</div>
                                </div>
                            `;
                        }).join('');
                        
                        detailsRow.innerHTML = `
                            <td colspan="7">
                                <div class="order-details">
                                    <h3>تفاصيل الطلب #${order.id}</h3>
                                    <p>تاريخ الطلب: ${order.date}</p>
                                    <div class="order-products">
                                        <h4>المنتجات:</h4>
                                        ${productDetails}
                                    </div>
                                    <div style="margin-top: 10px; text-align: left; font-weight: bold;">
                                        الإجمالي: ${order.totalPrice} ريال
                                    </div>
                                </div>
                            </td>
                        `;
                        ordersTable.appendChild(detailsRow);
                    });
                    
                    // Add event listeners for view buttons
                    document.querySelectorAll('.view-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const orderId = this.getAttribute('data-id');
                            const detailsRow = this.closest('tr').nextElementSibling;
                            const detailsDiv = detailsRow.querySelector('.order-details');
                            
                            if (detailsRow.style.display === 'none') {
                                detailsRow.style.display = 'table-row';
                                detailsDiv.style.display = 'block';
                            } else {
                                detailsRow.style.display = 'none';
                                detailsDiv.style.display = 'none';
                            }
                        });
                    });
                    
                    // Add event listeners for cancel buttons
                    document.querySelectorAll('.cancel-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const orderId = parseInt(this.getAttribute('data-id'));
                            
                            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                                // Update order status
                                const orderIndex = db.orders.findIndex(o => o.id === orderId);
                                if (orderIndex !== -1) {
                                    db.orders[orderIndex].status = 'cancelled';
                                    saveDatabase(db);
                                    alert('تم إلغاء الطلب بنجاح!');
                                    window.location.reload();
                                }
                            }
                        });
                    });
                }
            }
            
            // Initial table update
            updateOrdersTable();
            
            // Add event listeners for filters
            childFilter.addEventListener('change', updateOrdersTable);
            document.getElementById('status-filter').addEventListener('change', updateOrdersTable);
            
            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
        
        // Helper function to get order status text
        function getOrderStatusText(status) {
            switch(status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }
    </script>
</body>
</html>
