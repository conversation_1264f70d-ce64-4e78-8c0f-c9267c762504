<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2e7d32">
    <meta name="description" content="إدارة الطلبات - نظام فواصل النجاح للخدمات الإعاشة">
    <title>إدارة الطلبات - فواصل النجاح للخدمات الإعاشة</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">

    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-left: 5px;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .edit-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
        }

        .status-btn {
            background-color: #9e9e9e;
            color: white;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffeb3b;
            color: #333;
        }

        .status-processing {
            background-color: #2196f3;
            color: white;
        }

        .status-completed {
            background-color: #4caf50;
            color: white;
        }

        .status-cancelled {
            background-color: #f44336;
            color: white;
        }

        .search-filter {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 5px;
            padding: 5px 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .search-box input {
            border: none;
            padding: 8px;
            width: 200px;
            font-family: 'Tajawal', sans-serif;
        }

        .search-box input:focus {
            outline: none;
        }

        .search-box i {
            color: #666;
        }

        .filter-options {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-options select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .date-filter {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-filter label {
            margin-bottom: 0;
        }

        .date-filter input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
            border: none;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .order-details {
            margin-bottom: 20px;
        }

        .order-details-row {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #f5f5f5;
            padding-bottom: 10px;
        }

        .order-details-label {
            font-weight: 500;
            width: 150px;
        }

        .order-products {
            margin-top: 20px;
        }

        .order-product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #f5f5f5;
        }

        .order-product-name {
            flex: 1;
        }

        .order-product-quantity {
            width: 80px;
            text-align: center;
        }

        .order-product-price {
            width: 100px;
            text-align: left;
        }

        .order-total {
            display: flex;
            justify-content: space-between;
            font-weight: 700;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 2px solid #f5f5f5;
        }

        .status-actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .status-actions h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .status-btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .status-btn-group button {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-family: 'Tajawal', sans-serif;
        }

        .btn-pending {
            background-color: #ffeb3b;
            color: #333;
        }

        .btn-processing {
            background-color: #2196f3;
            color: white;
        }

        .btn-completed {
            background-color: #4caf50;
            color: white;
        }

        .btn-cancelled {
            background-color: #f44336;
            color: white;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .main-content {
                padding: 10px;
            }

            .search-filter {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-box {
                margin-bottom: 10px;
                width: 100%;
            }

            .search-box input {
                width: 100%;
            }

            .filter-options {
                width: 100%;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .page-header h1 {
                margin-bottom: 10px;
            }

            .date-filter {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="schools.html"><i class="fas fa-school"></i> إدارة المدارس</a></li>
                <li><a href="users.html"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> إدارة العاملين</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>إدارة الطلبات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="البحث عن طلب...">
                    </div>
                    <div class="filter-options">
                        <select id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="processing">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                        <select id="school-filter">
                            <option value="">جميع المدارس</option>
                            <!-- سيتم ملء هذه القائمة ديناميكيًا -->
                        </select>
                        <div class="date-filter">
                            <label for="date-from">من:</label>
                            <input type="date" id="date-from">
                            <label for="date-to">إلى:</label>
                            <input type="date" id="date-to">
                            <button id="apply-filter-btn" class="action-btn view-btn">تطبيق</button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>المدرسة</th>
                                <th>الطالب</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Order Details -->
    <div id="order-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">تفاصيل الطلب</h2>
                <button class="close-btn">&times;</button>
            </div>
            <div id="order-details-content">
                <!-- سيتم ملء هذا القسم ديناميكيًا -->
            </div>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة للتحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم من sessionStorage
                let currentUser = null;
                try {
                    const userDataString = sessionStorage.getItem('currentUser');
                    if (userDataString) {
                        currentUser = JSON.parse(userDataString);
                    }
                } catch (sessionError) {
                    console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
                }

                // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
                if (!currentUser || !currentUser.role) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'admin' && userId) {
                        // إنشاء كائن مستخدم بسيط
                        currentUser = {
                            id: parseInt(userId),
                            role: userRole,
                            name: 'مدير النظام'
                        };

                        // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                        try {
                            const db = getDatabase();
                            const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                            if (fullUserData) {
                                currentUser = fullUserData;
                                // تخزين البيانات الكاملة في sessionStorage
                                sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                            }
                        } catch (dbError) {
                            console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                        }
                    }
                }

                // التحقق من صلاحيات المستخدم
                if (!currentUser || currentUser.role !== 'admin') {
                    alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                    window.location.href = '../auth/login.html';
                    return null;
                }

                return currentUser;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
                window.location.href = '../auth/login.html';
                return null;
            }
        }

        // وظيفة لتحويل حالة الطلب إلى نص
        function getOrderStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }

        // وظيفة لتحويل حالة الطلب إلى فئة CSS
        function getOrderStatusClass(status) {
            switch (status) {
                case 'pending':
                    return 'status-pending';
                case 'processing':
                    return 'status-processing';
                case 'completed':
                    return 'status-completed';
                case 'cancelled':
                    return 'status-cancelled';
                default:
                    return '';
            }
        }

        // وظيفة لتنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // وظيفة لعرض الطلبات في الجدول
        function displayOrders(orders, db) {
            const ordersTable = document.getElementById('orders-table');
            ordersTable.innerHTML = '';

            if (orders.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="7" style="text-align: center;">لا توجد طلبات متاحة</td>';
                ordersTable.appendChild(row);
                return;
            }

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            const sortedOrders = [...orders].sort((a, b) => new Date(b.date) - new Date(a.date));

            sortedOrders.forEach(order => {
                const school = db.schools.find(s => s.id === order.schoolId);
                const user = db.users.find(u => u.id === order.userId);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.id}</td>
                    <td>${school ? school.name : 'غير معروف'}</td>
                    <td>${user ? user.name : 'غير معروف'}</td>
                    <td>${order.totalPrice} ريال</td>
                    <td>${formatDate(order.date)}</td>
                    <td><span class="status-badge ${getOrderStatusClass(order.status)}">${getOrderStatusText(order.status)}</span></td>
                    <td>
                        <button class="action-btn view-btn" data-id="${order.id}"><i class="fas fa-eye"></i></button>
                        <button class="action-btn edit-btn" data-id="${order.id}"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" data-id="${order.id}"><i class="fas fa-trash"></i></button>
                    </td>
                `;
                ordersTable.appendChild(row);
            });

            // إضافة مستمعي الأحداث للأزرار
            attachButtonListeners();
        }

        // وظيفة لإضافة مستمعي الأحداث للأزرار
        function attachButtonListeners() {
            // أزرار العرض
            document.querySelectorAll('.view-btn').forEach(btn => {
                if (!btn.getAttribute('data-listener')) {
                    btn.setAttribute('data-listener', 'true');
                    btn.addEventListener('click', function() {
                        const orderId = parseInt(this.getAttribute('data-id'));
                        viewOrder(orderId);
                    });
                }
            });

            // أزرار التعديل
            document.querySelectorAll('.edit-btn').forEach(btn => {
                if (!btn.getAttribute('data-listener')) {
                    btn.setAttribute('data-listener', 'true');
                    btn.addEventListener('click', function() {
                        const orderId = parseInt(this.getAttribute('data-id'));
                        editOrder(orderId);
                    });
                }
            });

            // أزرار الحذف
            document.querySelectorAll('.delete-btn').forEach(btn => {
                if (!btn.getAttribute('data-listener')) {
                    btn.setAttribute('data-listener', 'true');
                    btn.addEventListener('click', function() {
                        const orderId = parseInt(this.getAttribute('data-id'));
                        deleteOrder(orderId);
                    });
                }
            });
        }

        // وظيفة لعرض تفاصيل الطلب
        function viewOrder(orderId) {
            const db = getDatabase();
            const order = db.orders.find(o => o.id === orderId);

            if (!order) {
                alert('لم يتم العثور على الطلب!');
                return;
            }

            const school = db.schools.find(s => s.id === order.schoolId);
            const user = db.users.find(u => u.id === order.userId);

            // إعداد محتوى النافذة المنبثقة
            document.getElementById('modal-title').textContent = `تفاصيل الطلب #${order.id}`;

            const orderDetailsContent = document.getElementById('order-details-content');

            // إعداد تفاصيل الطلب
            let detailsHTML = `
                <div class="order-details">
                    <div class="order-details-row">
                        <div class="order-details-label">رقم الطلب:</div>
                        <div>${order.id}</div>
                    </div>
                    <div class="order-details-row">
                        <div class="order-details-label">التاريخ:</div>
                        <div>${formatDate(order.date)}</div>
                    </div>
                    <div class="order-details-row">
                        <div class="order-details-label">المدرسة:</div>
                        <div>${school ? school.name : 'غير معروف'}</div>
                    </div>
                    <div class="order-details-row">
                        <div class="order-details-label">الطالب:</div>
                        <div>${user ? user.name : 'غير معروف'}</div>
                    </div>
                    <div class="order-details-row">
                        <div class="order-details-label">الحالة:</div>
                        <div><span class="status-badge ${getOrderStatusClass(order.status)}">${getOrderStatusText(order.status)}</span></div>
                    </div>
                </div>

                <div class="order-products">
                    <h3>المنتجات</h3>
            `;

            // إضافة المنتجات
            let totalPrice = 0;
            order.products.forEach(item => {
                const product = db.products.find(p => p.id === item.productId);
                if (product) {
                    const itemTotal = product.price * item.quantity;
                    totalPrice += itemTotal;

                    detailsHTML += `
                        <div class="order-product-item">
                            <div class="order-product-name">${product.name}</div>
                            <div class="order-product-quantity">${item.quantity} × ${product.price} ريال</div>
                            <div class="order-product-price">${itemTotal} ريال</div>
                        </div>
                    `;
                }
            });

            // إضافة المجموع
            detailsHTML += `
                <div class="order-total">
                    <div>المجموع:</div>
                    <div>${order.totalPrice} ريال</div>
                </div>
            `;

            // إضافة أزرار تغيير الحالة (للعرض فقط)
            detailsHTML += `
                <div class="status-actions">
                    <h3>تغيير الحالة</h3>
                    <div class="status-btn-group">
                        <button class="btn-pending" onclick="changeOrderStatus(${order.id}, 'pending')">قيد الانتظار</button>
                        <button class="btn-processing" onclick="changeOrderStatus(${order.id}, 'processing')">قيد التنفيذ</button>
                        <button class="btn-completed" onclick="changeOrderStatus(${order.id}, 'completed')">مكتمل</button>
                        <button class="btn-cancelled" onclick="changeOrderStatus(${order.id}, 'cancelled')">ملغي</button>
                    </div>
                </div>
            `;

            orderDetailsContent.innerHTML = detailsHTML;

            // عرض النافذة المنبثقة
            document.getElementById('order-modal').style.display = 'flex';
        }

        // وظيفة لتعديل الطلب
        function editOrder(orderId) {
            // في هذه الحالة، نستخدم نفس نافذة العرض ولكن مع إمكانية تغيير الحالة
            viewOrder(orderId);
        }

        // وظيفة لحذف الطلب
        function deleteOrder(orderId) {
            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                const db = getDatabase();
                const orderIndex = db.orders.findIndex(o => o.id === orderId);

                if (orderIndex === -1) {
                    alert('لم يتم العثور على الطلب!');
                    return;
                }

                // حذف الطلب من المصفوفة
                db.orders.splice(orderIndex, 1);

                // حفظ التغييرات في قاعدة البيانات
                saveDatabase(db);

                // إعادة عرض الطلبات
                filterOrders();

                alert('تم حذف الطلب بنجاح!');
            }
        }

        // وظيفة لتغيير حالة الطلب
        function changeOrderStatus(orderId, newStatus) {
            const db = getDatabase();
            const orderIndex = db.orders.findIndex(o => o.id === orderId);

            if (orderIndex === -1) {
                alert('لم يتم العثور على الطلب!');
                return;
            }

            // تغيير حالة الطلب
            db.orders[orderIndex].status = newStatus;

            // حفظ التغييرات في قاعدة البيانات
            saveDatabase(db);

            // إغلاق النافذة المنبثقة
            document.getElementById('order-modal').style.display = 'none';

            // إعادة عرض الطلبات
            filterOrders();

            alert(`تم تغيير حالة الطلب إلى "${getOrderStatusText(newStatus)}" بنجاح!`);
        }

        // وظيفة لتصفية الطلبات حسب البحث والفلتر
        function filterOrders() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;
            const schoolFilter = document.getElementById('school-filter').value;
            const dateFrom = document.getElementById('date-from').value ? new Date(document.getElementById('date-from').value) : null;
            const dateTo = document.getElementById('date-to').value ? new Date(document.getElementById('date-to').value) : null;

            // ضبط dateTo ليشمل اليوم بأكمله
            if (dateTo) {
                dateTo.setHours(23, 59, 59, 999);
            }

            const db = getDatabase();

            // تطبيق الفلاتر
            const filteredOrders = db.orders.filter(order => {
                // البحث في رقم الطلب أو اسم الطالب أو اسم المدرسة
                const user = db.users.find(u => u.id === order.userId);
                const school = db.schools.find(s => s.id === order.schoolId);

                const orderIdStr = order.id.toString();
                const userName = user ? user.name.toLowerCase() : '';
                const schoolName = school ? school.name.toLowerCase() : '';

                const matchesSearch = orderIdStr.includes(searchTerm) ||
                                     userName.includes(searchTerm) ||
                                     schoolName.includes(searchTerm);

                // فلتر الحالة
                const matchesStatus = !statusFilter || order.status === statusFilter;

                // فلتر المدرسة
                const matchesSchool = !schoolFilter || order.schoolId === parseInt(schoolFilter);

                // فلتر التاريخ
                const orderDate = new Date(order.date);
                const matchesDateFrom = !dateFrom || orderDate >= dateFrom;
                const matchesDateTo = !dateTo || orderDate <= dateTo;

                return matchesSearch && matchesStatus && matchesSchool && matchesDateFrom && matchesDateTo;
            });

            // عرض الطلبات المصفاة
            displayOrders(filteredOrders, db);
        }

        // وظيفة لملء قائمة المدارس
        function populateSchoolFilter() {
            const db = getDatabase();
            const schoolFilter = document.getElementById('school-filter');

            db.schools.forEach(school => {
                const option = document.createElement('option');
                option.value = school.id;
                option.textContent = school.name;
                schoolFilter.appendChild(option);
            });
        }

        // تهيئة الصفحة عند تحميلها
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صلاحيات المستخدم
            const currentUser = checkUserAuthorization();
            if (!currentUser) {
                return;
            }

            // تعيين اسم المستخدم
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // تعيين التاريخ الحالي
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // تعيين التاريخ الافتراضي للفلتر (الشهر الحالي)
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            document.getElementById('date-from').valueAsDate = firstDay;
            document.getElementById('date-to').valueAsDate = lastDay;

            // ملء قائمة المدارس
            populateSchoolFilter();

            // عرض الطلبات
            filterOrders();

            // إضافة مستمعي الأحداث للبحث والفلاتر
            document.getElementById('search-input').addEventListener('input', filterOrders);
            document.getElementById('status-filter').addEventListener('change', filterOrders);
            document.getElementById('school-filter').addEventListener('change', filterOrders);
            document.getElementById('apply-filter-btn').addEventListener('click', filterOrders);

            // إضافة مستمع الحدث لزر إغلاق النافذة المنبثقة
            document.querySelector('.close-btn').addEventListener('click', function() {
                document.getElementById('order-modal').style.display = 'none';
            });

            // زر تسجيل الخروج
            document.getElementById('logout-btn').addEventListener('click', function() {
                try {
                    // حذف بيانات المستخدم من جميع وسائل التخزين
                    sessionStorage.clear();
                    localStorage.removeItem('currentUserRole');
                    localStorage.removeItem('currentUserId');

                    // حذف الكوكيز المتعلقة بالمستخدم
                    document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                    console.log('تم تسجيل الخروج بنجاح');

                    // التوجيه إلى صفحة تسجيل الدخول
                    window.location.href = '../auth/login.html';
                } catch (error) {
                    console.error('خطأ في تسجيل الخروج:', error);
                }
            });
        });
    </script>
</body>
</html>
