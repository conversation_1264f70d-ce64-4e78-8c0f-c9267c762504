<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2e7d32">
    <meta name="description" content="تسجيل دخول العاملين - نظام فواصل النجاح للخدمات الإعاشة">
    <title>تسجيل دخول العاملين - فواصل النجاح للخدمات الإعاشة</title>

    <!-- تحسين توافق المتصفحات -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- ملفات CSS الأساسية -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">

    <style>
        /* تعريف المتغيرات الأساسية */
        :root {
            --primary-color: #2e7d32;
            --secondary-color: #f9a825;
            --dark-color: #1b5e20;
            --light-color: #e8f5e9;
            --text-color: #333;
            --white-color: #fff;
            --beta-color: #ff5722;
            --beta-dark-color: #e64a19;
        }

        /* أنماط أساسية */
        body {
            opacity: 1;
            visibility: visible;
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 40px 0 0 0; /* إضافة مساحة للشريط التجريبي */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* شريط تجريبي */
        .beta-banner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(45deg, var(--beta-dark-color), var(--beta-color));
            color: white;
            text-align: center;
            padding: 8px 0;
            font-weight: bold;
            z-index: 2000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }

        .beta-banner span {
            display: inline-block;
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            margin: 0 5px;
            font-size: 0.9rem;
            transform: rotate(-2deg);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
        }

        /* تحسين توافق المتصفحات */
        html, body {
            width: 100%;
            height: 100%;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            overflow-x: hidden;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            body {
                -webkit-overflow-scrolling: touch;
            }
        }

        /* شاشة التحميل */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
            -webkit-transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .preloader-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2e7d32;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            -webkit-animation: spin 1s linear infinite;
        }

        @-webkit-keyframes spin {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسينات حاوية تسجيل الدخول */
        .auth-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            -webkit-transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .auth-header h2 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .auth-header p {
            color: #666;
            margin: 0;
        }

        .auth-form .form-group {
            margin-bottom: 20px;
        }

        .auth-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--primary-color);
        }

        .auth-form input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .auth-form input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
            outline: none;
            background-color: white;
        }

        .auth-form button {
            width: 100%;
            padding: 12px;
            margin-top: 10px;
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .auth-form button:hover {
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
            transform: translateY(-2px);
            -webkit-transform: translateY(-2px);
        }

        .auth-footer {
            text-align: center;
            margin-top: 20px;
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
        }

        .auth-footer a:hover {
            text-decoration: underline;
            color: var(--dark-color);
        }

        /* تأثيرات حركية */
        @-webkit-keyframes fadeIn {
            from { opacity: 0; -webkit-transform: translateY(20px); }
            to { opacity: 1; -webkit-transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @-webkit-keyframes fadeOut {
            from { opacity: 1; -webkit-transform: translate(-50%, -50%); }
            to { opacity: 0; -webkit-transform: translate(-50%, -60%); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, -50%); }
            to { opacity: 0; transform: translate(-50%, -60%); }
        }

        @-webkit-keyframes pulse {
            0% { -webkit-transform: scale(1); }
            50% { -webkit-transform: scale(1.05); }
            100% { -webkit-transform: scale(1); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .success-message, .error-message {
            animation: fadeIn 0.5s ease forwards;
            -webkit-animation: fadeIn 0.5s ease forwards;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 576px) {
            .auth-container {
                margin: 20px auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body class="loading">
    <!-- Beta Banner -->
    <div class="beta-banner">
        <span>تجريبي</span> هذا النظام في مرحلة تجريبية وقد يخضع للتغييرات والتحسينات المستمرة
    </div>

    <!-- شاشة التحميل -->
    <div class="preloader" id="preloader">
        <div class="preloader-content">
            <div class="preloader-spinner"></div>
            <p style="margin-top: 15px; color: #2e7d32; font-weight: 500;">جاري تحميل الصفحة...</p>
        </div>
    </div>
    <noscript>
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: #fff; z-index: 10000; display: flex; justify-content: center; align-items: center; text-align: center; padding: 20px;">
            <div>
                <h2 style="color: #e74c3c;">يرجى تفعيل JavaScript</h2>
                <p>هذا الموقع يتطلب تفعيل JavaScript للعمل بشكل صحيح.</p>
            </div>
        </div>
    </noscript>

    <!-- إضافة عنصر للتحقق من حالة تحميل الصفحة -->
    <div id="page-load-check" style="display: none;"></div>

    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="../../index.html">
                    <img src="../../assets/images/logo.svg" alt="فواصل النجاح" class="logo-img">
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h2>تسجيل دخول العاملين</h2>
                    <p>يرجى إدخال بيانات الدخول الخاصة بك</p>
                </div>
                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit">تسجيل الدخول</button>
                </form>
                <div class="auth-footer">
                    <a href="../auth/login.html">العودة إلى صفحة تسجيل الدخول الرئيسية</a>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة لإظهار رسالة نجاح
        function showSuccessMessage(message) {
            try {
                // إزالة أي رسائل سابقة
                const oldMessages = document.querySelectorAll('.success-message, .error-message');
                oldMessages.forEach(msg => {
                    if (msg && msg.parentNode) {
                        msg.parentNode.removeChild(msg);
                    }
                });

                const successMessage = document.createElement('div');
                successMessage.className = 'success-message';
                successMessage.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <p>${message}</p>
                `;
                successMessage.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #2e7d32;
                    color: white;
                    padding: 20px 30px;
                    border-radius: 10px;
                    text-align: center;
                    z-index: 1000;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                    -webkit-transform: translate(-50%, -50%);
                `;

                const icon = successMessage.querySelector('i');
                if (icon) {
                    icon.style.cssText = `
                        font-size: 3rem;
                        margin-bottom: 10px;
                        display: block;
                        animation: pulse 1s infinite;
                        -webkit-animation: pulse 1s infinite;
                    `;
                }

                document.body.appendChild(successMessage);
                return successMessage;
            } catch (error) {
                console.error('خطأ في إظهار رسالة النجاح:', error);
                alert(message); // استخدام alert كبديل في حالة الفشل
                return null;
            }
        }

        // وظيفة لإظهار رسالة خطأ
        function showErrorMessage(message) {
            try {
                // إزالة أي رسائل سابقة
                const oldMessages = document.querySelectorAll('.success-message, .error-message');
                oldMessages.forEach(msg => {
                    if (msg && msg.parentNode) {
                        msg.parentNode.removeChild(msg);
                    }
                });

                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i>
                    <p>${message}</p>
                `;
                errorMessage.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #e74c3c;
                    color: white;
                    padding: 20px 30px;
                    border-radius: 10px;
                    text-align: center;
                    z-index: 1000;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                    -webkit-transform: translate(-50%, -50%);
                `;

                const icon = errorMessage.querySelector('i');
                if (icon) {
                    icon.style.cssText = `
                        font-size: 3rem;
                        margin-bottom: 10px;
                        display: block;
                    `;
                }

                document.body.appendChild(errorMessage);

                // إزالة رسالة الخطأ بعد فترة
                setTimeout(() => {
                    try {
                        errorMessage.style.animation = 'fadeOut 0.5s ease forwards';
                        errorMessage.style.webkitAnimation = 'fadeOut 0.5s ease forwards';
                        setTimeout(() => {
                            if (errorMessage && errorMessage.parentNode) {
                                errorMessage.parentNode.removeChild(errorMessage);
                            }
                        }, 500);
                    } catch (removeError) {
                        console.error('خطأ في إزالة رسالة الخطأ:', removeError);
                        // محاولة إزالة الرسالة بطريقة بديلة
                        if (errorMessage && errorMessage.parentNode) {
                            errorMessage.parentNode.removeChild(errorMessage);
                        }
                    }
                }, 3000);

                return errorMessage;
            } catch (error) {
                console.error('خطأ في إظهار رسالة الخطأ:', error);
                alert(message); // استخدام alert كبديل في حالة الفشل
                return null;
            }
        }

        // وظيفة لإخفاء شاشة التحميل
        function hidePreloader() {
            try {
                const preloader = document.getElementById('preloader');
                if (preloader) {
                    preloader.style.opacity = '0';
                    preloader.style.visibility = 'hidden';

                    setTimeout(function() {
                        try {
                            preloader.style.display = 'none';
                            document.body.classList.add('loaded');
                            document.body.classList.remove('loading');
                            console.log('تم إخفاء شاشة التحميل بنجاح');
                        } catch (innerError) {
                            console.error('خطأ عند محاولة إخفاء شاشة التحميل:', innerError);
                        }
                    }, 500);
                } else {
                    console.warn('لم يتم العثور على عنصر شاشة التحميل');
                }
            } catch (error) {
                console.error('خطأ في إزالة شاشة التحميل:', error);
            }
        }

        // التحقق من حالة تحميل الصفحة
        function checkPageLoadStatus() {
            try {
                // التحقق من حالة تحميل الصفحة
                if (document.readyState === 'complete' || document.readyState === 'interactive') {
                    console.log('الصفحة جاهزة بالفعل، جاري إخفاء شاشة التحميل');
                    hidePreloader();
                    return true;
                }
                return false;
            } catch (error) {
                console.error('خطأ في التحقق من حالة تحميل الصفحة:', error);
                return false;
            }
        }

        // محاولة إخفاء شاشة التحميل مباشرة إذا كانت الصفحة جاهزة
        if (!checkPageLoadStatus()) {
            // إضافة مستمع لحدث DOMContentLoaded
            document.addEventListener('DOMContentLoaded', function() {
                console.log('تم تحميل DOM، جاري التحقق من شاشة التحميل');
                // محاولة إخفاء شاشة التحميل
                setTimeout(hidePreloader, 300);
            });

            // إخفاء شاشة التحميل عند اكتمال تحميل الصفحة
            window.addEventListener('load', function() {
                console.log('تم تحميل الصفحة بالكامل، جاري إخفاء شاشة التحميل');
                // إخفاء شاشة التحميل
                hidePreloader();
                // إزالة فئة التحميل من الجسم
                document.body.classList.remove('loading');
                document.body.classList.add('loaded');
            });
        }

        // إخفاء شاشة التحميل في حالة استغرق التحميل وقتاً طويلاً
        setTimeout(function() {
            console.log('تم تجاوز الحد الأقصى لوقت التحميل، جاري إخفاء شاشة التحميل');
            hidePreloader();
            // إزالة فئة التحميل من الجسم
            document.body.classList.remove('loading');
            document.body.classList.add('loaded');
        }, 3000); // إخفاء بعد 3 ثواني كحد أقصى

        // معالجة تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    // التحقق من البيانات
                    if (!username || !password) {
                        showErrorMessage('الرجاء إدخال اسم المستخدم وكلمة المرور');
                        return;
                    }

                    try {
                        // الحصول على قاعدة البيانات
                        const db = getDatabase();

                        if (!db || !db.users) {
                            console.error('قاعدة البيانات غير متوفرة');
                            showErrorMessage('حدث خطأ في النظام. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                            return;
                        }

                        // البحث عن المستخدم بالاسم والدور أولاً
                        const user = db.users.find(u =>
                            u.username === username &&
                            u.role === 'staff'
                        );

                        if (!user) {
                            showErrorMessage('اسم المستخدم أو كلمة المرور غير صحيحة!');
                            return;
                        }

                        // التحقق من كلمة المرور - مقارنة مباشرة
                        const passwordMatch = (user.password === password);

                        console.log('محاولة تسجيل دخول الموظف:', {
                            username: username,
                            userFound: !!user,
                            passwordMatch: passwordMatch,
                            userStatus: user.status,
                            storedPassword: user.password,
                            enteredPassword: password
                        });

                        if (!passwordMatch) {
                            showErrorMessage('اسم المستخدم أو كلمة المرور غير صحيحة!');
                            return;
                        }

                        // التحقق من حالة الحساب
                        if (user.status !== 'active') {
                                showErrorMessage('هذا الحساب غير مفعل. يرجى التواصل مع المدير.');
                                return;
                            }

                            // إظهار رسالة نجاح
                            showSuccessMessage('تم تسجيل الدخول بنجاح! جاري تحويلك...');

                            // تخزين بيانات المستخدم
                            sessionStorage.setItem('currentUser', JSON.stringify(user));
                            localStorage.setItem('currentUserRole', user.role);
                            localStorage.setItem('currentUserId', user.id.toString());

                            // التوجيه إلى الصفحة المناسبة
                            setTimeout(() => {
                                if (user.permissions && user.permissions.includes('sell')) {
                                    window.location.href = 'pos-simple.html';
                                } else {
                                    // توجيه جميع العاملين إلى نقطة البيع كافتراضي
                                    window.location.href = 'pos-simple.html';
                                }
                            }, 2000);
                    } catch (error) {
                        console.error('Error during login:', error);
                        showErrorMessage('حدث خطأ أثناء تسجيل الدخول. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                    }
                });
            }
        });
    </script>
</body>
</html>
