/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * ملف CSS الخاص بلوحة تحكم المدير
 */

/* ===== متغيرات CSS ===== */
:root {
    /* الألوان الأساسية */
    --admin-primary: #2e7d32;
    --admin-secondary: #f9a825;
    --admin-dark: #1b5e20;
    --admin-light: #e8f5e9;
    --admin-gray: #f5f5f5;
    --admin-text: #333;
    --admin-white: #fff;
    --admin-background: #f8f9fa;
    --admin-surface: #ffffff;

    /* ألوان الحالة */
    --admin-success: #4caf50;
    --admin-warning: #ff9800;
    --admin-danger: #f44336;
    --admin-info: #2196f3;

    /* الظلال والانتقالات */
    --admin-shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --admin-shadow-md: 0 4px 10px rgba(0, 0, 0, 0.1);
    --admin-shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
    --admin-shadow-xl: 0 15px 30px rgba(0, 0, 0, 0.2);
    --admin-transition: all 0.3s ease;

    /* نصف قطر الحواف */
    --admin-radius-sm: 5px;
    --admin-radius-md: 10px;
    --admin-radius-lg: 15px;
    --admin-radius-xl: 20px;
    --admin-radius-full: 9999px;
}

/* ===== تخطيط لوحة التحكم ===== */
.dashboard {
    display: flex;
    min-height: calc(100vh - 70px);
    background-color: var(--admin-background);
}

.sidebar {
    width: 280px;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-dark));
    color: var(--admin-white);
    padding: 20px 0;
    box-shadow: var(--admin-shadow-lg);
    position: fixed;
    height: calc(100vh - 70px);
    overflow-y: auto;
    z-index: 10;
    transition: var(--admin-transition);
}

.main-content {
    flex: 1;
    padding: 20px;
    margin-right: 280px;
    transition: var(--admin-transition);
}

/* ===== رأس الصفحة ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.page-header h1 {
    font-size: 1.8rem;
    color: var(--admin-primary);
    position: relative;
    padding-right: 15px;
}

.page-header h1::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 25px;
    background: linear-gradient(to bottom, var(--admin-primary), var(--admin-secondary));
    border-radius: var(--admin-radius-sm);
}

.date {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.date i {
    margin-left: 8px;
    color: var(--admin-primary);
}

/* ===== القائمة الجانبية ===== */
.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
    text-align: center;
}

.sidebar-header h2 {
    font-size: 1.4rem;
    margin-bottom: 5px;
    color: var(--admin-white);
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
    color: var(--admin-white);
}

.sidebar-menu {
    list-style: none;
    padding: 0 10px;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu li a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: var(--admin-white);
    border-radius: var(--admin-radius-md);
    transition: var(--admin-transition);
}

.sidebar-menu li a:hover,
.sidebar-menu li a.active {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
}

.sidebar-menu li a.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--admin-shadow-sm);
}

.sidebar-menu li a i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
    transition: var(--admin-transition);
}

.sidebar-menu li a:hover i,
.sidebar-menu li a.active i {
    transform: scale(1.2);
}

/* ===== بطاقات الإحصائيات ===== */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--admin-surface);
    border-radius: var(--admin-radius-md);
    padding: 20px;
    box-shadow: var(--admin-shadow-md);
    display: flex;
    align-items: center;
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--admin-shadow-lg);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--admin-primary), var(--admin-secondary));
    border-radius: var(--admin-radius-sm);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 1.5rem;
    transition: var(--admin-transition);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(10deg);
}

.stat-icon.schools {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--admin-primary);
}

.stat-icon.students {
    background-color: rgba(249, 168, 37, 0.1);
    color: var(--admin-secondary);
}

.stat-icon.products {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--admin-info);
}

.stat-icon.orders {
    background-color: rgba(233, 30, 99, 0.1);
    color: #e91e63;
}

.stat-icon.staff {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.stat-icon.parents {
    background-color: rgba(0, 150, 136, 0.1);
    color: #009688;
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    color: var(--admin-text);
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* ===== بطاقات المحتوى ===== */
.content-card {
    background-color: var(--admin-surface);
    border-radius: var(--admin-radius-md);
    padding: 20px;
    box-shadow: var(--admin-shadow-md);
    margin-bottom: 25px;
    transition: var(--admin-transition);
}

.content-card:hover {
    box-shadow: var(--admin-shadow-lg);
}

.content-card h2 {
    font-size: 1.3rem;
    color: var(--admin-primary);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-card h2 .card-actions {
    display: flex;
    gap: 10px;
}

/* ===== الجداول ===== */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--admin-radius-sm);
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 10px;
}

table th, table td {
    padding: 12px 15px;
    text-align: right;
}

table th {
    background-color: rgba(46, 125, 50, 0.05);
    color: var(--admin-primary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

table tr {
    border-bottom: 1px solid #eee;
    transition: var(--admin-transition);
}

table tr:hover {
    background-color: rgba(46, 125, 50, 0.02);
}

table tr:last-child {
    border-bottom: none;
}

/* ===== أزرار الإجراءات ===== */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--admin-transition);
    margin: 0 2px;
    font-size: 0.9rem;
}

.action-btn:hover {
    transform: translateY(-3px);
}

.view-btn {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--admin-info);
}

.view-btn:hover {
    background-color: var(--admin-info);
    color: white;
}

.edit-btn {
    background-color: rgba(249, 168, 37, 0.1);
    color: var(--admin-secondary);
}

.edit-btn:hover {
    background-color: var(--admin-secondary);
    color: white;
}

.delete-btn {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--admin-danger);
}

.delete-btn:hover {
    background-color: var(--admin-danger);
    color: white;
}

/* ===== شارات الحالة ===== */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: var(--admin-radius-full);
    font-size: 0.8rem;
    font-weight: 500;
}

.status-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--admin-success);
}

.status-warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--admin-warning);
}

.status-danger {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--admin-danger);
}

.status-info {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--admin-info);
}

/* ===== قائمة الأنشطة ===== */
.activities-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: var(--admin-radius-md);
    transition: var(--admin-transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:hover {
    background-color: white;
    transform: translateX(-5px);
    box-shadow: var(--admin-shadow-sm);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--admin-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    flex-shrink: 0;
}

.activity-details {
    flex: 1;
}

.activity-text {
    margin-bottom: 5px;
    color: var(--admin-text);
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #666;
}

.activity-user {
    font-weight: 500;
    color: var(--admin-primary);
}

/* ===== الإشعارات ===== */
.notifications-dropdown {
    position: relative;
}

.notifications-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.1rem; /* تم تصغير حجم الأيقونة */
    width: 36px; /* تم تصغير حجم الدائرة */
    height: 36px; /* تم تصغير حجم الدائرة */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background-color: rgba(0, 0, 0, 0.03);
}

.notifications-count {
    position: absolute;
    top: -5px;
    left: -5px;
    width: 16px;
    height: 16px;
    background-color: var(--admin-danger);
    color: white;
    border-radius: 50%;
    font-size: 0.65rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.notifications-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 300px;
    background-color: white;
    border-radius: var(--admin-radius-md);
    box-shadow: var(--admin-shadow-lg);
    z-index: 100;
    display: none;
}

.notifications-menu.show {
    display: block;
}

.notifications-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h3 {
    font-size: 0.95rem;
    margin: 0;
    font-weight: 600;
}

.mark-all-read {
    font-size: 0.8rem;
    color: var(--admin-primary);
    cursor: pointer;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    transition: var(--admin-transition);
}

.notification-item:hover {
    background-color: rgba(46, 125, 50, 0.05);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-content {
    font-size: 0.85rem;
    margin-bottom: 5px;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: #666;
}

.notification-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid #eee;
}

.notification-footer a {
    color: var(--admin-primary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* ===== الزر العائم ===== */
.floating-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-dark));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--admin-shadow-lg);
    cursor: pointer;
    transition: var(--admin-transition);
    z-index: 99;
}

.floating-btn:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: var(--admin-shadow-xl);
}

/* ===== الإجراءات السريعة ===== */
.quick-actions-menu {
    position: absolute;
    bottom: 100px;
    left: 30px;
    background-color: white;
    border-radius: var(--admin-radius-md);
    box-shadow: var(--admin-shadow-lg);
    padding: 10px;
    z-index: 98;
    display: none;
}

.quick-actions-menu.show {
    display: block;
}

.quick-action {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: var(--admin-radius-sm);
    transition: var(--admin-transition);
    cursor: pointer;
}

.quick-action:hover {
    background-color: rgba(46, 125, 50, 0.1);
}

.quick-action i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
    color: var(--admin-primary);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
        overflow: visible;
    }

    .sidebar-header h2,
    .sidebar-header p,
    .sidebar-menu li a span {
        display: none;
    }

    .sidebar-menu li a {
        justify-content: center;
        padding: 15px;
    }

    .sidebar-menu li a i {
        margin: 0;
    }

    .main-content {
        margin-right: 70px;
    }

    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        overflow: hidden;
    }

    .sidebar-header h2,
    .sidebar-header p,
    .sidebar-menu li a span {
        display: block;
    }

    .sidebar-menu {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .sidebar-menu li {
        margin: 5px;
    }

    .sidebar-menu li a {
        padding: 10px 15px;
    }

    .sidebar-menu li a i {
        margin-left: 10px;
    }

    .main-content {
        margin-right: 0;
    }

    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin: 0 0 10px 0;
    }
}
