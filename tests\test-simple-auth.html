<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للمصادقة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1b5e20;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .step {
            background: #f0f0f0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بسيط للمصادقة</h1>
        <p>اختبار سريع للتأكد من عمل نظام التسجيل وتسجيل الدخول</p>

        <div class="test-section">
            <h3>1. اختبار التدفق الكامل</h3>
            <input type="text" id="test-username" placeholder="اسم مستخدم جديد" value="">
            <input type="password" id="test-password" placeholder="كلمة مرور" value="123456">
            <input type="text" id="test-name" placeholder="الاسم" value="مستخدم تجريبي">
            <br>
            <button onclick="testCompleteFlow()">اختبار التسجيل والدخول</button>
            <div id="flow-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. عرض المستخدمين الحاليين</h3>
            <button onclick="showCurrentUsers()">عرض المستخدمين</button>
            <div id="users-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار تسجيل دخول مستخدم موجود</h3>
            <input type="text" id="existing-username" placeholder="اسم المستخدم" value="admin">
            <input type="password" id="existing-password" placeholder="كلمة المرور" value="admin123">
            <select id="existing-role">
                <option value="admin">مدير</option>
                <option value="student">طالب</option>
                <option value="parent">ولي أمر</option>
                <option value="school">مدرسة</option>
                <option value="staff">موظف</option>
            </select>
            <br>
            <button onclick="testExistingLogin()">اختبار تسجيل الدخول</button>
            <div id="existing-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. إعادة تعيين قاعدة البيانات</h3>
            <button onclick="resetDatabase()" style="background: #d32f2f;">إعادة تعيين</button>
            <div id="reset-result" class="result"></div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // انتظار تحميل النظام
        setTimeout(() => {
            console.log('نظام الاختبار البسيط جاهز');
        }, 1000);

        async function testCompleteFlow() {
            const resultDiv = document.getElementById('flow-result');
            const username = document.getElementById('test-username').value || `testuser_${Date.now()}`;
            const password = document.getElementById('test-password').value;
            const name = document.getElementById('test-name').value;

            if (!password || !name) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء ملء جميع الحقول</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 بدء الاختبار...</div>';

                // Step 1: Register new user
                resultDiv.innerHTML += '<div class="step">الخطوة 1: تسجيل مستخدم جديد...</div>';
                
                const db = getDatabase();
                
                // Check if username exists
                if (db.users.some(user => user.username === username)) {
                    resultDiv.innerHTML += '<div class="error">✗ اسم المستخدم موجود بالفعل</div>';
                    return;
                }

                // Create new user
                const newUser = {
                    id: db.users.length + 1,
                    username: username,
                    password: password, // كلمة مرور عادية
                    role: 'student',
                    name: name,
                    email: '<EMAIL>',
                    phone: '0500000000',
                    schoolId: 1,
                    grade: '1',
                    noorId: `${Date.now()}`,
                    balance: 0
                };

                db.users.push(newUser);
                saveDatabase(db);

                resultDiv.innerHTML += '<div class="success">✓ تم تسجيل المستخدم بنجاح</div>';

                // Step 2: Verify user was saved
                resultDiv.innerHTML += '<div class="step">الخطوة 2: التحقق من حفظ المستخدم...</div>';
                
                const savedDb = getDatabase();
                const savedUser = savedDb.users.find(u => u.username === username);
                
                if (!savedUser) {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم العثور على المستخدم في قاعدة البيانات</div>';
                    return;
                }
                
                resultDiv.innerHTML += `<div class="success">✓ المستخدم محفوظ - كلمة المرور: "${savedUser.password}"</div>`;

                // Step 3: Test login
                resultDiv.innerHTML += '<div class="step">الخطوة 3: اختبار تسجيل الدخول...</div>';
                
                const user = savedDb.users.find(u => u.username === username && u.role === 'student');
                if (!user) {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم العثور على المستخدم للدخول</div>';
                    return;
                }

                const passwordMatch = (user.password === password);
                
                if (passwordMatch) {
                    resultDiv.innerHTML += '<div class="success">✓ تم تسجيل الدخول بنجاح!</div>';
                    resultDiv.innerHTML += '<div class="success"><strong>🎉 الاختبار نجح بالكامل!</strong></div>';
                    resultDiv.innerHTML += `<div class="info">المستخدم: ${user.name} (${user.username})</div>`;
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المحفوظة: "${user.password}"</div>`;
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المدخلة: "${password}"</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="error">✗ فشل تسجيل الدخول - كلمة المرور غير صحيحة</div>';
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المحفوظة: "${user.password}"</div>`;
                    resultDiv.innerHTML += `<div class="info">كلمة المرور المدخلة: "${password}"</div>`;
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في الاختبار: ${error.message}</div>`;
                console.error('Test error:', error);
            }
        }

        function showCurrentUsers() {
            const resultDiv = document.getElementById('users-result');
            try {
                const db = getDatabase();
                if (!db || !db.users) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let html = '<div class="info">المستخدمون في النظام:</div>';
                db.users.forEach(user => {
                    html += `
                        <div class="info">
                            ${user.name} (${user.username}) - ${user.role} - كلمة المرور: "${user.password}"
                        </div>
                    `;
                });
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في عرض المستخدمين: ${error.message}</div>`;
            }
        }

        function testExistingLogin() {
            const resultDiv = document.getElementById('existing-result');
            const username = document.getElementById('existing-username').value;
            const password = document.getElementById('existing-password').value;
            const role = document.getElementById('existing-role').value;

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }

            try {
                const db = getDatabase();
                const user = db.users.find(u => u.username === username && u.role === role);
                
                if (!user) {
                    resultDiv.innerHTML = '<div class="error">✗ المستخدم غير موجود</div>';
                    return;
                }

                const passwordMatch = (user.password === password);
                
                if (passwordMatch) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ تم تسجيل الدخول بنجاح</div>
                        <div class="info">المستخدم: ${user.name} (${user.username})</div>
                        <div class="info">الدور: ${user.role}</div>
                        <div class="info">كلمة المرور المحفوظة: "${user.password}"</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ كلمة المرور غير صحيحة</div>
                        <div class="info">كلمة المرور المحفوظة: "${user.password}"</div>
                        <div class="info">كلمة المرور المدخلة: "${password}"</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تسجيل الدخول: ${error.message}</div>`;
            }
        }

        function resetDatabase() {
            const resultDiv = document.getElementById('reset-result');
            try {
                localStorage.removeItem('cafeteriaDB');
                resultDiv.innerHTML = '<div class="success">✓ تم إعادة تعيين قاعدة البيانات. يرجى تحديث الصفحة.</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين قاعدة البيانات: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
