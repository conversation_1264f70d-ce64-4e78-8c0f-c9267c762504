/*
 * فواصل النجاح للخدمات الإعاشة
 * أنماط الصفحة الرئيسية
 */

/* تعديلات الشريط العلوي للصفحة الرئيسية */
.header {
    padding: 15px 0;
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    background-color: var(--white-color);
}

/* تأثير التمرير على الشريط العلوي */
.header.scrolled {
    padding: 5px 0;
    box-shadow: var(--shadow-lg);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header.scrolled .container {
    height: 70px; /* تقليل ارتفاع الحاوية عند التمرير */
}

.header.scrolled .logo {
    padding: 4px 8px;
    height: 60px; /* تقليل ارتفاع الشعار عند التمرير */
}

.header.scrolled .logo-img {
    width: 70px;
    height: 70px;
}

.header.scrolled .logo h1 {
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .header.scrolled .container {
        height: 60px; /* تقليل ارتفاع الحاوية عند التمرير للشاشات الصغيرة */
    }

    .header.scrolled .logo {
        height: 50px; /* تقليل ارتفاع الشعار عند التمرير للشاشات الصغيرة */
    }

    .header.scrolled .logo-img {
        width: 45px;
        height: 45px;
    }

    .header.scrolled .logo h1 {
        font-size: 1.05rem;
    }
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 90px; /* تحديد ارتفاع ثابت للحاوية */
}

/* تنسيق القائمة الرئيسية */
.main-nav {
    height: 100%; /* جعل القائمة بارتفاع كامل الحاوية */
    display: flex;
    align-items: center;
}

.main-nav ul {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    height: 100%; /* جعل القائمة بارتفاع كامل الحاوية */
}

.main-nav ul li {
    margin-right: 20px;
    list-style: none;
    height: 100%; /* جعل عناصر القائمة بارتفاع كامل */
    display: flex;
    align-items: center;
}

.main-nav ul li:last-child {
    margin-right: 0;
}

.main-nav ul li a {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    height: 36px; /* تحديد ارتفاع ثابت للروابط */
    font-size: 0.95rem;
}

.main-nav ul li a:hover,
.main-nav ul li a.active {
    color: var(--primary-color);
    background-color: rgba(46, 125, 50, 0.05);
}

.main-nav ul li a.btn {
    padding: 0 20px;
    height: 36px;
    justify-content: center;
}

/* تعديلات متجاوبة للقائمة */
@media (max-width: 992px) {
    .main-nav ul li {
        margin-right: 12px;
    }

    .main-nav ul li a {
        font-size: 0.9rem;
        padding: 6px 10px;
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--primary-color);
        background: none;
        border: none;
        padding: 0;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .menu-toggle:hover {
        transform: scale(1.1);
        background-color: rgba(46, 125, 50, 0.05);
    }

    .main-nav {
        position: fixed;
        top: 90px; /* تعديل المسافة من الأعلى لتتناسب مع ارتفاع الشريط العلوي */
        right: -100%;
        width: 80%;
        height: calc(100vh - 90px);
        background-color: var(--white-color);
        box-shadow: var(--shadow);
        transition: all 0.4s ease;
        z-index: 99;
        padding: 20px;
        overflow-y: auto;
        display: block; /* تغيير طريقة العرض للقائمة المتنقلة */
    }

    .main-nav.active {
        right: 0;
    }

    .main-nav ul {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-nav ul li {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
        height: auto;
        display: block;
    }

    .main-nav ul li a {
        padding: 10px 15px;
        width: 100%;
        display: block;
        font-size: 0.9rem;
        height: auto;
    }

    .main-nav ul li a.btn {
        width: 100%;
        text-align: center;
        height: auto;
        padding: 10px 15px;
        display: flex;
        justify-content: center;
    }
}

/* قسم الترحيب */
.hero-section {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: url('../images/hero-bg.svg') no-repeat center center;
    background-size: cover;
    color: white;
    padding: 50px 0;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 15px;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.hero-btn-primary {
    background-color: #f9a825;
    color: #1b5e20;
    border: 2px solid #f9a825;
}

.hero-btn-primary:hover {
    background-color: #fbc02d;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.hero-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-btn i {
    margin-left: 10px;
}

.hero-image {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 50%;
    max-width: 600px;
    animation: floatImage 6s ease-in-out infinite;
}

/* قسم الميزات */
.features-section {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern.svg') repeat;
    opacity: 0.05;
    z-index: 1;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
}

.section-title h2 {
    font-size: 2.5rem;
    color: #2e7d32;
    margin-bottom: 15px;
}

.section-title p {
    font-size: 1.2rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 2;
}

.feature-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #2e7d32, #f9a825);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.feature-card:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
}

.feature-title {
    font-size: 1.5rem;
    color: #2e7d32;
    margin-bottom: 15px;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

/* قسم الوجبات */
.meals-section {
    padding: 80px 0;
    background-color: white;
}

.meals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.meal-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.meal-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(46, 125, 50, 0.2);
}

.meal-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #2e7d32, #f9a825);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.4s ease;
}

.meal-card:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.meal-image {
    width: 100%;
    height: 200px;
    object-fit: contain;
    padding: 15px;
    background-color: #f9f9f9;
    transition: all 0.4s ease;
}

.meal-card:hover .meal-image {
    transform: scale(1.05);
    background-color: #f5f5f5;
}

.meal-content {
    padding: 25px;
    position: relative;
}

.meal-title {
    font-size: 1.6rem;
    color: #2e7d32;
    margin-bottom: 12px;
    position: relative;
    display: inline-block;
}

.meal-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: #f9a825;
    transition: width 0.3s ease;
}

.meal-card:hover .meal-title::after {
    width: 100%;
}

.meal-description {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.7;
    font-size: 1.05rem;
}

.meal-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #f9a825;
    display: block;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.meal-btn {
    display: inline-block;
    padding: 10px 25px;
    background-color: #2e7d32;
    color: white;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(46, 125, 50, 0.2);
}

.meal-btn:hover {
    background-color: #1b5e20;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

/* قسم عن المقصف */
.about-section {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern.svg') repeat;
    opacity: 0.05;
    z-index: 1;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
    position: relative;
    z-index: 2;
}

.about-image {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
}

.about-text {
    flex: 1;
}

.about-text h2 {
    font-size: 2.5rem;
    color: #2e7d32;
    margin-bottom: 20px;
}

.about-text p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #f9a825;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 1rem;
}

/* قسم الاتصال */
.contact-section {
    padding: 80px 0;
    background-color: white;
}

.contact-content {
    display: flex;
    gap: 50px;
}

.contact-info {
    flex: 1;
}

.contact-info h2 {
    font-size: 2.5rem;
    color: #2e7d32;
    margin-bottom: 20px;
}

.contact-info p {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.8;
}

.contact-details {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background-color: #e8f5e9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2e7d32;
    margin-left: 15px;
}

.contact-text {
    color: #666;
}

.contact-form {
    flex: 1;
    background-color: #f9f9f9;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #2e7d32;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #2e7d32;
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
}

.form-btn {
    background-color: #2e7d32;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-btn:hover {
    background-color: #1b5e20;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* التحريكات */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatImage {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* تجاوب الموقع */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-image {
        width: 40%;
    }

    .about-content {
        flex-direction: column;
    }

    .contact-content {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }

    .hero-content {
        max-width: 100%;
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-image {
        display: none;
    }

    .about-stats {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .section-title h2 {
        font-size: 2rem;
    }
}
