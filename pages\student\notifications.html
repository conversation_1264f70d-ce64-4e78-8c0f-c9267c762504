<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 8px 0;
            min-height: 50px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* لوجو الشوكة والسكينة */
        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 10px;
            color: white;
            font-size: 1.4rem;
            box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .compact-header .logo h1 {
            font-size: 1.1rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .compact-header .user-menu span {
            font-size: 0.9rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        .dashboard {
            display: flex;
            min-height: calc(100vh - 50px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: sticky;
            top: 70px;
            height: calc(100vh - 70px);
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
            transform: translateX(-5px);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
            transition: all 0.3s ease;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background-color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .page-header h1 i {
            margin-left: 10px;
            font-size: 1.5rem;
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-card h2 button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .content-card h2 button i {
            margin-left: 5px;
        }

        .content-card h2 button:hover {
            background-color: var(--dark-color);
            transform: translateY(-2px);
        }

        .notifications-list {
            list-style: none;
        }

        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item:hover {
            background-color: #f9f9f9;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(46, 125, 50, 0.1);
            color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .notification-item:hover .notification-icon {
            transform: scale(1.1);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-date {
            color: #999;
            font-size: 0.8rem;
        }

        .notification-message {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .notification-unread {
            background-color: rgba(46, 125, 50, 0.05);
            position: relative;
        }

        .notification-unread::before {
            content: '';
            position: absolute;
            top: 15px;
            right: -5px;
            width: 10px;
            height: 10px;
            background-color: var(--primary-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .notification-unread .notification-title {
            color: var(--primary-color);
        }

        .notification-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }

        .notification-actions button {
            background-color: transparent;
            border: 1px solid #ddd;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .notification-actions button i {
            margin-left: 5px;
        }

        .notification-actions button:hover {
            background-color: #f5f5f5;
            transform: translateY(-2px);
        }

        .mark-read-btn {
            color: var(--primary-color);
        }

        .delete-btn {
            color: #e74c3c;
        }

        .empty-state {
            text-align: center;
            padding: 50px 30px;
            color: #666;
            animation: fadeIn 1s ease;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .empty-state p {
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(46, 125, 50, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 125, 50, 0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .sidebar {
                width: 200px;
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
            }

            .sidebar-menu {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }

            .sidebar-menu li {
                margin: 0 5px;
            }

            .sidebar-menu li a {
                padding: 10px 15px;
                border-right: none;
                border-bottom: 3px solid transparent;
            }

            .sidebar-menu li a:hover,
            .sidebar-menu li a.active {
                border-right: none;
                border-bottom: 3px solid var(--secondary-color);
                transform: translateY(-3px);
            }

            .logout-btn {
                width: auto;
                margin: 20px auto;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .page-header .date {
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header compact-header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>أحمد محمد</strong></span>
                <button id="header-logout-btn" class="header-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>الطالب</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="notifications.html" class="active"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-bell"></i> الإشعارات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card animate__animated animate__fadeIn">
                <h2>
                    قائمة الإشعارات
                    <button id="mark-all-read-btn"><i class="fas fa-check-double"></i> تعيين الكل كمقروء</button>
                </h2>
                <ul class="notifications-list" id="notifications-list">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </ul>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is student
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'student') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Get notifications
            const notifications = db.notifications.filter(notification => notification.userId === currentUser.id);

            // Populate notifications list
            const notificationsList = document.getElementById('notifications-list');

            if (notifications.length === 0) {
                notificationsList.innerHTML = `
                    <div class="empty-state">
                        <i class="far fa-bell-slash"></i>
                        <p>لا توجد إشعارات جديدة.</p>
                        <p class="text-muted">ستظهر هنا الإشعارات المتعلقة بطلباتك وتغييرات الرصيد.</p>
                    </div>
                `;
            } else {
                // Sort notifications by date (newest first) and read status
                notifications.sort((a, b) => {
                    // First sort by read status (unread first)
                    if (a.isRead !== b.isRead) {
                        return a.isRead ? 1 : -1;
                    }
                    // Then sort by date
                    return new Date(b.date) - new Date(a.date);
                });

                notifications.forEach((notification, index) => {
                    // Add delay for staggered animation
                    setTimeout(() => {
                        const notificationItem = document.createElement('li');
                        notificationItem.className = 'notification-item' + (notification.isRead ? '' : ' notification-unread');
                        notificationItem.setAttribute('data-id', notification.id);

                        let iconClass = 'fas fa-bell';
                        if (notification.title.includes('طلب')) {
                            iconClass = 'fas fa-shopping-cart';
                        } else if (notification.title.includes('رصيد')) {
                            iconClass = 'fas fa-wallet';
                        }

                        notificationItem.innerHTML = `
                            <div class="notification-icon">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">
                                    <span>${notification.title}</span>
                                    <span class="notification-date">${notification.date}</span>
                                </div>
                                <div class="notification-message">${notification.message}</div>
                                <div class="notification-actions">
                                    ${!notification.isRead ?
                                        `<button class="mark-read-btn" data-id="${notification.id}"><i class="fas fa-check"></i> تعيين كمقروء</button>` :
                                        ''}
                                    <button class="delete-btn" data-id="${notification.id}"><i class="fas fa-trash"></i> حذف</button>
                                </div>
                            </div>
                        `;
                        notificationsList.appendChild(notificationItem);

                        // Add fade-in animation
                        notificationItem.style.opacity = '0';
                        notificationItem.style.transform = 'translateY(10px)';

                        setTimeout(() => {
                            notificationItem.style.opacity = '1';
                            notificationItem.style.transform = 'translateY(0)';
                        }, 50);
                    }, index * 100); // Stagger the animations
                });

                // Add event listeners after all items are added
                setTimeout(() => {
                    // Mark as read button
                    document.querySelectorAll('.mark-read-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const notificationId = parseInt(this.getAttribute('data-id'));

                            // Update notification in database
                            const notificationIndex = db.notifications.findIndex(n => n.id === notificationId);
                            if (notificationIndex !== -1) {
                                db.notifications[notificationIndex].isRead = true;
                                saveDatabase(db);

                                // Update UI
                                const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
                                notificationItem.classList.remove('notification-unread');
                                this.remove();

                                // Show success message
                                showToast('تم تعيين الإشعار كمقروء');
                            }
                        });
                    });

                    // Delete button
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const notificationId = parseInt(this.getAttribute('data-id'));

                            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                                // Remove notification from database
                                const notificationIndex = db.notifications.findIndex(n => n.id === notificationId);
                                if (notificationIndex !== -1) {
                                    db.notifications.splice(notificationIndex, 1);
                                    saveDatabase(db);

                                    // Remove from UI with animation
                                    const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
                                    notificationItem.style.opacity = '0';
                                    notificationItem.style.transform = 'translateY(10px)';

                                    setTimeout(() => {
                                        notificationItem.remove();

                                        // Check if list is empty
                                        if (notificationsList.children.length === 0) {
                                            notificationsList.innerHTML = `
                                                <div class="empty-state">
                                                    <i class="far fa-bell-slash"></i>
                                                    <p>لا توجد إشعارات جديدة.</p>
                                                    <p class="text-muted">ستظهر هنا الإشعارات المتعلقة بطلباتك وتغييرات الرصيد.</p>
                                                </div>
                                            `;
                                        }
                                    }, 300);

                                    // Show success message
                                    showToast('تم حذف الإشعار بنجاح');
                                }
                            }
                        });
                    });
                }, notifications.length * 100 + 100);
            }

            // Mark all as read button
            document.getElementById('mark-all-read-btn').addEventListener('click', function() {
                // Update all notifications in database
                let hasUnread = false;
                db.notifications.forEach(notification => {
                    if (notification.userId === currentUser.id && !notification.isRead) {
                        notification.isRead = true;
                        hasUnread = true;
                    }
                });

                if (hasUnread) {
                    saveDatabase(db);

                    // Update UI
                    document.querySelectorAll('.notification-item').forEach(item => {
                        item.classList.remove('notification-unread');
                    });

                    document.querySelectorAll('.mark-read-btn').forEach(btn => {
                        btn.remove();
                    });

                    // Show success message
                    showToast('تم تعيين جميع الإشعارات كمقروءة');
                } else {
                    showToast('لا توجد إشعارات غير مقروءة', 'warning');
                }
            });

            // Logout button - Sidebar
            document.getElementById('logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });

            // Logout button - Header
            document.getElementById('header-logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });

            // Function to show toast notification
            function showToast(message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type} animate__animated animate__fadeInUp`;
                toast.innerHTML = `
                    <div class="toast-content">
                        <i class="${type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.classList.remove('animate__fadeInUp');
                    toast.classList.add('animate__fadeOutDown');
                    setTimeout(() => {
                        toast.remove();
                    }, 500);
                }, 3000);
            }

            // Add toast styles
            const style = document.createElement('style');
            style.textContent = `
                .toast {
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    background-color: white;
                    color: #333;
                    padding: 12px 20px;
                    border-radius: 5px;
                    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    min-width: 250px;
                }

                .toast-content {
                    display: flex;
                    align-items: center;
                }

                .toast-content i {
                    margin-left: 10px;
                    font-size: 1.2rem;
                }

                .toast-success i {
                    color: var(--primary-color);
                }

                .toast-warning i {
                    color: #f39c12;
                }

                .toast-error i {
                    color: #e74c3c;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
