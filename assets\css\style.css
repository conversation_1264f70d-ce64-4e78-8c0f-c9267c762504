/*
* فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
* Main Stylesheet
*/

/* ===== Base Styles ===== */
:root {
    /* الألوان الأساسية */
    --primary-color: #2e7d32;
    --secondary-color: #f9a825;
    --dark-color: #1b5e20;
    --light-color: #e8f5e9;
    --gray-color: #f5f5f5;
    --text-color: #333;
    --white-color: #fff;
    --background-color: #ffffff;
    --surface-color: #f5f5f5;

    /* الظلال والانتقالات */
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 15px 30px rgba(0, 0, 0, 0.2);

    /* الانتقالات والتأثيرات الحركية */
    --transition: all 0.3s ease;
    --transition-speed: 0.3s;
    --transition-function: ease;
    --animation-speed: 1;

    /* الخطوط */
    --main-font: 'Tajawal';
    --base-font-size: 16px;
    --base-font-weight: 400;

    /* نصف قطر الحواف */
    --border-radius-sm: 5px;
    --border-radius-md: 10px;
    --border-radius-lg: 15px;
    --border-radius-xl: 20px;
    --border-radius-full: 9999px;

    /* ألوان إضافية */
    --beta-color: #ff5722;
    --beta-dark-color: #e64a19;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--main-font), sans-serif;
    font-size: var(--base-font-size);
    font-weight: var(--base-font-weight);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    direction: rtl;
    padding-top: 40px; /* إضافة مساحة للشريط التجريبي */
    transition: background-color 0.5s ease, color 0.5s ease;
}

/* شريط تجريبي */
.beta-banner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(45deg, var(--beta-dark-color), var(--beta-color));
    color: white;
    text-align: center;
    padding: 8px 0;
    font-weight: bold;
    z-index: 2000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

.beta-banner span {
    display: inline-block;
    padding: 2px 10px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin: 0 5px;
    font-size: 0.9rem;
    transform: rotate(-2deg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
}

a {
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
}

.section-header p {
    color: #666;
    font-size: 1.1rem;
}

.btn {
    display: inline-block;
    padding: 10px 25px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: var(--dark-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #f57f17;
}

/* ===== Header Styles ===== */
.header {
    background-color: var(--white-color);
    box-shadow: var(--shadow);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ===== أنماط الشعار ===== */
.logo {
    display: flex;
    align-items: center;
    position: relative;
    padding: 6px 10px;
    border-radius: 12px;
    transition: all 0.4s ease;
    background-color: rgba(46, 125, 50, 0.03);
    border: 1px solid transparent;
    margin-left: 15px;
    height: 70px; /* تحديد ارتفاع ثابت للشعار */
}

.logo:hover {
    background-color: rgba(46, 125, 50, 0.08);
    border-color: rgba(46, 125, 50, 0.15);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-img {
    width: 60px;
    height: 60px;
    margin-left: 15px;
    transition: all 0.5s ease;
    filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.25));
    border-radius: 50%;
    background-color: white;
    padding: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    object-fit: contain;
    vertical-align: middle; /* تحسين المحاذاة الرأسية */
}

.logo:hover .logo-img {
    transform: rotate(15deg) scale(1.1);
    filter: drop-shadow(0 8px 15px rgba(0, 0, 0, 0.35));
    box-shadow: 0 8px 20px rgba(46, 125, 50, 0.25);
}

.logo h1 {
    font-size: 1.4rem;
    color: var(--primary-color);
    position: relative;
    transition: all 0.4s ease;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
    font-weight: 600;
    letter-spacing: -0.2px;
    line-height: 1.2; /* تحسين ارتفاع السطر */
    vertical-align: middle; /* تحسين المحاذاة الرأسية */
    display: inline-block; /* تحسين المحاذاة */
}

.logo:hover h1 {
    color: var(--dark-color);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* تعديلات متجاوبة للشعار */
@media (max-width: 992px) {
    .logo-img {
        width: 65px;
        height: 65px;
        margin-left: 15px;
    }

    .logo h1 {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .logo-img {
        width: 55px;
        height: 55px;
        margin-left: 12px;
    }

    .logo h1 {
        font-size: 1.15rem;
    }
}

@media (max-width: 576px) {
    .logo-img {
        width: 45px;
        height: 45px;
        margin-left: 8px;
    }

    .logo h1 {
        font-size: 1rem;
    }
}

/* تم نقل أنماط القائمة الرئيسية إلى ملف home.css للصفحة الرئيسية */
/* وتم الاحتفاظ بالأنماط الأساسية هنا للصفحات الأخرى */

.main-nav ul {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

.main-nav ul li {
    margin-right: 20px;
    list-style: none;
}

.main-nav ul li:last-child {
    margin-right: 0;
}

.main-nav ul li a {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    color: var(--text-color);
    text-decoration: none;
    display: block;
}

.main-nav ul li a:hover,
.main-nav ul li a.active {
    color: var(--primary-color);
    background-color: rgba(46, 125, 50, 0.05);
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-color);
    background: none;
    border: none;
    padding: 5px;
    transition: all 0.3s ease;
}

/* زر تبديل الوضع الليلي/النهاري */
.dark-mode-toggle {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 15px;
}

.dark-mode-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.dark-mode-toggle:active {
    transform: scale(0.95);
}

.user-menu {
    display: flex;
    align-items: center;
}

#user-name {
    font-size: 0.95rem;
    font-weight: 500;
}

#user-name strong {
    font-weight: 600;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    #user-name {
        font-size: 0.85rem;
    }
}

/* ===== Hero Section ===== */
.hero {
    padding: 80px 0;
    background-color: var(--light-color);
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 1;
}

.hero-content h2 {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #555;
}

.hero-buttons .btn {
    margin-left: 15px;
}

.hero-image {
    flex: 1;
    text-align: center;
}

/* ===== About Section ===== */
.about {
    padding: 80px 0;
    background-color: var(--white-color);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-image {
    flex: 1;
}

.about-text {
    flex: 1;
}

.about-text h3 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.about-text p {
    margin-bottom: 20px;
}

.feature-list li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.feature-list li i {
    color: var(--primary-color);
    margin-left: 10px;
}

/* ===== Services Section ===== */
.services {
    padding: 80px 0;
    background-color: var(--gray-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: var(--white-color);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* ===== Contact Section ===== */
.contact {
    padding: 80px 0;
    background-color: var(--white-color);
}

.contact-content {
    display: flex;
    gap: 50px;
}

.contact-form {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Tajawal', sans-serif;
}

.contact-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.info-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.info-item h3 {
    font-size: 1.3rem;
    margin-bottom: 5px;
    color: var(--primary-color);
}

/* ===== Footer Section ===== */
.footer {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-logo h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.footer-links h3,
.footer-social h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    position: relative;
}

.footer-links h3::after,
.footer-social h3::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 2px;
    background-color: var(--secondary-color);
    bottom: -10px;
    right: 0;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #ccc;
}

.footer-links ul li a:hover {
    color: var(--secondary-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--secondary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* معلومات المطور */
.developer-info {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    transition: all 0.3s ease;
}

.developer-info:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.developer-info-text {
    display: flex;
    align-items: center;
    gap: 10px;
}

.developer-info i {
    color: var(--secondary-color);
    font-size: 1.2rem;
}

.developer-info a {
    color: var(--white-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
}

.developer-info a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* ===== Responsive Styles ===== */
@media (max-width: 992px) {
    .hero-content h2 {
        font-size: 2.5rem;
    }

    .about-content,
    .contact-content {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .main-nav {
        position: fixed;
        top: 80px;
        right: -100%;
        width: 80%;
        height: calc(100vh - 80px);
        background-color: var(--white-color);
        box-shadow: var(--shadow);
        transition: all 0.4s ease;
        z-index: 99;
        padding: 20px;
        overflow-y: auto;
    }

    .main-nav.active {
        right: 0;
    }

    .main-nav ul {
        flex-direction: column;
        align-items: flex-start;
        padding: 0;
    }

    .main-nav ul li {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }

    .main-nav ul li a {
        padding: 12px 15px;
        width: 100%;
        display: block;
    }

    .main-nav ul li a.btn {
        width: 100%;
        text-align: center;
    }

    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        margin-bottom: 40px;
    }

    .section-header h2 {
        font-size: 2rem;
    }
}
