<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 6px 0;
            min-height: 45px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* لوجو الشوكة والسكينة */
        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 8px;
            color: white;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .compact-header .logo h1 {
            font-size: 1rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .compact-header .user-menu span {
            font-size: 0.85rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 18px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        .dashboard {
            display: flex;
            min-height: calc(100vh - 45px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .balance-card {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .balance-info h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .balance-info p {
            font-size: 2rem;
            font-weight: 700;
        }

        .balance-actions button {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .balance-actions button i {
            margin-left: 5px;
        }

        .balance-actions button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group label {
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px 10px;
        }

        .search-box input {
            border: none;
            padding: 5px;
            font-family: 'Tajawal', sans-serif;
            width: 200px;
        }

        .search-box input:focus {
            outline: none;
        }

        .search-box i {
            color: #666;
        }

        /* شبكة المنتجات البسيطة */
        .simple-products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }

        .simple-product-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            min-height: 220px;
        }

        .simple-product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(46, 125, 50, 0.1) 100%);
        }

        .simple-product-card.out-of-stock {
            opacity: 0.6;
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
        }

        .simple-product-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
            transition: all 0.3s ease;
        }

        .simple-product-card:hover .simple-product-icon {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }

        .simple-product-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
            line-height: 1.3;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .simple-product-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--secondary-color);
            background: rgba(249, 168, 37, 0.1);
            padding: 5px 12px;
            border-radius: 15px;
            display: inline-block;
            margin-bottom: 8px;
        }

        .simple-product-stock {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-bottom: 12px;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: center;
        }

        .preorder-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            box-shadow: 0 3px 10px rgba(46, 125, 50, 0.3);
            flex: 1;
        }

        .preorder-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
        }

        .preorder-btn:active {
            transform: translateY(0);
        }

        .preorder-btn.out-of-stock {
            background: #ccc;
            cursor: not-allowed;
            box-shadow: none;
        }

        .preorder-btn.out-of-stock:hover {
            transform: none;
            box-shadow: none;
        }

        .preorder-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            box-shadow: none;
        }

        /* زر الحساسية */
        .allergy-btn {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 50%;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }

        .allergy-btn:hover {
            background: linear-gradient(135deg, #f57c00, #ef6c00);
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        .allergy-btn:active {
            transform: translateY(0) scale(1.05);
        }

        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 8px 0;
            min-height: 50px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo h1 {
            font-size: 1.2rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .compact-header .user-menu span {
            font-size: 0.9rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        /* تعديل ارتفاع لوحة التحكم */
        .dashboard {
            min-height: calc(100vh - 50px);
        }

        /* نافذة الطلب المسبق */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            padding: 15px 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .close {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: scale(1.2);
            opacity: 0.8;
        }

        .modal-body {
            padding: 20px;
        }

        .selected-product {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .product-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .product-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .product-details h4 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .product-details p {
            margin: 0;
            color: var(--secondary-color);
            font-weight: 600;
        }

        .quantity-selector {
            margin-bottom: 20px;
        }

        .quantity-selector label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 10px;
        }

        .quantity-controls button {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-controls button:hover {
            background: var(--dark-color);
            transform: scale(1.1);
        }

        .quantity-controls span {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            min-width: 30px;
            text-align: center;
        }

        .total-price {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), rgba(249, 168, 37, 0.1));
            border-radius: 10px;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .modal-actions {
            display: flex;
            gap: 10px;
        }

        .modal-actions button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .confirm-btn {
            background: var(--primary-color);
            color: white;
        }

        .confirm-btn:hover {
            background: var(--dark-color);
            transform: translateY(-2px);
        }

        .cancel-btn {
            background: #f44336;
            color: white;
        }

        .cancel-btn:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .filter-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .search-box {
                width: 100%;
            }

            .search-box input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header compact-header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>أحمد محمد</strong></span>
                <button id="header-logout-btn" class="header-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>الطالب</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="products.html" class="active"><i class="fas fa-store"></i> المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="profile.html"><i class="fas fa-user"></i> الملف الشخصي</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>المنتجات المتاحة</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="balance-card">
                <div class="balance-info">
                    <h3>رصيدك الحالي</h3>
                    <p id="student-balance">100 ريال</p>
                </div>
                <div class="balance-actions">
                    <button id="add-balance-btn"><i class="fas fa-plus"></i> إضافة رصيد</button>
                </div>
            </div>

            <div class="content-card">
                <h2><i class="fas fa-utensils"></i> المنتجات المتاحة</h2>

                <div class="simple-products-grid" id="simple-products-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>

            <!-- نافذة الطلب المسبق -->
            <div id="preorder-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-clock"></i> طلب مسبق</h3>
                        <span class="close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="selected-product">
                            <div class="product-info">
                                <div class="product-icon" id="modal-product-icon">🍔</div>
                                <div class="product-details">
                                    <h4 id="modal-product-name">اسم المنتج</h4>
                                    <p id="modal-product-price">0 ريال</p>
                                </div>
                            </div>
                        </div>

                        <div class="quantity-selector">
                            <label>الكمية:</label>
                            <div class="quantity-controls">
                                <button type="button" id="decrease-qty">-</button>
                                <span id="quantity-display">1</span>
                                <button type="button" id="increase-qty">+</button>
                            </div>
                        </div>

                        <div class="total-price">
                            <strong>المجموع: <span id="total-price">0</span> ريال</strong>
                        </div>

                        <div class="modal-actions">
                            <button id="confirm-preorder" class="confirm-btn">
                                <i class="fas fa-check"></i> تأكيد الطلب
                            </button>
                            <button id="cancel-preorder" class="cancel-btn">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/notifications.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is student
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'student') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Update balance
            document.getElementById('student-balance').textContent = (currentUser.balance || 0) + ' ريال';

            // منتجات المقصف (جميع المنتجات من صفحة العاملين)
            const canteenProducts = [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, icon: '🥙', isPopular: true },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, icon: '🧀', isPopular: false },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, icon: '🐟', isPopular: false },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, icon: '🍗', isPopular: false },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, icon: '🥗', isPopular: false },
                { id: 6, name: 'برجر لحم', price: 12.00, category: 'meals', stock: 6, icon: '🍔', isPopular: true },
                { id: 7, name: 'برجر دجاج', price: 11.00, category: 'meals', stock: 7, icon: '🍔', isPopular: false },
                { id: 8, name: 'بيتزا صغيرة', price: 15.00, category: 'meals', stock: 5, icon: '🍕', isPopular: true },
                { id: 9, name: 'مرامي قشار بطل افخاذ', price: 13.50, category: 'meals', stock: 4, icon: '🥙', isPopular: false },
                { id: 10, name: 'شاورما دجاج', price: 11.50, category: 'meals', stock: 9, icon: '🌯', isPopular: false },

                // المشروبات
                { id: 11, name: 'عصير برتقال طازج', price: 4.50, category: 'drinks', stock: 20, icon: '🍊', isPopular: true },
                { id: 12, name: 'عصير تفاح', price: 4.00, category: 'drinks', stock: 18, icon: '🍎', isPopular: false },
                { id: 13, name: 'عصير مانجو', price: 5.00, category: 'drinks', stock: 15, icon: '🥭', isPopular: false },
                { id: 14, name: 'عصير فراولة', price: 4.50, category: 'drinks', stock: 12, icon: '🍓', isPopular: false },
                { id: 15, name: 'ماء معدني', price: 1.50, category: 'drinks', stock: 50, icon: '💧', isPopular: true },
                { id: 16, name: 'مشروب غازي', price: 3.00, category: 'drinks', stock: 25, icon: '🥤', isPopular: false },
                { id: 17, name: 'شاي مثلج', price: 3.50, category: 'drinks', stock: 14, icon: '🧊', isPopular: false },
                { id: 18, name: 'قهوة باردة', price: 4.00, category: 'drinks', stock: 10, icon: '☕', isPopular: false },
                { id: 19, name: 'لبن رائب', price: 2.50, category: 'drinks', stock: 16, icon: '🥛', isPopular: false },
                { id: 20, name: 'عصير ليمون', price: 3.50, category: 'drinks', stock: 13, icon: '🍋', isPopular: false },

                // الوجبات الخفيفة
                { id: 21, name: 'فشار بالزبدة', price: 3.50, category: 'snacks', stock: 22, icon: '🍿', isPopular: true },
                { id: 22, name: 'شيبس بطاطس', price: 2.50, category: 'snacks', stock: 30, icon: '🥔', isPopular: false },
                { id: 23, name: 'مكسرات مشكلة', price: 5.50, category: 'snacks', stock: 18, icon: '🥜', isPopular: false },
                { id: 24, name: 'بسكويت شوكولاتة', price: 3.00, category: 'snacks', stock: 25, icon: '🍪', isPopular: false },
                { id: 25, name: 'كراكرز جبن', price: 2.00, category: 'snacks', stock: 20, icon: '🧀', isPopular: false },

                // الحلويات
                { id: 26, name: 'كيك شوكولاتة', price: 6.00, category: 'sweets', stock: 8, icon: '🧁', isPopular: true },
                { id: 27, name: 'دونات محشي', price: 4.50, category: 'sweets', stock: 12, icon: '🍩', isPopular: false },
                { id: 28, name: 'آيس كريم', price: 5.50, category: 'sweets', stock: 15, icon: '🍦', isPopular: true },
                { id: 29, name: 'شوكولاتة', price: 3.50, category: 'sweets', stock: 20, icon: '🍫', isPopular: false },
                { id: 30, name: 'حلوى عربية', price: 7.00, category: 'sweets', stock: 6, icon: '🍯', isPopular: false }
            ];

            // عرض المنتجات
            const simpleProductsGrid = document.getElementById('simple-products-grid');
            let selectedProduct = null;
            let currentQuantity = 1;

            function renderSimpleProducts() {
                simpleProductsGrid.innerHTML = '';

                canteenProducts.forEach(product => {
                    const productCard = document.createElement('div');
                    productCard.className = 'simple-product-card';

                    // تحديد حالة المخزون
                    const isOutOfStock = product.stock === 0;
                    const stockClass = isOutOfStock ? 'out-of-stock' : '';

                    productCard.innerHTML = `
                        <div class="simple-product-icon">${product.icon}</div>
                        <div class="simple-product-name">${product.name}</div>
                        <div class="simple-product-price">${product.price.toFixed(2)} ريال</div>
                        <div class="simple-product-stock">متوفر: ${product.stock}</div>
                        <div class="product-actions">
                            <button class="preorder-btn ${stockClass}" ${isOutOfStock ? 'disabled' : ''} onclick="openPreorderModal(${product.id})">
                                <i class="fas fa-clock"></i>
                                ${isOutOfStock ? 'غير متوفر' : 'طلب مسبق'}
                            </button>
                            <button class="allergy-btn" onclick="showAllergyInfo(${product.id})" title="معلومات الحساسية">
                                <i class="fas fa-exclamation-triangle"></i>
                            </button>
                        </div>
                    `;

                    simpleProductsGrid.appendChild(productCard);
                });
            }

            // نافذة الطلب المسبق
            const modal = document.getElementById('preorder-modal');
            const closeBtn = document.querySelector('.close');
            const decreaseBtn = document.getElementById('decrease-qty');
            const increaseBtn = document.getElementById('increase-qty');
            const quantityDisplay = document.getElementById('quantity-display');
            const totalPriceDisplay = document.getElementById('total-price');
            const confirmBtn = document.getElementById('confirm-preorder');
            const cancelBtn = document.getElementById('cancel-preorder');

            // تحديث دالة فتح نافذة الطلب المسبق
            window.openPreorderModal = function(productId) {
                const product = canteenProducts.find(p => p.id === productId);
                if (!product) return;

                // فحص المخزون
                if (product.stock === 0) {
                    alert('عذراً، هذا المنتج غير متوفر حالياً');
                    return;
                }

                selectedProduct = product;
                currentQuantity = 1;

                document.getElementById('modal-product-icon').textContent = product.icon;
                document.getElementById('modal-product-name').textContent = product.name;
                document.getElementById('modal-product-price').textContent = product.price.toFixed(2) + ' ريال';

                updateQuantityDisplay();
                modal.style.display = 'block';
            }

            function closePreorderModal() {
                modal.style.display = 'none';
                selectedProduct = null;
                currentQuantity = 1;
            }

            function updateQuantityDisplay() {
                quantityDisplay.textContent = currentQuantity;
                const totalPrice = selectedProduct.price * currentQuantity;
                totalPriceDisplay.textContent = totalPrice;
            }

            // أحداث النافذة
            closeBtn.addEventListener('click', closePreorderModal);
            cancelBtn.addEventListener('click', closePreorderModal);

            decreaseBtn.addEventListener('click', () => {
                if (currentQuantity > 1) {
                    currentQuantity--;
                    updateQuantityDisplay();
                }
            });

            increaseBtn.addEventListener('click', () => {
                if (currentQuantity < 10) {
                    currentQuantity++;
                    updateQuantityDisplay();
                }
            });

            confirmBtn.addEventListener('click', () => {
                if (!selectedProduct) return;

                const totalPrice = selectedProduct.price * currentQuantity;

                // فحص الرصيد
                if ((currentUser.balance || 0) < totalPrice) {
                    alert('رصيدك غير كافي لإتمام هذا الطلب!');
                    return;
                }

                // إنشاء طلب مسبق جديد محسن للوصول لصفحة العامل
                const preorder = {
                    id: Date.now(),
                    userId: currentUser.id, // تم تغيير studentId إلى userId للتوافق مع صفحة العامل
                    studentId: currentUser.id,
                    studentName: currentUser.name,
                    schoolId: currentUser.schoolId,
                    products: [{
                        productId: selectedProduct.id,
                        name: selectedProduct.name,
                        quantity: currentQuantity,
                        price: selectedProduct.price
                    }],
                    product: selectedProduct, // الاحتفاظ بالتوافق مع النظام القديم
                    quantity: currentQuantity,
                    totalPrice: totalPrice,
                    status: 'pending',
                    type: 'preorder',
                    timestamp: new Date().toISOString(),
                    date: new Date().toLocaleDateString('ar-SA'),
                    time: new Date().toLocaleTimeString('ar-SA'),
                    createdAt: new Date().toISOString()
                };

                // حفظ الطلب في localStorage
                let preorders = JSON.parse(localStorage.getItem('preorders')) || [];
                preorders.push(preorder);
                localStorage.setItem('preorders', JSON.stringify(preorders));

                // خصم من الرصيد
                currentUser.balance = (currentUser.balance || 0) - totalPrice;

                // حفظ الطلب في قاعدة البيانات مع تسجيل مفصل
                console.log('💾 حفظ الطلب المسبق في قاعدة البيانات...');
                const db = getDatabase();
                if (!db.orders) db.orders = [];

                // إضافة الطلب لقاعدة البيانات
                db.orders.push(preorder);
                console.log(`📋 تم إضافة الطلب ${preorder.id} لقاعدة البيانات. إجمالي الطلبات: ${db.orders.length}`);

                // إرسال إشعارات للعاملين
                sendPreOrderNotificationsToStaff(preorder, currentUser);

                // حفظ قاعدة البيانات
                saveDatabase(db);
                console.log('✅ تم حفظ قاعدة البيانات بنجاح');

                // التحقق من الحفظ
                const verifyDb = getDatabase();
                const savedOrder = verifyDb.orders.find(o => o.id === preorder.id);
                if (savedOrder) {
                    console.log('✅ تم التحقق من حفظ الطلب بنجاح');
                } else {
                    console.error('❌ فشل في حفظ الطلب!');
                }

                // إرسال الطلب تلقائياً لصفحة العامل
                sendOrderToStaffPage(preorder);

                // تحديث بيانات المستخدم
                let users = JSON.parse(localStorage.getItem('users')) || [];
                const userIndex = users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    users[userIndex] = currentUser;
                    localStorage.setItem('users', JSON.stringify(users));
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                }

                // تحديث عرض الرصيد
                document.getElementById('student-balance').textContent = currentUser.balance + ' ريال';

                alert(`تم إرسال طلبك المسبق بنجاح!\nالمنتج: ${selectedProduct.name}\nالكمية: ${currentQuantity}\nالمجموع: ${totalPrice} ريال\n\nتم إرسال إشعار للعاملين وسيتم تحضير طلبك.`);

                closePreorderModal();
            });

            // إغلاق النافذة عند النقر خارجها
            window.addEventListener('click', (event) => {
                if (event.target === modal) {
                    closePreorderModal();
                }
            });

            // دالة إرسال إشعارات للعاملين عند الطلب المسبق
            function sendPreOrderNotificationsToStaff(order, student) {
                const db = getDatabase();

                // تأكد من وجود مصفوفة الإشعارات
                if (!db.notifications) {
                    db.notifications = [];
                }

                const now = new Date();
                const productsList = order.products ?
                    order.products.map(p => `${p.name} (${p.quantity})`).join(', ') :
                    `${order.product.name} (${order.quantity})`;

                // إشعار للعاملين (جميع المستخدمين من نوع staff)
                const staffUsers = db.users.filter(u => u.role === 'staff' && u.schoolId === student.schoolId);
                staffUsers.forEach(staff => {
                    const staffNotification = {
                        id: Date.now() + Math.random() * 1000,
                        userId: staff.id,
                        title: '📋 طلب مسبق جديد من طالب',
                        message: `طلب مسبق جديد من الطالب ${student.name}. المنتجات: ${productsList}. المبلغ: ${order.totalPrice} ريال.`,
                        type: 'staff_preorder',
                        status: 'unread',
                        createdAt: now.toISOString(),
                        metadata: {
                            orderId: order.id,
                            studentName: student.name,
                            totalPrice: order.totalPrice,
                            products: order.products || [order.product]
                        }
                    };

                    db.notifications.push(staffNotification);
                });

                console.log(`تم إرسال ${staffUsers.length} إشعار للعاملين للطلب المسبق رقم ${order.id}`);
            }

            // دالة إرسال الطلب تلقائياً لصفحة العامل
            function sendOrderToStaffPage(order) {
                try {
                    // إنشاء حدث مخصص لإشعار صفحة العامل
                    const orderEvent = new CustomEvent('newPreOrder', {
                        detail: {
                            order: order,
                            timestamp: new Date().toISOString(),
                            source: 'student'
                        }
                    });

                    // إرسال الحدث
                    window.dispatchEvent(orderEvent);

                    // حفظ في localStorage للتأكد من وصول الطلب
                    const staffOrders = JSON.parse(localStorage.getItem('staffPendingOrders')) || [];
                    staffOrders.push({
                        ...order,
                        receivedAt: new Date().toISOString(),
                        source: 'student'
                    });
                    localStorage.setItem('staffPendingOrders', JSON.stringify(staffOrders));

                    // إشعار فوري للعاملين المتصلين
                    broadcastToStaffPages(order);

                    console.log('✅ تم إرسال الطلب المسبق تلقائياً لصفحة العامل:', order.id);
                } catch (error) {
                    console.error('❌ خطأ في إرسال الطلب لصفحة العامل:', error);
                }
            }

            // دالة بث الطلب لجميع صفحات العاملين المفتوحة
            function broadcastToStaffPages(order) {
                try {
                    // استخدام BroadcastChannel للتواصل بين الصفحات
                    if (typeof BroadcastChannel !== 'undefined') {
                        const channel = new BroadcastChannel('canteen-orders');
                        channel.postMessage({
                            type: 'NEW_PREORDER',
                            order: order,
                            timestamp: new Date().toISOString(),
                            source: 'student'
                        });

                        console.log('📡 تم بث الطلب المسبق لجميع صفحات العاملين');
                    }

                    // استخدام localStorage كبديل
                    const broadcastData = {
                        type: 'NEW_PREORDER',
                        order: order,
                        timestamp: new Date().toISOString(),
                        source: 'student',
                        id: Date.now()
                    };

                    localStorage.setItem('orderBroadcast', JSON.stringify(broadcastData));

                    // إزالة البيانات بعد ثانية واحدة
                    setTimeout(() => {
                        localStorage.removeItem('orderBroadcast');
                    }, 1000);

                } catch (error) {
                    console.error('خطأ في بث الطلب:', error);
                }
            }

            // دالة عرض معلومات الحساسية
            window.showAllergyInfo = function(productId) {
                const product = canteenProducts.find(p => p.id === productId);
                if (!product) return;

                // معلومات الحساسية للمنتجات
                const allergyInfo = {
                    1: ['الجلوتين', 'السمسم'], // ساندويش فلافل
                    2: ['الألبان'], // ساندويش جبن
                    3: ['السمك'], // ساندويش تونة
                    4: ['الدجاج'], // ساندويش دجاج
                    5: [], // ساندويش خضار
                    6: ['اللحوم الحمراء', 'الجلوتين'], // برجر لحم
                    7: ['الدجاج', 'الجلوتين'], // برجر دجاج
                    8: ['الجلوتين', 'الألبان'], // بيتزا صغيرة
                    9: ['الجلوتين', 'السمسم'], // مرامي قشار بطل افخاذ
                    10: ['الدجاج', 'الجلوتين'], // شاورما دجاج
                    11: ['الحمضيات'], // عصير برتقال طازج
                    12: [], // عصير تفاح
                    13: [], // عصير مانجو
                    14: [], // عصير فراولة
                    15: [], // ماء معدني
                    16: ['الكافيين', 'المواد الحافظة'], // مشروب غازي
                    17: ['الكافيين'], // شاي مثلج
                    18: ['الكافيين', 'الألبان'], // قهوة باردة
                    19: ['الألبان'], // لبن رائب
                    20: ['الحمضيات'], // عصير ليمون
                    21: ['الزبدة'], // فشار بالزبدة
                    22: [], // شيبس بطاطس
                    23: ['المكسرات'], // مكسرات مشكلة
                    24: ['الجلوتين', 'الألبان'], // بسكويت شوكولاتة
                    25: ['الجلوتين', 'الألبان'], // كراكرز جبن
                    26: ['الجلوتين', 'الألبان', 'البيض'], // كيك شوكولاتة
                    27: ['الجلوتين', 'الألبان', 'البيض'], // دونات محشي
                    28: ['الألبان'], // آيس كريم
                    29: ['الألبان', 'المكسرات'], // شوكولاتة
                    30: ['العسل', 'المكسرات'] // حلوى عربية
                };

                const allergies = allergyInfo[productId] || [];

                let message = `معلومات الحساسية للمنتج: ${product.name}\n\n`;

                if (allergies.length === 0) {
                    message += '✅ هذا المنتج لا يحتوي على مواد مسببة للحساسية معروفة.';
                } else {
                    message += '⚠️ يحتوي هذا المنتج على:\n';
                    allergies.forEach(allergy => {
                        message += `• ${allergy}\n`;
                    });
                    message += '\nيرجى توخي الحذر إذا كان لديك حساسية من أي من هذه المواد.';
                }

                alert(message);
            };

            // عرض المنتجات عند تحميل الصفحة
            renderSimpleProducts();

            // Add balance functionality
            document.getElementById('add-balance-btn').addEventListener('click', function() {
                const amount = prompt('كم تريد إضافة إلى رصيدك؟', '50');

                if (amount && !isNaN(amount) && amount > 0) {
                    currentUser.balance = (currentUser.balance || 0) + parseInt(amount);

                    // تحديث بيانات المستخدم
                    let users = JSON.parse(localStorage.getItem('users')) || [];
                    const userIndex = users.findIndex(u => u.id === currentUser.id);
                    if (userIndex !== -1) {
                        users[userIndex] = currentUser;
                        localStorage.setItem('users', JSON.stringify(users));
                        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                    }

                    // تحديث العرض
                    document.getElementById('student-balance').textContent = currentUser.balance + ' ريال';

                    alert(`تم إضافة ${amount} ريال إلى رصيدك بنجاح!`);
                }
            });

            // Logout functionality - Sidebar button
            document.getElementById('logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });

            // Logout functionality - Header button
            document.getElementById('header-logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });
        });
    </script>
</body>
</html>
