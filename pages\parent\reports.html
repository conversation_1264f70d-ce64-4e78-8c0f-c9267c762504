<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }
        
        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .report-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-group label {
            font-weight: 500;
        }
        
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }
        
        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .report-card h3 {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 10px;
        }
        
        .report-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 30px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }
        
        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        table tr {
            border-bottom: 1px solid #eee;
        }
        
        table tr:last-child {
            border-bottom: none;
        }
        
        .export-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            margin-right: auto;
        }
        
        .export-btn i {
            margin-left: 5px;
        }
        
        .export-btn:hover {
            background-color: var(--dark-color);
        }
        
        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }
        
        .logout-btn i {
            margin-left: 8px;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .report-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>ولي الأمر</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="children.html"><i class="fas fa-child"></i> الأبناء</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="reports.html" class="active"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>
        
        <div class="main-content">
            <div class="page-header">
                <h1>التقارير</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>
            
            <div class="content-card">
                <div class="report-filters">
                    <div class="filter-group">
                        <label for="child-filter">الابن:</label>
                        <select id="child-filter">
                            <option value="all">جميع الأبناء</option>
                            <!-- سيتم ملء هذه القائمة ديناميكيًا -->
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date-filter">الفترة:</label>
                        <select id="date-filter">
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                            <option value="year">هذا العام</option>
                            <option value="all">كل الوقت</option>
                        </select>
                    </div>
                </div>
                
                <div class="report-grid">
                    <div class="report-card">
                        <h3>إجمالي المصروفات</h3>
                        <div class="value" id="total-spending">0 ريال</div>
                    </div>
                    <div class="report-card">
                        <h3>عدد الطلبات</h3>
                        <div class="value" id="total-orders">0</div>
                    </div>
                    <div class="report-card">
                        <h3>متوسط قيمة الطلب</h3>
                        <div class="value" id="average-order">0 ريال</div>
                    </div>
                </div>
                
                <h2>توزيع المصروفات</h2>
                <div class="chart-container">
                    <canvas id="spending-chart"></canvas>
                </div>
                
                <h2>المنتجات الأكثر طلبًا</h2>
                <div class="chart-container">
                    <canvas id="products-chart"></canvas>
                </div>
                
                <h2>سجل الطلبات</h2>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الابن</th>
                                <th>المنتجات</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
                
                <button id="export-btn" class="export-btn"><i class="fas fa-file-export"></i> تصدير التقرير</button>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is parent
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }
            
            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            
            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            
            // Get database
            const db = getDatabase();
            
            // Get children
            const children = db.users.filter(user => currentUser.children.includes(user.id));
            
            // Populate child filter
            const childFilter = document.getElementById('child-filter');
            children.forEach(child => {
                const option = document.createElement('option');
                option.value = child.id;
                option.textContent = child.name;
                childFilter.appendChild(option);
            });
            
            // Get orders for children
            const childrenIds = currentUser.children;
            let orders = db.orders.filter(order => childrenIds.includes(order.userId));
            
            // Function to filter orders by date
            function filterOrdersByDate(orders, filter) {
                const today = new Date();
                const startOfWeek = new Date(today);
                startOfWeek.setDate(today.getDate() - today.getDay());
                startOfWeek.setHours(0, 0, 0, 0);
                
                const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                const startOfYear = new Date(today.getFullYear(), 0, 1);
                
                return orders.filter(order => {
                    const orderDate = new Date(order.date);
                    
                    switch(filter) {
                        case 'week':
                            return orderDate >= startOfWeek;
                        case 'month':
                            return orderDate >= startOfMonth;
                        case 'year':
                            return orderDate >= startOfYear;
                        default:
                            return true;
                    }
                });
            }
            
            // Function to update reports
            function updateReports() {
                const selectedChild = childFilter.value;
                const dateFilter = document.getElementById('date-filter').value;
                
                // Filter orders
                let filteredOrders = orders;
                if (selectedChild !== 'all') {
                    filteredOrders = filteredOrders.filter(order => order.userId === parseInt(selectedChild));
                }
                
                filteredOrders = filterOrdersByDate(filteredOrders, dateFilter);
                
                // Calculate statistics
                const totalSpending = filteredOrders.reduce((sum, order) => sum + order.totalPrice, 0);
                const totalOrders = filteredOrders.length;
                const averageOrder = totalOrders > 0 ? totalSpending / totalOrders : 0;
                
                // Update statistics display
                document.getElementById('total-spending').textContent = totalSpending + ' ريال';
                document.getElementById('total-orders').textContent = totalOrders;
                document.getElementById('average-order').textContent = averageOrder.toFixed(2) + ' ريال';
                
                // Populate orders table
                const ordersTable = document.getElementById('orders-table');
                ordersTable.innerHTML = '';
                
                filteredOrders.forEach(order => {
                    const child = db.users.find(u => u.id === order.userId);
                    
                    // Get product names
                    const productNames = order.products.map(p => {
                        const product = db.products.find(prod => prod.id === p.productId);
                        return `${product ? product.name : 'منتج غير معروف'} (${p.quantity})`;
                    }).join(', ');
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${order.id}</td>
                        <td>${child ? child.name : 'غير معروف'}</td>
                        <td>${productNames}</td>
                        <td>${order.totalPrice} ريال</td>
                        <td>${order.date}</td>
                        <td>${getOrderStatusText(order.status)}</td>
                    `;
                    ordersTable.appendChild(row);
                });
                
                // If no orders
                if (filteredOrders.length === 0) {
                    ordersTable.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد طلبات.</td></tr>';
                }
                
                // Update spending chart
                updateSpendingChart(filteredOrders);
                
                // Update products chart
                updateProductsChart(filteredOrders);
            }
            
            // Function to update spending chart
            function updateSpendingChart(orders) {
                // Group orders by child
                const spendingByChild = {};
                
                orders.forEach(order => {
                    const child = db.users.find(u => u.id === order.userId);
                    const childName = child ? child.name : 'غير معروف';
                    
                    if (!spendingByChild[childName]) {
                        spendingByChild[childName] = 0;
                    }
                    
                    spendingByChild[childName] += order.totalPrice;
                });
                
                // Prepare data for chart
                const labels = Object.keys(spendingByChild);
                const data = Object.values(spendingByChild);
                
                // Create chart
                const ctx = document.getElementById('spending-chart').getContext('2d');
                
                // Check if chart already exists
                if (window.spendingChart) {
                    window.spendingChart.destroy();
                }
                
                window.spendingChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'المصروفات',
                            data: data,
                            backgroundColor: [
                                'rgba(46, 125, 50, 0.7)',
                                'rgba(249, 168, 37, 0.7)',
                                'rgba(33, 150, 243, 0.7)',
                                'rgba(233, 30, 99, 0.7)',
                                'rgba(156, 39, 176, 0.7)'
                            ],
                            borderColor: [
                                'rgba(46, 125, 50, 1)',
                                'rgba(249, 168, 37, 1)',
                                'rgba(33, 150, 243, 1)',
                                'rgba(233, 30, 99, 1)',
                                'rgba(156, 39, 176, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        return `${label}: ${value} ريال`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
            
            // Function to update products chart
            function updateProductsChart(orders) {
                // Count products
                const productCounts = {};
                
                orders.forEach(order => {
                    order.products.forEach(p => {
                        const product = db.products.find(prod => prod.id === p.productId);
                        const productName = product ? product.name : 'منتج غير معروف';
                        
                        if (!productCounts[productName]) {
                            productCounts[productName] = 0;
                        }
                        
                        productCounts[productName] += p.quantity;
                    });
                });
                
                // Sort products by count
                const sortedProducts = Object.entries(productCounts)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5); // Get top 5
                
                // Prepare data for chart
                const labels = sortedProducts.map(p => p[0]);
                const data = sortedProducts.map(p => p[1]);
                
                // Create chart
                const ctx = document.getElementById('products-chart').getContext('2d');
                
                // Check if chart already exists
                if (window.productsChart) {
                    window.productsChart.destroy();
                }
                
                window.productsChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'عدد الطلبات',
                            data: data,
                            backgroundColor: 'rgba(46, 125, 50, 0.7)',
                            borderColor: 'rgba(46, 125, 50, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        }
                    }
                });
            }
            
            // Initial update
            updateReports();
            
            // Filter change events
            childFilter.addEventListener('change', updateReports);
            document.getElementById('date-filter').addEventListener('change', updateReports);
            
            // Export button
            document.getElementById('export-btn').addEventListener('click', function() {
                alert('تم تصدير التقرير بنجاح!');
            });
            
            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../auth/login.html';
            });
        });
        
        // Helper function to get order status text
        function getOrderStatusText(status) {
            switch(status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }
    </script>
</body>
</html>
