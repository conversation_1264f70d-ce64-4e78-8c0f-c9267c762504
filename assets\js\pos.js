﻿/**
 * نظام نقاط البيع (POS) للمقاصف المدرسية
 * فواصل النجاح للخدمات الإعاشة
 * نسخة متقدمة مع واجهة مستخدم محسنة وميزات إضافية
 */

// المتغيرات العامة
let currentUser = null;       // المستخدم الحالي (العامل)
let currentStudent = null;    // الطالب المحدد حالياً
let cart = [];                // سلة المشتريات
let products = [];            // قائمة المنتجات
let categories = [];          // فئات المنتجات
let pendingOrders = [];       // الطلبات المعلقة
let allStudents = [];         // قائمة جميع الطلاب
let salesChart = null;        // مخطط المبيعات
let scanner = null;           // ماسح الباركود

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تحميل بيانات المستخدم الحالي
    loadCurrentUser();

    // تهيئة المنتجات والفئات
    initializeProducts();

    // تحميل بيانات الطلاب
    loadStudents();

    // تهيئة الأحداث
    initializeEventListeners();

    // تحميل الطلبات المعلقة
    loadPendingOrders();

    // التحقق من وجود طالب محدد من صفحة الطلاب
    checkSelectedStudent();

    // إضافة تأثيرات حركية للصفحة
    addPageAnimations();
});

/**
 * تحميل بيانات المستخدم الحالي
 */
function loadCurrentUser() {
    try {
        // الحصول على بيانات المستخدم من sessionStorage
        const userJson = sessionStorage.getItem('currentUser');
        if (userJson) {
            currentUser = JSON.parse(userJson);

            // عرض اسم المستخدم في الواجهة
            const userNameElement = document.getElementById('user-name');
            if (userNameElement && currentUser.name) {
                userNameElement.innerHTML = `مرحبًا، <strong>${currentUser.name}</strong>`;
            }

            // عرض التاريخ الحالي
            const currentDateElement = document.getElementById('current-date');
            if (currentDateElement) {
                const now = new Date();
                const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
            }

            // التحقق من صلاحيات المستخدم
            if (!currentUser.permissions || !currentUser.permissions.includes('sell')) {
                alert('ليس لديك صلاحية الوصول إلى نظام نقاط البيع');
                window.location.href = 'dashboard.html';
            }
        } else {
            // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول إذا لم يكن مسجل الدخول
            alert('يرجى تسجيل الدخول أولاً');
            window.location.href = '../auth/login.html';
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدم:', error);
        alert('حدث خطأ أثناء تحميل بيانات المستخدم');
    }
}

/**
 * تهيئة المنتجات والفئات
 */
function initializeProducts() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحميل المنتجات النشطة فقط
        products = db.products.filter(product => product.status === 'active');

        // استخراج الفئات الفريدة
        const uniqueCategories = [...new Set(products.map(product => product.category))];
        categories = uniqueCategories;

        // عرض الفئات في الواجهة
        renderCategories();

        // عرض المنتجات في الواجهة
        renderProducts();
    } catch (error) {
        console.error('خطأ في تهيئة المنتجات:', error);
        alert('حدث خطأ أثناء تحميل المنتجات');
    }
}

/**
 * تحميل الطلبات المعلقة
 */
function loadPendingOrders() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحميل الطلبات المعلقة للمدرسة الحالية
        if (currentUser && currentUser.schoolId) {
            pendingOrders = db.orders.filter(order =>
                order.schoolId === currentUser.schoolId &&
                order.status === 'pending'
            );

            // تحديث عدد الطلبات المعلقة في الواجهة
            const pendingOrdersCountElement = document.getElementById('pending-orders-count');
            if (pendingOrdersCountElement) {
                pendingOrdersCountElement.textContent = pendingOrders.length;

                // إضافة تأثير نبض إذا كان هناك طلبات معلقة
                if (pendingOrders.length > 0) {
                    const pendingOrdersBtn = document.getElementById('pending-orders-btn');
                    if (pendingOrdersBtn) {
                        pendingOrdersBtn.classList.add('pulse-animation');
                    }
                }
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الطلبات المعلقة:', error);
    }
}

/**
 * عرض فئات المنتجات في الواجهة
 */
function renderCategories() {
    try {
        const categoriesContainer = document.querySelector('.pos-categories');
        if (!categoriesContainer) return;

        // الاحتفاظ بزر "الكل"
        const allCategoryButton = categoriesContainer.querySelector('[data-category="all"]');

        // مسح الفئات الحالية
        categoriesContainer.innerHTML = '';

        // إضافة زر "الكل" مرة أخرى
        categoriesContainer.appendChild(allCategoryButton);

        // إضافة الفئات
        categories.forEach(category => {
            const categoryElement = document.createElement('div');
            categoryElement.className = 'pos-category';
            categoryElement.setAttribute('data-category', category);
            categoryElement.textContent = category;

            // إضافة حدث النقر
            categoryElement.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                document.querySelectorAll('.pos-category').forEach(cat => cat.classList.remove('active'));

                // تعيين الفئة النشطة للزر الحالي
                this.classList.add('active');

                // تصفية المنتجات حسب الفئة
                filterProductsByCategory(category);
            });

            categoriesContainer.appendChild(categoryElement);
        });
    } catch (error) {
        console.error('خطأ في عرض الفئات:', error);
    }
}

/**
 * عرض المنتجات في الواجهة
 * @param {Array} filteredProducts - قائمة المنتجات المصفاة (اختياري)
 */
function renderProducts(filteredProducts = null) {
    try {
        const productsContainer = document.getElementById('products-container');
        if (!productsContainer) return;

        // تحديد المنتجات التي سيتم عرضها
        const productsToRender = filteredProducts || products;

        // مسح المنتجات الحالية
        productsContainer.innerHTML = '';

        // إضافة المنتجات
        if (productsToRender.length === 0) {
            productsContainer.innerHTML = '<div class="pos-no-products">لا توجد منتجات متاحة</div>';
            return;
        }

        productsToRender.forEach(product => {
            const productElement = document.createElement('div');
            productElement.className = 'pos-product';
            productElement.setAttribute('data-product-id', product.id);

            // تحديد الصورة أو الأيقونة
            let imageHtml = '';
            if (product.image && product.image !== 'default-product.jpg') {
                imageHtml = `<img src="../../assets/images/products/${product.image}" alt="${product.name}">`;
            } else {
                imageHtml = `<i class="fas fa-utensils"></i>`;
            }

            productElement.innerHTML = `
                <div class="pos-product-image">${imageHtml}</div>
                <div class="pos-product-info">
                    <div class="pos-product-name">${product.name}</div>
                    <div class="pos-product-price">${product.price} ريال</div>
                </div>
            `;

            // إضافة حدث النقر
            productElement.addEventListener('click', function() {
                addToCart(product);
            });

            productsContainer.appendChild(productElement);
        });
    } catch (error) {
        console.error('خطأ في عرض المنتجات:', error);
    }
}

/**
 * تصفية المنتجات حسب الفئة
 * @param {string} category - الفئة المطلوبة
 */
function filterProductsByCategory(category) {
    try {
        if (category === 'all') {
            // عرض جميع المنتجات
            renderProducts();
        } else {
            // تصفية المنتجات حسب الفئة
            const filteredProducts = products.filter(product => product.category === category);
            renderProducts(filteredProducts);
        }
    } catch (error) {
        console.error('خطأ في تصفية المنتجات:', error);
    }
}

/**
 * البحث عن طالب
 */
function searchStudent() {
    try {
        const searchInput = document.getElementById('student-search');
        if (!searchInput) return;

        const searchValue = searchInput.value.trim();
        if (!searchValue) {
            alert('الرجاء إدخال رقم نور أو اسم الطالب');
            return;
        }

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // البحث عن الطالب
        let student = null;

        // البحث حسب رقم نور
        student = db.users.find(user =>
            user.role === 'student' &&
            user.noorId === searchValue
        );

        // إذا لم يتم العثور على الطالب، البحث حسب الاسم
        if (!student) {
            student = db.users.find(user =>
                user.role === 'student' &&
                user.name.includes(searchValue)
            );
        }

        if (student) {
            // عرض معلومات الطالب
            displayStudentInfo(student);
        } else {
            alert('لم يتم العثور على الطالب');
        }
    } catch (error) {
        console.error('خطأ في البحث عن طالب:', error);
        alert('حدث خطأ أثناء البحث عن الطالب');
    }
}

/**
 * عرض معلومات الطالب
 * @param {Object} student - بيانات الطالب
 */
function displayStudentInfo(student) {
    try {
        // تخزين الطالب الحالي
        currentStudent = student;

        // الحصول على عناصر واجهة المستخدم
        const studentInfoCard = document.getElementById('student-info-card');
        const studentName = document.getElementById('student-name');
        const studentNoorId = document.getElementById('student-noor-id');
        const studentSchool = document.getElementById('student-school');
        const studentGrade = document.getElementById('student-grade');
        const studentBalance = document.getElementById('student-balance');
        const studentAllergiesContainer = document.getElementById('student-allergies-container');
        const studentAllergies = document.getElementById('student-allergies');

        // التحقق من وجود العناصر
        if (!studentInfoCard || !studentName || !studentNoorId || !studentSchool ||
            !studentGrade || !studentBalance || !studentAllergiesContainer || !studentAllergies) {
            console.error('لم يتم العثور على عناصر واجهة المستخدم');
            return;
        }

        // الحصول على معلومات المدرسة
        const db = getDatabase();
        const school = db.schools.find(s => s.id === student.schoolId);

        // عرض معلومات الطالب
        studentName.textContent = student.name || '-';
        studentNoorId.textContent = 'رقم نور: ' + (student.noorId || '-');
        studentSchool.textContent = school ? school.name : '-';
        studentGrade.textContent = student.grade || '-';
        studentBalance.textContent = (student.balance || 0) + ' ريال';

        // إضافة تأثير حركي للرصيد
        animateBalance(studentBalance, student.balance || 0);

        // عرض معلومات الحساسية إذا وجدت
        if (student.allergies && student.allergies.length > 0) {
            studentAllergiesContainer.style.display = 'block';

            // مسح الحساسيات الحالية
            studentAllergies.innerHTML = '';

            // إضافة الحساسيات
            student.allergies.forEach(allergy => {
                const allergyBadge = document.createElement('div');
                allergyBadge.className = 'pos-allergy-badge';
                allergyBadge.textContent = allergy.name;
                allergyBadge.title = allergy.description || '';

                // إضافة لون مختلف حسب شدة الحساسية
                if (allergy.severity === 'high') {
                    allergyBadge.style.color = 'var(--pos-danger)';
                    allergyBadge.style.borderColor = 'var(--pos-danger)';
                } else if (allergy.severity === 'medium') {
                    allergyBadge.style.color = 'var(--pos-warning)';
                    allergyBadge.style.borderColor = 'var(--pos-warning)';
                } else {
                    allergyBadge.style.color = 'var(--pos-info)';
                    allergyBadge.style.borderColor = 'var(--pos-info)';
                }

                // إضافة أيقونة للحساسية
                const allergyIcon = document.createElement('i');
                allergyIcon.className = getAllergyIcon(allergy.name);
                allergyIcon.style.marginLeft = '5px';
                allergyBadge.prepend(allergyIcon);

                studentAllergies.appendChild(allergyBadge);
            });
        } else {
            studentAllergiesContainer.style.display = 'none';
        }

        // إظهار بطاقة معلومات الطالب
        studentInfoCard.style.display = 'block';
        studentInfoCard.classList.add('active');

        // إضافة تأثير حركي لبطاقة معلومات الطالب
        studentInfoCard.style.animation = 'fadeIn 0.5s ease-out';
    } catch (error) {
        console.error('خطأ في عرض معلومات الطالب:', error);
    }
}

/**
 * تحريك قيمة الرصيد
 * @param {HTMLElement} element - عنصر الرصيد
 * @param {number} finalValue - القيمة النهائية
 */
function animateBalance(element, finalValue) {
    try {
        // إعادة تعيين القيمة إلى صفر
        element.textContent = '0 ريال';

        // تحريك القيمة من صفر إلى القيمة النهائية
        let currentValue = 0;
        const duration = 1000; // مدة التحريك بالمللي ثانية
        const interval = 16; // فترة التحديث بالمللي ثانية
        const steps = duration / interval;
        const increment = finalValue / steps;

        const animation = setInterval(() => {
            currentValue += increment;

            if (currentValue >= finalValue) {
                clearInterval(animation);
                element.textContent = finalValue + ' ريال';
            } else {
                element.textContent = Math.floor(currentValue) + ' ريال';
            }
        }, interval);
    } catch (error) {
        console.error('خطأ في تحريك قيمة الرصيد:', error);
    }
}

/**
 * الحصول على أيقونة الحساسية المناسبة
 * @param {string} allergyName - اسم الحساسية
 * @returns {string} - اسم الأيقونة
 */
function getAllergyIcon(allergyName) {
    try {
        const allergyName_lower = allergyName.toLowerCase();

        if (allergyName_lower.includes('حليب') || allergyName_lower.includes('لبن') || allergyName_lower.includes('جبن')) {
            return 'fas fa-cheese';
        } else if (allergyName_lower.includes('فول') || allergyName_lower.includes('مكسرات')) {
            return 'fas fa-seedling';
        } else if (allergyName_lower.includes('بيض')) {
            return 'fas fa-egg';
        } else if (allergyName_lower.includes('سمك') || allergyName_lower.includes('بحر')) {
            return 'fas fa-fish';
        } else if (allergyName_lower.includes('قمح') || allergyName_lower.includes('خبز')) {
            return 'fas fa-bread-slice';
        } else if (allergyName_lower.includes('فراولة') || allergyName_lower.includes('توت')) {
            return 'fas fa-apple-alt';
        } else {
            return 'fas fa-allergies';
        }
    } catch (error) {
        console.error('خطأ في الحصول على أيقونة الحساسية:', error);
        return 'fas fa-allergies';
    }
}

/**
 * تهيئة مستمعي الأحداث
 */
function initializeEventListeners() {
    try {
        // زر البحث عن طالب
        const searchBtn = document.getElementById('search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', searchStudent);
        }

        // حقل البحث عن طالب (للبحث عند الضغط على Enter)
        const studentSearchInput = document.getElementById('student-search');
        if (studentSearchInput) {
            studentSearchInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    searchStudent();
                }
            });
        }

        // زر مسح معلومات الطالب
        const clearStudentBtn = document.getElementById('clear-student-btn');
        if (clearStudentBtn) {
            clearStudentBtn.addEventListener('click', clearStudentInfo);
        }

        // زر الطلبات المعلقة
        const pendingOrdersBtn = document.getElementById('pending-orders-btn');
        if (pendingOrdersBtn) {
            pendingOrdersBtn.addEventListener('click', showPendingOrders);
        }

        // زر تقرير اليوم
        const dailyReportBtn = document.getElementById('daily-report-btn');
        if (dailyReportBtn) {
            dailyReportBtn.addEventListener('click', showDailyReport);
        }

        // زر لوحة التحكم
        const dashboardBtn = document.getElementById('dashboard-btn');
        if (dashboardBtn) {
            dashboardBtn.addEventListener('click', function() {
                window.location.href = 'dashboard.html';
            });
        }

        // أزرار إضافة الرصيد وعرض سجل الطلبات
        const addBalanceBtn = document.getElementById('add-balance-btn');
        if (addBalanceBtn) {
            addBalanceBtn.addEventListener('click', showAddBalanceModal);
        }

        const viewOrdersBtn = document.getElementById('view-orders-btn');
        if (viewOrdersBtn) {
            viewOrdersBtn.addEventListener('click', showStudentOrdersModal);
        }

        // أزرار ماسح الباركود وقائمة الطلاب
        const scanBarcodeBtn = document.getElementById('scan-barcode-btn');
        if (scanBarcodeBtn) {
            scanBarcodeBtn.addEventListener('click', showBarcodeScannerModal);
        }

        const browseStudentsBtn = document.getElementById('browse-students-btn');
        if (browseStudentsBtn) {
            browseStudentsBtn.addEventListener('click', showStudentsListModal);
        }

        // حقل البحث عن منتج
        const productSearchInput = document.getElementById('product-search');
        if (productSearchInput) {
            productSearchInput.addEventListener('input', function() {
                const searchValue = this.value.trim().toLowerCase();

                if (searchValue === '') {
                    // إذا كان حقل البحث فارغًا، عرض جميع المنتجات
                    renderProducts();

                    // تحديد الفئة "الكل" كفئة نشطة
                    document.querySelectorAll('.pos-category').forEach(cat => cat.classList.remove('active'));
                    document.querySelector('.pos-category[data-category="all"]').classList.add('active');
                } else {
                    // تصفية المنتجات حسب النص المدخل
                    const filteredProducts = products.filter(product =>
                        product.name.toLowerCase().includes(searchValue) ||
                        product.category.toLowerCase().includes(searchValue)
                    );

                    renderProducts(filteredProducts);
                }
            });
        }

        // أزرار المبالغ السريعة
        const quickAmountButtons = document.querySelectorAll('.pos-quick-amount');
        if (quickAmountButtons.length > 0) {
            quickAmountButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const amount = parseFloat(this.getAttribute('data-amount'));
                    addQuickAmount(amount);
                });
            });
        }

        // أزرار المبالغ السريعة في نافذة إضافة الرصيد
        const balanceQuickAmountButtons = document.querySelectorAll('.balance-quick-amount');
        if (balanceQuickAmountButtons.length > 0) {
            balanceQuickAmountButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const amount = parseFloat(this.getAttribute('data-amount'));
                    document.getElementById('balance-amount').value = amount;
                });
            });
        }

        // زر تأكيد إضافة الرصيد
        const confirmAddBalanceBtn = document.getElementById('confirm-add-balance-btn');
        if (confirmAddBalanceBtn) {
            confirmAddBalanceBtn.addEventListener('click', addStudentBalance);
        }

        // زر مسح السلة
        const clearCartBtn = document.getElementById('clear-cart-btn');
        if (clearCartBtn) {
            clearCartBtn.addEventListener('click', clearCart);
        }

        // زر حفظ الطلب
        const saveOrderBtn = document.getElementById('save-order-btn');
        if (saveOrderBtn) {
            saveOrderBtn.addEventListener('click', saveOrderForLater);
        }

        // زر إتمام الطلب
        const checkoutBtn = document.getElementById('checkout-btn');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', checkout);
        }

        // أزرار نافذة ماسح الباركود
        const cancelScanBtn = document.getElementById('cancel-scan-btn');
        if (cancelScanBtn) {
            cancelScanBtn.addEventListener('click', function() {
                const modal = document.getElementById('barcode-scanner-modal');
                if (modal) {
                    modal.style.display = 'none';
                }
                if (scanner) {
                    try {
                        scanner.stop();
                    } catch (e) {
                        console.error('خطأ في إيقاف الماسح:', e);
                    }
                }
            });
        }

        const manualEntryBtn = document.getElementById('manual-entry-btn');
        if (manualEntryBtn) {
            manualEntryBtn.addEventListener('click', function() {
                const modal = document.getElementById('barcode-scanner-modal');
                if (modal) {
                    modal.style.display = 'none';
                }
                if (scanner) {
                    try {
                        scanner.stop();
                    } catch (e) {
                        console.error('خطأ في إيقاف الماسح:', e);
                    }
                }
                document.getElementById('student-search').focus();
            });
        }

        // أزرار نافذة قائمة الطلاب
        const studentsListSearch = document.getElementById('students-list-search');
        if (studentsListSearch) {
            studentsListSearch.addEventListener('input', filterStudentsList);
        }

        const gradeFilter = document.getElementById('grade-filter');
        if (gradeFilter) {
            gradeFilter.addEventListener('change', filterStudentsList);
        }

        // أزرار نافذة تقرير المبيعات اليومية
        const printReportBtn = document.getElementById('print-report-btn');
        if (printReportBtn) {
            printReportBtn.addEventListener('click', printDailyReport);
        }

        const closeDashboardBtn = document.getElementById('close-dashboard-btn');
        if (closeDashboardBtn) {
            closeDashboardBtn.addEventListener('click', function() {
                document.getElementById('daily-sales-dashboard').style.display = 'none';
                document.querySelector('.pos-card').style.display = 'block';
            });
        }

        // أزرار الإيصال
        const printReceiptBtn = document.getElementById('print-receipt-btn');
        if (printReceiptBtn) {
            printReceiptBtn.addEventListener('click', printReceipt);
        }

        const sendReceiptBtn = document.getElementById('send-receipt-btn');
        if (sendReceiptBtn) {
            sendReceiptBtn.addEventListener('click', sendReceiptToParent);
        }

        // أزرار إغلاق النوافذ المنبثقة
        const closeButtons = document.querySelectorAll('.close-btn');
        if (closeButtons.length > 0) {
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const modal = this.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';

                        // إيقاف ماسح الباركود إذا كان نشطًا
                        if (modal.id === 'barcode-scanner-modal' && scanner) {
                            try {
                                scanner.stop();
                            } catch (e) {
                                console.error('خطأ في إيقاف الماسح:', e);
                            }
                        }
                    }
                });
            });
        }

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';

                    // إيقاف ماسح الباركود إذا كان نشطًا
                    if (modal.id === 'barcode-scanner-modal' && scanner) {
                        try {
                            scanner.stop();
                        } catch (e) {
                            console.error('خطأ في إيقاف الماسح:', e);
                        }
                    }
                }
            });
        });
    } catch (error) {
        console.error('خطأ في تهيئة مستمعي الأحداث:', error);
    }
}

/**
 * مسح معلومات الطالب
 */
function clearStudentInfo() {
    try {
        // إعادة تعيين الطالب الحالي
        currentStudent = null;

        // إخفاء بطاقة معلومات الطالب
        const studentInfoCard = document.getElementById('student-info-card');
        if (studentInfoCard) {
            studentInfoCard.style.display = 'none';
            studentInfoCard.classList.remove('active');
        }

        // مسح حقل البحث
        const studentSearchInput = document.getElementById('student-search');
        if (studentSearchInput) {
            studentSearchInput.value = '';
            studentSearchInput.focus();
        }
    } catch (error) {
        console.error('خطأ في مسح معلومات الطالب:', error);
    }
}

/**
 * إضافة منتج إلى السلة
 * @param {Object} product - المنتج المراد إضافته
 */
function addToCart(product) {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');

            // تمييز قسم البحث عن طالب
            const studentSearchCard = document.querySelector('.pos-card:first-child');
            if (studentSearchCard) {
                studentSearchCard.classList.add('highlight-card');
                setTimeout(() => {
                    studentSearchCard.classList.remove('highlight-card');
                }, 2000);
            }

            return;
        }

        // التحقق من وجود المنتج في السلة
        const existingItemIndex = cart.findIndex(item => item.productId === product.id);

        if (existingItemIndex !== -1) {
            // زيادة الكمية إذا كان المنتج موجودًا بالفعل
            cart[existingItemIndex].quantity++;
        } else {
            // إضافة المنتج إلى السلة
            cart.push({
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity: 1
            });
        }

        // تحديث عرض السلة
        updateCartDisplay();

        // إضافة تأثير حركي للمنتج
        const productElement = document.querySelector(`.pos-product[data-product-id="${product.id}"]`);
        if (productElement) {
            productElement.classList.add('product-added');
            setTimeout(() => {
                productElement.classList.remove('product-added');
            }, 500);
        }
    } catch (error) {
        console.error('خطأ في إضافة منتج إلى السلة:', error);
    }
}

/**
 * تحديث عرض السلة
 */
function updateCartDisplay() {
    try {
        const cartItemsContainer = document.getElementById('cart-items');
        const cartSubtotal = document.getElementById('cart-subtotal');
        const cartTotal = document.getElementById('cart-total');

        if (!cartItemsContainer || !cartSubtotal || !cartTotal) return;

        // حساب المجموع
        const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

        // عرض المجموع
        cartSubtotal.textContent = subtotal + ' ريال';
        cartTotal.textContent = subtotal + ' ريال';

        // إذا كانت السلة فارغة
        if (cart.length === 0) {
            cartItemsContainer.innerHTML = `
                <div class="pos-cart-empty">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <p>السلة فارغة</p>
                </div>
            `;
            return;
        }

        // عرض عناصر السلة
        cartItemsContainer.innerHTML = '';

        cart.forEach((item, index) => {
            const cartItem = document.createElement('div');
            cartItem.className = 'pos-cart-item';

            cartItem.innerHTML = `
                <div class="pos-cart-item-info">
                    <div class="pos-cart-item-name">${item.name}</div>
                    <div class="pos-cart-item-price">${item.price} ريال × ${item.quantity}</div>
                </div>
                <div class="pos-cart-item-total">${item.price * item.quantity} ريال</div>
                <div class="pos-cart-item-actions">
                    <button class="pos-cart-item-decrease" data-index="${index}">-</button>
                    <span class="pos-cart-item-quantity">${item.quantity}</span>
                    <button class="pos-cart-item-increase" data-index="${index}">+</button>
                    <button class="pos-cart-item-remove" data-index="${index}">×</button>
                </div>
            `;

            cartItemsContainer.appendChild(cartItem);
        });

        // إضافة مستمعي الأحداث لأزرار السلة
        addCartButtonEventListeners();
    } catch (error) {
        console.error('خطأ في تحديث عرض السلة:', error);
    }
}

/**
 * إضافة مستمعي الأحداث لأزرار السلة
 */
function addCartButtonEventListeners() {
    try {
        // أزرار زيادة الكمية
        const increaseButtons = document.querySelectorAll('.pos-cart-item-increase');
        if (increaseButtons.length > 0) {
            increaseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    increaseItemQuantity(index);
                });
            });
        }

        // أزرار تقليل الكمية
        const decreaseButtons = document.querySelectorAll('.pos-cart-item-decrease');
        if (decreaseButtons.length > 0) {
            decreaseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    decreaseItemQuantity(index);
                });
            });
        }

        // أزرار إزالة العنصر
        const removeButtons = document.querySelectorAll('.pos-cart-item-remove');
        if (removeButtons.length > 0) {
            removeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    removeCartItem(index);
                });
            });
        }
    } catch (error) {
        console.error('خطأ في إضافة مستمعي الأحداث لأزرار السلة:', error);
    }
}

/**
 * زيادة كمية عنصر في السلة
 * @param {number} index - فهرس العنصر في السلة
 */
function increaseItemQuantity(index) {
    try {
        if (index >= 0 && index < cart.length) {
            cart[index].quantity++;
            updateCartDisplay();
        }
    } catch (error) {
        console.error('خطأ في زيادة كمية عنصر في السلة:', error);
    }
}

/**
 * تقليل كمية عنصر في السلة
 * @param {number} index - فهرس العنصر في السلة
 */
function decreaseItemQuantity(index) {
    try {
        if (index >= 0 && index < cart.length) {
            if (cart[index].quantity > 1) {
                cart[index].quantity--;
            } else {
                removeCartItem(index);
            }
            updateCartDisplay();
        }
    } catch (error) {
        console.error('خطأ في تقليل كمية عنصر في السلة:', error);
    }
}

/**
 * إزالة عنصر من السلة
 * @param {number} index - فهرس العنصر في السلة
 */
function removeCartItem(index) {
    try {
        if (index >= 0 && index < cart.length) {
            cart.splice(index, 1);
            updateCartDisplay();
        }
    } catch (error) {
        console.error('خطأ في إزالة عنصر من السلة:', error);
    }
}

/**
 * مسح السلة
 */
function clearCart() {
    try {
        // التأكيد قبل المسح
        if (cart.length > 0) {
            if (confirm('هل أنت متأكد من رغبتك في مسح السلة؟')) {
                cart = [];
                updateCartDisplay();
            }
        }
    } catch (error) {
        console.error('خطأ في مسح السلة:', error);
    }
}

/**
 * إضافة مبلغ سريع
 * @param {number} amount - المبلغ المراد إضافته
 */
function addQuickAmount(amount) {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // إضافة منتج وهمي للسلة
        cart.push({
            productId: -1, // معرف وهمي للمنتج
            name: 'مبلغ نقدي',
            price: amount,
            quantity: 1
        });

        // تحديث عرض السلة
        updateCartDisplay();
    } catch (error) {
        console.error('خطأ في إضافة مبلغ سريع:', error);
    }
}

/**
 * إتمام الطلب
 */
function checkout() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // التحقق من وجود عناصر في السلة
        if (cart.length === 0) {
            alert('السلة فارغة');
            return;
        }

        // حساب المجموع
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // التحقق من كفاية الرصيد
        if (currentStudent.balance < total) {
            alert('رصيد الطالب غير كافٍ');
            return;
        }

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // إنشاء معرف جديد للطلب
        const newOrderId = Math.max(...db.orders.map(order => order.id), 0) + 1;

        // إنشاء كائن الطلب الجديد
        const newOrder = {
            id: newOrderId,
            userId: currentStudent.id,
            schoolId: currentStudent.schoolId,
            date: new Date().toISOString(),
            items: cart.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
                price: item.price
            })),
            totalPrice: total,
            status: 'completed'
        };

        // إضافة الطلب إلى قاعدة البيانات
        db.orders.push(newOrder);

        // تحديث رصيد الطالب
        const studentIndex = db.users.findIndex(user => user.id === currentStudent.id);
        if (studentIndex !== -1) {
            db.users[studentIndex].balance -= total;
            currentStudent.balance -= total;
        }

        // حفظ التغييرات في قاعدة البيانات
        saveDatabase(db);

        // عرض إيصال الدفع
        showReceipt(newOrder);

        // تحديث معلومات الطالب في الواجهة
        if (studentIndex !== -1) {
            const studentBalance = document.getElementById('student-balance');
            if (studentBalance) {
                studentBalance.textContent = currentStudent.balance + ' ريال';
            }
        }

        // مسح السلة
        cart = [];
        updateCartDisplay();
    } catch (error) {
        console.error('خطأ في إتمام الطلب:', error);
        alert('حدث خطأ أثناء إتمام الطلب');
    }
}

/**
 * عرض إيصال الدفع
 * @param {Object} order - الطلب
 */
function showReceipt(order) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // الحصول على معلومات الطالب
        const student = db.users.find(user => user.id === order.userId);

        // الحصول على معلومات المدرسة
        const school = db.schools.find(s => s.id === order.schoolId);

        // تنسيق التاريخ
        const orderDate = new Date(order.date);
        const formattedDate = `${orderDate.getDate()}/${orderDate.getMonth() + 1}/${orderDate.getFullYear()}`;
        const formattedTime = `${orderDate.getHours()}:${orderDate.getMinutes().toString().padStart(2, '0')}`;

        // إنشاء محتوى الإيصال
        const receiptContainer = document.getElementById('receipt-container');
        if (!receiptContainer) return;

        receiptContainer.innerHTML = `
            <div class="pos-receipt-header">
                <h2>فواصل النجاح للخدمات الإعاشة</h2>
                <p>${school ? school.name : 'المدرسة'}</p>
                <p>إيصال دفع #${order.id}</p>
                <p>${formattedDate} - ${formattedTime}</p>
            </div>

            <div class="pos-receipt-student">
                <p>الطالب: ${student ? student.name : 'غير معروف'}</p>
                <p>رقم نور: ${student && student.noorId ? student.noorId : 'غير متوفر'}</p>
            </div>

            <div class="pos-receipt-items">
                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.items.map(item => {
                            const product = db.products.find(p => p.id === item.productId);
                            const productName = product ? product.name : (item.productId === -1 ? 'مبلغ نقدي' : 'منتج غير معروف');
                            return `
                                <tr>
                                    <td>${productName}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price} ريال</td>
                                    <td>${item.price * item.quantity} ريال</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>

            <div class="pos-receipt-total">
                <p>المجموع: ${order.totalPrice} ريال</p>
                <p>الرصيد المتبقي: ${student ? student.balance : 0} ريال</p>
            </div>

            <div class="pos-receipt-footer">
                <p>شكرًا لك!</p>
                <p>تم إصدار هذا الإيصال بواسطة: ${currentUser ? currentUser.name : 'العامل'}</p>
            </div>
        `;

        // عرض النافذة المنبثقة
        const receiptModal = document.getElementById('receipt-modal');
        if (receiptModal) {
            receiptModal.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في عرض إيصال الدفع:', error);
    }
}

/**
 * عرض الطلبات المعلقة
 */
function showPendingOrders() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحديث قائمة الطلبات المعلقة
        loadPendingOrders();

        // الحصول على حاوية الطلبات المعلقة
        const pendingOrdersContainer = document.getElementById('pending-orders-container');
        if (!pendingOrdersContainer) return;

        // مسح المحتوى الحالي
        pendingOrdersContainer.innerHTML = '';

        // إذا لم تكن هناك طلبات معلقة
        if (pendingOrders.length === 0) {
            pendingOrdersContainer.innerHTML = '<div class="pos-no-orders">لا توجد طلبات معلقة</div>';
        } else {
            // عرض الطلبات المعلقة
            pendingOrders.forEach(order => {
                // الحصول على معلومات الطالب
                const student = db.users.find(user => user.id === order.userId);

                // تنسيق التاريخ
                const orderDate = new Date(order.date);
                const formattedDate = `${orderDate.getDate()}/${orderDate.getMonth() + 1}/${orderDate.getFullYear()}`;
                const formattedTime = `${orderDate.getHours()}:${orderDate.getMinutes().toString().padStart(2, '0')}`;

                // إنشاء عنصر الطلب
                const orderElement = document.createElement('div');
                orderElement.className = 'pos-pending-order';

                orderElement.innerHTML = `
                    <div class="pos-pending-order-header">
                        <div class="pos-pending-order-info">
                            <div class="pos-pending-order-id">طلب #${order.id}</div>
                            <div class="pos-pending-order-date">${formattedDate} - ${formattedTime}</div>
                        </div>
                        <div class="pos-pending-order-student">${student ? student.name : 'غير معروف'}</div>
                    </div>
                    <div class="pos-pending-order-items">
                        ${order.items.map(item => {
                            const product = db.products.find(p => p.id === item.productId);
                            return `
                                <div class="pos-pending-order-item">
                                    <div class="pos-pending-order-item-name">${product ? product.name : 'منتج غير معروف'}</div>
                                    <div class="pos-pending-order-item-quantity">×${item.quantity}</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                    <div class="pos-pending-order-footer">
                        <div class="pos-pending-order-total">${order.totalPrice} ريال</div>
                        <div class="pos-pending-order-actions">
                            <button class="pos-btn pos-btn-secondary cancel-order-btn" data-order-id="${order.id}">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button class="pos-btn pos-btn-primary complete-order-btn" data-order-id="${order.id}">
                                <i class="fas fa-check"></i> إتمام
                            </button>
                        </div>
                    </div>
                `;

                pendingOrdersContainer.appendChild(orderElement);
            });

            // إضافة مستمعي الأحداث لأزرار الطلبات المعلقة
            addPendingOrderButtonEventListeners();
        }

        // عرض النافذة المنبثقة
        const pendingOrdersModal = document.getElementById('pending-orders-modal');
        if (pendingOrdersModal) {
            pendingOrdersModal.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في عرض الطلبات المعلقة:', error);
    }
}

/**
 * إضافة مستمعي الأحداث لأزرار الطلبات المعلقة
 */
function addPendingOrderButtonEventListeners() {
    try {
        // أزرار إتمام الطلب
        const completeButtons = document.querySelectorAll('.complete-order-btn');
        if (completeButtons.length > 0) {
            completeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = parseInt(this.getAttribute('data-order-id'));
                    completeOrder(orderId);
                });
            });
        }

        // أزرار إلغاء الطلب
        const cancelButtons = document.querySelectorAll('.cancel-order-btn');
        if (cancelButtons.length > 0) {
            cancelButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = parseInt(this.getAttribute('data-order-id'));
                    cancelOrder(orderId);
                });
            });
        }
    } catch (error) {
        console.error('خطأ في إضافة مستمعي الأحداث لأزرار الطلبات المعلقة:', error);
    }
}

/**
 * إتمام طلب معلق
 * @param {number} orderId - معرف الطلب
 */
function completeOrder(orderId) {
    try {
        // التأكيد قبل الإتمام
        if (confirm('هل أنت متأكد من رغبتك في إتمام هذا الطلب؟')) {
            // الحصول على قاعدة البيانات
            const db = getDatabase();

            // البحث عن الطلب
            const orderIndex = db.orders.findIndex(order => order.id === orderId);

            if (orderIndex !== -1) {
                // تحديث حالة الطلب
                db.orders[orderIndex].status = 'completed';

                // حفظ التغييرات في قاعدة البيانات
                saveDatabase(db);

                // تحديث قائمة الطلبات المعلقة
                loadPendingOrders();

                // إعادة عرض الطلبات المعلقة
                showPendingOrders();

                // عرض رسالة نجاح
                alert('تم إتمام الطلب بنجاح');
            } else {
                alert('لم يتم العثور على الطلب');
            }
        }
    } catch (error) {
        console.error('خطأ في إتمام طلب معلق:', error);
        alert('حدث خطأ أثناء إتمام الطلب');
    }
}

/**
 * إلغاء طلب معلق
 * @param {number} orderId - معرف الطلب
 */
function cancelOrder(orderId) {
    try {
        // التأكيد قبل الإلغاء
        if (confirm('هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟')) {
            // الحصول على قاعدة البيانات
            const db = getDatabase();

            // البحث عن الطلب
            const orderIndex = db.orders.findIndex(order => order.id === orderId);

            if (orderIndex !== -1) {
                // الحصول على معلومات الطلب
                const order = db.orders[orderIndex];

                // إعادة المبلغ إلى رصيد الطالب
                const studentIndex = db.users.findIndex(user => user.id === order.userId);
                if (studentIndex !== -1) {
                    db.users[studentIndex].balance += order.totalPrice;
                }

                // تحديث حالة الطلب
                db.orders[orderIndex].status = 'cancelled';

                // حفظ التغييرات في قاعدة البيانات
                saveDatabase(db);

                // تحديث قائمة الطلبات المعلقة
                loadPendingOrders();

                // إعادة عرض الطلبات المعلقة
                showPendingOrders();

                // عرض رسالة نجاح
                alert('تم إلغاء الطلب بنجاح وإعادة المبلغ إلى رصيد الطالب');
            } else {
                alert('لم يتم العثور على الطلب');
            }
        }
    } catch (error) {
        console.error('خطأ في إلغاء طلب معلق:', error);
        alert('حدث خطأ أثناء إلغاء الطلب');
    }
}

/**
 * التحقق من وجود طالب محدد من صفحة الطلاب
 */
function checkSelectedStudent() {
    try {
        // الحصول على معرف الطالب المحدد من sessionStorage
        const selectedStudentId = sessionStorage.getItem('selectedStudentId');

        if (selectedStudentId) {
            // حذف المعرف من sessionStorage
            sessionStorage.removeItem('selectedStudentId');

            // الحصول على قاعدة البيانات
            const db = getDatabase();

            // البحث عن الطالب
            const student = db.users.find(user =>
                user.role === 'student' &&
                user.id === parseInt(selectedStudentId)
            );

            if (student) {
                // عرض معلومات الطالب
                displayStudentInfo(student);

                // إضافة تأثير حركي لبطاقة معلومات الطالب
                const studentInfoCard = document.getElementById('student-info-card');
                if (studentInfoCard) {
                    studentInfoCard.classList.add('highlight-card');
                    setTimeout(() => {
                        studentInfoCard.classList.remove('highlight-card');
                    }, 2000);
                }
            }
        }
    } catch (error) {
        console.error('خطأ في التحقق من وجود طالب محدد:', error);
    }
}

/**
 * تحميل بيانات الطلاب
 */
function loadStudents() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحميل جميع الطلاب
        allStudents = db.users.filter(user => user.role === 'student');

        // تحميل الصفوف الدراسية للفلترة
        const grades = [...new Set(allStudents.map(student => student.grade))].filter(Boolean);

        // إضافة الصفوف إلى قائمة الفلترة
        const gradeFilter = document.getElementById('grade-filter');
        if (gradeFilter) {
            // الاحتفاظ بخيار "جميع الصفوف"
            const allGradesOption = gradeFilter.querySelector('option[value="all"]');
            gradeFilter.innerHTML = '';
            gradeFilter.appendChild(allGradesOption);

            // إضافة الصفوف
            grades.sort().forEach(grade => {
                const option = document.createElement('option');
                option.value = grade;
                option.textContent = grade;
                gradeFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الطلاب:', error);
    }
}

/**
 * إضافة تأثيرات حركية للصفحة
 */
function addPageAnimations() {
    try {
        // إضافة تأثير حركي للمنتجات
        const productsContainer = document.getElementById('products-container');
        if (productsContainer) {
            productsContainer.classList.add('fade-in');
        }

        // إضافة تأثير حركي للفئات
        const categoriesContainer = document.querySelector('.pos-categories');
        if (categoriesContainer) {
            categoriesContainer.classList.add('slide-in');
        }

        // إضافة تأثير حركي للبطاقات
        const cards = document.querySelectorAll('.pos-card');
        if (cards.length > 0) {
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        }

        // إضافة تأثير حركي للأزرار
        const buttons = document.querySelectorAll('.pos-btn');
        if (buttons.length > 0) {
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.add('btn-hover');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('btn-hover');
                });
            });
        }
    } catch (error) {
        console.error('خطأ في إضافة تأثيرات حركية للصفحة:', error);
    }
}

/**
 * عرض نافذة إضافة رصيد للطالب
 */
function showAddBalanceModal() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // عرض معلومات الطالب في النافذة
        const studentName = document.getElementById('balance-student-name');
        const studentId = document.getElementById('balance-student-id');

        if (studentName && studentId) {
            studentName.textContent = currentStudent.name || '-';
            studentId.textContent = 'رقم نور: ' + (currentStudent.noorId || '-');
        }

        // تعيين قيمة افتراضية للمبلغ
        const balanceAmount = document.getElementById('balance-amount');
        if (balanceAmount) {
            balanceAmount.value = 10;
        }

        // عرض النافذة
        const modal = document.getElementById('add-balance-modal');
        if (modal) {
            modal.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في عرض نافذة إضافة رصيد:', error);
    }
}

/**
 * إضافة رصيد للطالب
 */
function addStudentBalance() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // الحصول على المبلغ
        const balanceAmount = document.getElementById('balance-amount');
        if (!balanceAmount) return;

        const amount = parseFloat(balanceAmount.value);
        if (isNaN(amount) || amount <= 0) {
            alert('يرجى إدخال مبلغ صحيح');
            return;
        }

        // الحصول على طريقة الدفع
        const paymentMethod = document.getElementById('payment-method');
        const paymentMethodValue = paymentMethod ? paymentMethod.value : 'cash';

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // تحديث رصيد الطالب
        const studentIndex = db.users.findIndex(user => user.id === currentStudent.id);
        if (studentIndex !== -1) {
            db.users[studentIndex].balance += amount;
            currentStudent.balance += amount;

            // إضافة سجل للعملية
            const newTransactionId = Math.max(...(db.transactions || []).map(t => t.id), 0) + 1;

            if (!db.transactions) {
                db.transactions = [];
            }

            db.transactions.push({
                id: newTransactionId,
                userId: currentStudent.id,
                amount: amount,
                type: 'deposit',
                paymentMethod: paymentMethodValue,
                date: new Date().toISOString(),
                staffId: currentUser ? currentUser.id : null
            });

            // حفظ التغييرات في قاعدة البيانات
            saveDatabase(db);

            // تحديث عرض رصيد الطالب في الواجهة
            const studentBalance = document.getElementById('student-balance');
            if (studentBalance) {
                studentBalance.textContent = currentStudent.balance + ' ريال';

                // إضافة تأثير حركي للرصيد
                animateBalance(studentBalance, currentStudent.balance);
            }

            // إغلاق النافذة
            const modal = document.getElementById('add-balance-modal');
            if (modal) {
                modal.style.display = 'none';
            }

            // عرض رسالة نجاح
            alert(`تم إضافة ${amount} ريال إلى رصيد الطالب بنجاح`);
        } else {
            alert('حدث خطأ أثناء تحديث رصيد الطالب');
        }
    } catch (error) {
        console.error('خطأ في إضافة رصيد للطالب:', error);
        alert('حدث خطأ أثناء إضافة الرصيد');
    }
}

/**
 * عرض سجل طلبات الطالب
 */
function showStudentOrdersModal() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // عرض معلومات الطالب في النافذة
        const studentName = document.getElementById('orders-student-name');
        const studentId = document.getElementById('orders-student-id');

        if (studentName && studentId) {
            studentName.textContent = currentStudent.name || '-';
            studentId.textContent = 'رقم نور: ' + (currentStudent.noorId || '-');
        }

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // الحصول على طلبات الطالب
        const studentOrders = db.orders.filter(order =>
            order.userId === currentStudent.id
        ).sort((a, b) => new Date(b.date) - new Date(a.date)); // ترتيب حسب التاريخ (الأحدث أولاً)

        // عرض الطلبات
        const ordersContainer = document.getElementById('student-orders-container');
        if (!ordersContainer) return;

        // مسح المحتوى الحالي
        ordersContainer.innerHTML = '';

        // إذا لم تكن هناك طلبات
        if (studentOrders.length === 0) {
            ordersContainer.innerHTML = '<div class="pos-no-orders" style="text-align: center; padding: 20px; color: #999;"><i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 10px;"></i><p>لا توجد طلبات سابقة</p></div>';
        } else {
            // إنشاء عنصر لكل طلب
            studentOrders.forEach(order => {
                // تنسيق التاريخ
                const orderDate = new Date(order.date);
                const formattedDate = `${orderDate.getDate()}/${orderDate.getMonth() + 1}/${orderDate.getFullYear()}`;
                const formattedTime = `${orderDate.getHours()}:${orderDate.getMinutes().toString().padStart(2, '0')}`;

                // إنشاء عنصر الطلب
                const orderElement = document.createElement('div');
                orderElement.className = 'pos-order-item';
                orderElement.style.padding = '15px';
                orderElement.style.marginBottom = '15px';
                orderElement.style.backgroundColor = 'white';
                orderElement.style.borderRadius = '10px';
                orderElement.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.05)';
                orderElement.style.border = '1px solid var(--pos-border)';
                orderElement.style.transition = 'all 0.3s ease';

                // تحديد لون حسب حالة الطلب
                let statusColor = '';
                let statusText = '';

                switch (order.status) {
                    case 'completed':
                        statusColor = 'var(--pos-success)';
                        statusText = 'مكتمل';
                        break;
                    case 'pending':
                        statusColor = 'var(--pos-warning)';
                        statusText = 'معلق';
                        break;
                    case 'cancelled':
                        statusColor = 'var(--pos-danger)';
                        statusText = 'ملغي';
                        break;
                    default:
                        statusColor = 'var(--pos-info)';
                        statusText = order.status;
                }

                orderElement.innerHTML = `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid var(--pos-border);">
                        <div>
                            <div style="font-weight: 700; color: var(--pos-primary);">طلب #${order.id}</div>
                            <div style="color: #666; font-size: 0.9rem;">${formattedDate} - ${formattedTime}</div>
                        </div>
                        <div style="background-color: ${statusColor}; color: white; padding: 5px 10px; border-radius: 20px; font-size: 0.8rem; display: inline-block;">${statusText}</div>
                    </div>
                    <div style="margin-bottom: 10px;">
                        ${order.items.map(item => {
                            const product = db.products.find(p => p.id === item.productId);
                            const productName = product ? product.name : (item.productId === -1 ? 'مبلغ نقدي' : 'منتج غير معروف');
                            return `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <div>${productName} × ${item.quantity}</div>
                                    <div>${item.price * item.quantity} ريال</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                    <div style="text-align: left; font-weight: 700; color: var(--pos-primary); font-size: 1.1rem;">
                        الإجمالي: ${order.totalPrice} ريال
                    </div>
                `;

                ordersContainer.appendChild(orderElement);
            });
        }

        // عرض النافذة
        const modal = document.getElementById('student-orders-modal');
        if (modal) {
            modal.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في عرض سجل طلبات الطالب:', error);
    }
}

/**
 * عرض نافذة ماسح الباركود
 */
function showBarcodeScannerModal() {
    try {
        // عرض النافذة
        const modal = document.getElementById('barcode-scanner-modal');
        if (modal) {
            modal.style.display = 'block';
        }

        // تهيئة ماسح الباركود
        initBarcodeScanner();
    } catch (error) {
        console.error('خطأ في عرض نافذة ماسح الباركود:', error);
    }
}

/**
 * تهيئة ماسح الباركود
 */
function initBarcodeScanner() {
    try {
        // التحقق من وجود مكتبة QuaggaJS
        if (typeof Quagga === 'undefined') {
            // إذا لم تكن المكتبة موجودة، قم بتحميلها
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@ericblade/quagga2/dist/quagga.min.js';
            script.onload = startScanner;
            document.head.appendChild(script);
        } else {
            // إذا كانت المكتبة موجودة، ابدأ الماسح
            startScanner();
        }
    } catch (error) {
        console.error('خطأ في تهيئة ماسح الباركود:', error);
    }
}

/**
 * بدء تشغيل ماسح الباركود
 */
function startScanner() {
    try {
        const scannerContainer = document.getElementById('scanner-container');
        if (!scannerContainer) return;

        // تهيئة ماسح الباركود
        Quagga.init({
            inputStream: {
                name: 'Live',
                type: 'LiveStream',
                target: scannerContainer,
                constraints: {
                    width: 640,
                    height: 480,
                    facingMode: 'environment' // استخدام الكاميرا الخلفية
                }
            },
            locator: {
                patchSize: 'medium',
                halfSample: true
            },
            numOfWorkers: 2,
            decoder: {
                readers: ['code_128_reader', 'ean_reader', 'ean_8_reader', 'code_39_reader', 'code_93_reader', 'upc_reader', 'upc_e_reader']
            },
            locate: true
        }, function(err) {
            if (err) {
                console.error('خطأ في تهيئة ماسح الباركود:', err);
                alert('حدث خطأ أثناء تهيئة الكاميرا. يرجى التأكد من السماح بالوصول إلى الكاميرا.');
                return;
            }

            // بدء المسح
            Quagga.start();

            // حفظ مرجع للماسح
            scanner = Quagga;

            // إضافة مستمع للنتائج
            Quagga.onDetected(function(result) {
                if (result && result.codeResult && result.codeResult.code) {
                    const barcode = result.codeResult.code;

                    // إيقاف الماسح
                    Quagga.stop();
                    scanner = null;

                    // إغلاق النافذة
                    const modal = document.getElementById('barcode-scanner-modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }

                    // البحث عن الطالب باستخدام الباركود
                    searchStudentByBarcode(barcode);
                }
            });
        });
    } catch (error) {
        console.error('خطأ في بدء تشغيل ماسح الباركود:', error);
    }
}

/**
 * البحث عن طالب باستخدام الباركود
 * @param {string} barcode - الباركود
 */
function searchStudentByBarcode(barcode) {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // البحث عن الطالب باستخدام الباركود
        const student = db.users.find(user =>
            user.role === 'student' &&
            (user.barcode === barcode || user.noorId === barcode)
        );

        if (student) {
            // عرض معلومات الطالب
            displayStudentInfo(student);
        } else {
            alert('لم يتم العثور على طالب بهذا الباركود');

            // إعادة فتح نافذة الماسح
            showBarcodeScannerModal();
        }
    } catch (error) {
        console.error('خطأ في البحث عن طالب باستخدام الباركود:', error);
    }
}

/**
 * عرض نافذة قائمة الطلاب
 */
function showStudentsListModal() {
    try {
        // عرض النافذة
        const modal = document.getElementById('students-list-modal');
        if (modal) {
            modal.style.display = 'block';
        }

        // عرض قائمة الطلاب
        renderStudentsList();
    } catch (error) {
        console.error('خطأ في عرض نافذة قائمة الطلاب:', error);
    }
}

/**
 * عرض قائمة الطلاب
 */
function renderStudentsList() {
    try {
        const container = document.getElementById('students-list-container');
        if (!container) return;

        // مسح المحتوى الحالي
        container.innerHTML = '';

        // إذا لم تكن هناك طلاب
        if (allStudents.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;"><i class="fas fa-user-graduate" style="font-size: 3rem; margin-bottom: 10px;"></i><p>لا يوجد طلاب</p></div>';
            return;
        }

        // إنشاء عنصر لكل طالب
        allStudents.forEach(student => {
            const studentElement = document.createElement('div');
            studentElement.className = 'pos-student-list-item';
            studentElement.style.padding = '12px';
            studentElement.style.marginBottom = '10px';
            studentElement.style.backgroundColor = 'white';
            studentElement.style.borderRadius = '10px';
            studentElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';
            studentElement.style.border = '1px solid var(--pos-border)';
            studentElement.style.transition = 'all 0.3s ease';
            studentElement.style.cursor = 'pointer';
            studentElement.style.display = 'flex';
            studentElement.style.justifyContent = 'space-between';
            studentElement.style.alignItems = 'center';

            // الحصول على معلومات المدرسة
            const db = getDatabase();
            const school = db.schools.find(s => s.id === student.schoolId);

            studentElement.innerHTML = `
                <div>
                    <div style="font-weight: 600; color: var(--pos-dark); font-size: 1.1rem;">${student.name || '-'}</div>
                    <div style="color: #666; font-size: 0.9rem;">رقم نور: ${student.noorId || '-'}</div>
                    <div style="color: #666; font-size: 0.9rem;">${student.grade || '-'} - ${school ? school.name : '-'}</div>
                </div>
                <div style="font-weight: 700; color: var(--pos-primary);">${student.balance || 0} ريال</div>
            `;

            // إضافة حدث النقر
            studentElement.addEventListener('click', function() {
                // عرض معلومات الطالب
                displayStudentInfo(student);

                // إغلاق النافذة
                const modal = document.getElementById('students-list-modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });

            container.appendChild(studentElement);
        });
    } catch (error) {
        console.error('خطأ في عرض قائمة الطلاب:', error);
    }
}

/**
 * تصفية قائمة الطلاب
 */
function filterStudentsList() {
    try {
        // الحصول على قيمة البحث
        const searchInput = document.getElementById('students-list-search');
        const searchValue = searchInput ? searchInput.value.trim().toLowerCase() : '';

        // الحصول على قيمة الفلتر
        const gradeFilter = document.getElementById('grade-filter');
        const gradeValue = gradeFilter ? gradeFilter.value : 'all';

        // تصفية الطلاب
        let filteredStudents = [...allStudents];

        // تصفية حسب الصف
        if (gradeValue !== 'all') {
            filteredStudents = filteredStudents.filter(student => student.grade === gradeValue);
        }

        // تصفية حسب النص المدخل
        if (searchValue) {
            filteredStudents = filteredStudents.filter(student =>
                (student.name && student.name.toLowerCase().includes(searchValue)) ||
                (student.noorId && student.noorId.toLowerCase().includes(searchValue))
            );
        }

        // عرض الطلاب المصفاة
        const container = document.getElementById('students-list-container');
        if (!container) return;

        // مسح المحتوى الحالي
        container.innerHTML = '';

        // إذا لم تكن هناك طلاب بعد التصفية
        if (filteredStudents.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;"><i class="fas fa-search" style="font-size: 3rem; margin-bottom: 10px;"></i><p>لا توجد نتائج مطابقة</p></div>';
            return;
        }

        // إنشاء عنصر لكل طالب
        filteredStudents.forEach(student => {
            const studentElement = document.createElement('div');
            studentElement.className = 'pos-student-list-item';
            studentElement.style.padding = '12px';
            studentElement.style.marginBottom = '10px';
            studentElement.style.backgroundColor = 'white';
            studentElement.style.borderRadius = '10px';
            studentElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';
            studentElement.style.border = '1px solid var(--pos-border)';
            studentElement.style.transition = 'all 0.3s ease';
            studentElement.style.cursor = 'pointer';
            studentElement.style.display = 'flex';
            studentElement.style.justifyContent = 'space-between';
            studentElement.style.alignItems = 'center';

            // الحصول على معلومات المدرسة
            const db = getDatabase();
            const school = db.schools.find(s => s.id === student.schoolId);

            studentElement.innerHTML = `
                <div>
                    <div style="font-weight: 600; color: var(--pos-dark); font-size: 1.1rem;">${student.name || '-'}</div>
                    <div style="color: #666; font-size: 0.9rem;">رقم نور: ${student.noorId || '-'}</div>
                    <div style="color: #666; font-size: 0.9rem;">${student.grade || '-'} - ${school ? school.name : '-'}</div>
                </div>
                <div style="font-weight: 700; color: var(--pos-primary);">${student.balance || 0} ريال</div>
            `;

            // إضافة حدث النقر
            studentElement.addEventListener('click', function() {
                // عرض معلومات الطالب
                displayStudentInfo(student);

                // إغلاق النافذة
                const modal = document.getElementById('students-list-modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });

            container.appendChild(studentElement);
        });
    } catch (error) {
        console.error('خطأ في تصفية قائمة الطلاب:', error);
    }
}

/**
 * عرض تقرير المبيعات اليومية
 */
function showDailyReport() {
    try {
        // إخفاء قسم المنتجات
        const productsCard = document.querySelector('.pos-card');
        if (productsCard) {
            productsCard.style.display = 'none';
        }

        // عرض لوحة التقارير
        const dashboard = document.getElementById('daily-sales-dashboard');
        if (dashboard) {
            dashboard.style.display = 'block';
        }

        // تحميل بيانات المبيعات
        loadSalesData();
    } catch (error) {
        console.error('خطأ في عرض تقرير المبيعات اليومية:', error);
    }
}

/**
 * تحميل بيانات المبيعات
 */
function loadSalesData() {
    try {
        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // الحصول على تاريخ اليوم
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // تصفية الطلبات المكتملة لليوم الحالي
        const todayOrders = db.orders.filter(order => {
            const orderDate = new Date(order.date);
            orderDate.setHours(0, 0, 0, 0);
            return orderDate.getTime() === today.getTime() && order.status === 'completed';
        });

        // حساب إجمالي المبيعات
        const totalSales = todayOrders.reduce((sum, order) => sum + order.totalPrice, 0);

        // حساب عدد المنتجات المباعة
        const totalProducts = todayOrders.reduce((sum, order) => {
            return sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0);
        }, 0);

        // حساب عدد الطلاب المشترين (الفريدين)
        const uniqueStudents = [...new Set(todayOrders.map(order => order.userId))];

        // تحديث العناصر في الواجهة
        document.getElementById('today-orders-count').textContent = todayOrders.length;
        document.getElementById('today-sales-total').textContent = totalSales + ' ريال';
        document.getElementById('today-products-count').textContent = totalProducts;
        document.getElementById('today-students-count').textContent = uniqueStudents.length;

        // إنشاء الرسم البياني
        createSalesChart(todayOrders);
    } catch (error) {
        console.error('خطأ في تحميل بيانات المبيعات:', error);
    }
}

/**
 * إنشاء الرسم البياني للمبيعات
 * @param {Array} orders - الطلبات
 */
function createSalesChart(orders) {
    try {
        // الحصول على عنصر الرسم البياني
        const chartCanvas = document.getElementById('sales-chart');
        if (!chartCanvas) return;

        // تجميع البيانات حسب الساعة
        const hourlyData = {};
        for (let i = 0; i < 24; i++) {
            hourlyData[i] = 0;
        }

        // تجميع المبيعات حسب الساعة
        orders.forEach(order => {
            const orderDate = new Date(order.date);
            const hour = orderDate.getHours();
            hourlyData[hour] += order.totalPrice;
        });

        // تحويل البيانات إلى مصفوفات للرسم البياني
        const hours = Object.keys(hourlyData).map(hour => `${hour}:00`);
        const sales = Object.values(hourlyData);

        // إذا كان هناك رسم بياني سابق، قم بتدميره
        if (salesChart) {
            salesChart.destroy();
        }

        // إنشاء الرسم البياني
        salesChart = new Chart(chartCanvas, {
            type: 'bar',
            data: {
                labels: hours,
                datasets: [{
                    label: 'المبيعات (ريال)',
                    data: sales,
                    backgroundColor: 'rgba(46, 125, 50, 0.7)',
                    borderColor: 'rgba(46, 125, 50, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبيعات (ريال)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الساعة'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'المبيعات اليومية حسب الساعة',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    } catch (error) {
        console.error('خطأ في إنشاء الرسم البياني للمبيعات:', error);
    }
}

/**
 * طباعة تقرير المبيعات اليومية
 */
function printDailyReport() {
    try {
        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
            alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
            return;
        }

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // الحصول على تاريخ اليوم
        const today = new Date();
        const formattedDate = `${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;

        // الحصول على بيانات التقرير
        const ordersCount = document.getElementById('today-orders-count').textContent;
        const salesTotal = document.getElementById('today-sales-total').textContent;
        const productsCount = document.getElementById('today-products-count').textContent;
        const studentsCount = document.getElementById('today-students-count').textContent;

        // إنشاء محتوى التقرير
        const reportContent = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير المبيعات اليومية - ${formattedDate}</title>
                <style>
                    body {
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: white;
                        color: #333;
                    }
                    .report-header {
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #4caf50;
                    }
                    .report-header h1 {
                        color: #2e7d32;
                        margin: 0 0 10px;
                    }
                    .report-header p {
                        margin: 5px 0;
                        color: #666;
                    }
                    .report-stats {
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        margin-bottom: 30px;
                    }
                    .stat-card {
                        width: 45%;
                        padding: 15px;
                        margin-bottom: 20px;
                        background-color: #f5f5f5;
                        border-radius: 10px;
                        border-right: 4px solid #4caf50;
                    }
                    .stat-card h3 {
                        margin: 0 0 10px;
                        color: #2e7d32;
                    }
                    .stat-card p {
                        margin: 0;
                        font-size: 1.5rem;
                        font-weight: bold;
                    }
                    .report-footer {
                        margin-top: 50px;
                        text-align: center;
                        color: #666;
                        font-size: 0.9rem;
                    }
                    @media print {
                        body {
                            padding: 0;
                        }
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                    <p>تقرير المبيعات اليومية</p>
                    <p>${formattedDate}</p>
                </div>

                <div class="report-stats">
                    <div class="stat-card">
                        <h3>عدد الطلبات</h3>
                        <p>${ordersCount}</p>
                    </div>
                    <div class="stat-card">
                        <h3>إجمالي المبيعات</h3>
                        <p>${salesTotal}</p>
                    </div>
                    <div class="stat-card">
                        <h3>المنتجات المباعة</h3>
                        <p>${productsCount}</p>
                    </div>
                    <div class="stat-card">
                        <h3>الطلاب المشترين</h3>
                        <p>${studentsCount}</p>
                    </div>
                </div>

                <div class="report-footer">
                    <p>تم إصدار هذا التقرير بواسطة: ${currentUser ? currentUser.name : 'العامل'}</p>
                    <p>© فواصل النجاح للخدمات الإعاشة</p>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 30px;">
                    <button onclick="window.print()" style="padding: 10px 20px; background-color: #4caf50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 1rem;">طباعة التقرير</button>
                </div>
            </body>
            </html>
        `;

        // كتابة المحتوى في النافذة الجديدة
        printWindow.document.open();
        printWindow.document.write(reportContent);
        printWindow.document.close();

        // طباعة التقرير بعد تحميل الصفحة
        printWindow.onload = function() {
            setTimeout(function() {
                printWindow.focus();
                printWindow.print();
            }, 500);
        };
    } catch (error) {
        console.error('خطأ في طباعة تقرير المبيعات اليومية:', error);
    }
}

/**
 * طباعة الإيصال
 */
function printReceipt() {
    try {
        window.print();
    } catch (error) {
        console.error('خطأ في طباعة الإيصال:', error);
    }
}

/**
 * إرسال الإيصال إلى ولي الأمر
 */
function sendReceiptToParent() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('لم يتم تحديد طالب');
            return;
        }

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // البحث عن ولي الأمر
        const parent = db.users.find(user =>
            user.role === 'parent' &&
            user.children &&
            user.children.includes(currentStudent.id)
        );

        if (!parent || !parent.email) {
            alert('لم يتم العثور على بريد إلكتروني لولي الأمر');
            return;
        }

        // محاكاة إرسال الإيصال
        alert(`تم إرسال الإيصال إلى ولي الأمر (${parent.name}) على البريد الإلكتروني: ${parent.email}`);
    } catch (error) {
        console.error('خطأ في إرسال الإيصال إلى ولي الأمر:', error);
        alert('حدث خطأ أثناء إرسال الإيصال');
    }
}

/**
 * حفظ الطلب للاحقًا
 */
function saveOrderForLater() {
    try {
        // التحقق من وجود طالب محدد
        if (!currentStudent) {
            alert('يرجى تحديد طالب أولاً');
            return;
        }

        // التحقق من وجود عناصر في السلة
        if (cart.length === 0) {
            alert('السلة فارغة');
            return;
        }

        // حساب المجموع
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // الحصول على قاعدة البيانات
        const db = getDatabase();

        // إنشاء معرف جديد للطلب
        const newOrderId = Math.max(...db.orders.map(order => order.id), 0) + 1;

        // إنشاء كائن الطلب الجديد
        const newOrder = {
            id: newOrderId,
            userId: currentStudent.id,
            schoolId: currentStudent.schoolId,
            date: new Date().toISOString(),
            items: cart.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
                price: item.price
            })),
            totalPrice: total,
            status: 'pending'
        };

        // إضافة الطلب إلى قاعدة البيانات
        db.orders.push(newOrder);

        // حفظ التغييرات في قاعدة البيانات
        saveDatabase(db);

        // تحديث قائمة الطلبات المعلقة
        loadPendingOrders();

        // مسح السلة
        cart = [];
        updateCartDisplay();

        // عرض رسالة نجاح
        alert('تم حفظ الطلب بنجاح');
    } catch (error) {
        console.error('خطأ في حفظ الطلب للاحقًا:', error);
        alert('حدث خطأ أثناء حفظ الطلب');
    }
}

/**
 * تحديث التاريخ الحالي
 */
function updateCurrentDate() {
    try {
        const currentDateElement = document.getElementById('current-date');
        if (currentDateElement) {
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    } catch (error) {
        console.error('خطأ في تحديث التاريخ الحالي:', error);
    }
}