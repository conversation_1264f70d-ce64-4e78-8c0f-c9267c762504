<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ اختبار قاعدة البيانات</h1>
            <p>أدوات لفحص وإصلاح قاعدة البيانات وإدارة البيانات المخزنة</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="admin-login-quick.html">⚡ اختبار المدير</a>
            <a href="../pages/admin/dashboard.html">📊 لوحة المدير</a>
        </div>

        <div class="test-section">
            <h3>1. فحص سلامة قاعدة البيانات</h3>
            <button onclick="checkDatabaseIntegrity()">فحص سلامة قاعدة البيانات</button>
            <div id="integrity-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. عرض محتويات قاعدة البيانات</h3>
            <button onclick="showDatabaseContents()">عرض المحتويات</button>
            <div id="contents-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. إنشاء نسخة احتياطية</h3>
            <button onclick="createBackup()">إنشاء نسخة احتياطية</button>
            <button onclick="downloadBackup()">تحميل النسخة الاحتياطية</button>
            <div id="backup-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. استعادة من نسخة احتياطية</h3>
            <input type="file" id="restore-file" accept=".json">
            <button onclick="restoreFromBackup()">استعادة من ملف</button>
            <div id="restore-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. تنظيف البيانات التالفة</h3>
            <button onclick="cleanCorruptedData()">تنظيف البيانات التالفة</button>
            <div id="clean-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. إعادة تهيئة قاعدة البيانات</h3>
            <button onclick="reinitializeDatabase()" style="background: #f44336;">إعادة تهيئة كاملة</button>
            <div id="reinit-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>7. إحصائيات قاعدة البيانات</h3>
            <button onclick="showDatabaseStats()">عرض الإحصائيات</button>
            <div id="stats-result" class="result"></div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // فحص سلامة قاعدة البيانات
        function checkDatabaseIntegrity() {
            const resultDiv = document.getElementById('integrity-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري فحص سلامة قاعدة البيانات...</div>';

                let html = '';
                let issues = [];

                // فحص وجود قاعدة البيانات
                const dbString = localStorage.getItem('cafeteriaDB');
                if (!dbString) {
                    html += '<div class="error">✗ قاعدة البيانات غير موجودة</div>';
                    resultDiv.innerHTML = html;
                    return;
                }

                // فحص صحة JSON
                let db;
                try {
                    db = JSON.parse(dbString);
                    html += '<div class="success">✓ تنسيق JSON صحيح</div>';
                } catch (parseError) {
                    html += '<div class="error">✗ تنسيق JSON تالف</div>';
                    resultDiv.innerHTML = html;
                    return;
                }

                // فحص الجداول المطلوبة
                const requiredTables = ['users', 'schools', 'products', 'orders'];
                requiredTables.forEach(table => {
                    if (db[table] && Array.isArray(db[table])) {
                        html += `<div class="success">✓ جدول ${table} موجود (${db[table].length} عنصر)</div>`;
                    } else {
                        html += `<div class="error">✗ جدول ${table} مفقود أو تالف</div>`;
                        issues.push(`جدول ${table} مفقود`);
                    }
                });

                // فحص بيانات المستخدمين
                if (db.users && Array.isArray(db.users)) {
                    const adminUsers = db.users.filter(u => u.role === 'admin');
                    if (adminUsers.length === 0) {
                        html += '<div class="warning">⚠ لا يوجد مدراء في النظام</div>';
                        issues.push('لا يوجد مدراء');
                    } else {
                        html += `<div class="success">✓ يوجد ${adminUsers.length} مدير في النظام</div>`;
                    }

                    // فحص تكرار أسماء المستخدمين
                    const usernames = db.users.map(u => u.username);
                    const duplicates = usernames.filter((item, index) => usernames.indexOf(item) !== index);
                    if (duplicates.length > 0) {
                        html += `<div class="error">✗ أسماء مستخدمين مكررة: ${duplicates.join(', ')}</div>`;
                        issues.push('أسماء مستخدمين مكررة');
                    } else {
                        html += '<div class="success">✓ لا توجد أسماء مستخدمين مكررة</div>';
                    }
                }

                // النتيجة النهائية
                if (issues.length === 0) {
                    html += '<div class="success"><strong>🎉 قاعدة البيانات سليمة!</strong></div>';
                } else {
                    html += `<div class="warning"><strong>⚠️ تم العثور على ${issues.length} مشكلة</strong></div>`;
                    html += '<div class="info">المشاكل المكتشفة:</div>';
                    issues.forEach(issue => {
                        html += `<div class="warning">- ${issue}</div>`;
                    });
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في فحص قاعدة البيانات: ${error.message}</div>`;
            }
        }

        // عرض محتويات قاعدة البيانات
        function showDatabaseContents() {
            const resultDiv = document.getElementById('contents-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري عرض محتويات قاعدة البيانات...</div>';

                const db = getDatabase();
                if (!db) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let html = '<div class="info">محتويات قاعدة البيانات:</div>';
                
                Object.keys(db).forEach(table => {
                    if (Array.isArray(db[table])) {
                        html += `<div class="success">📊 ${table}: ${db[table].length} عنصر</div>`;
                        
                        if (db[table].length > 0) {
                            html += '<div class="code-block">';
                            html += JSON.stringify(db[table].slice(0, 2), null, 2);
                            if (db[table].length > 2) {
                                html += `\n... و ${db[table].length - 2} عنصر آخر`;
                            }
                            html += '</div>';
                        }
                    } else {
                        html += `<div class="warning">⚠️ ${table}: ليس مصفوفة</div>';
                    }
                });

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في عرض المحتويات: ${error.message}</div>`;
            }
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            const resultDiv = document.getElementById('backup-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري إنشاء نسخة احتياطية...</div>';

                const db = getDatabase();
                if (!db) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                const backup = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    data: db
                };

                localStorage.setItem('cafeteriaDB_backup', JSON.stringify(backup));

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إنشاء النسخة الاحتياطية بنجاح</div>
                    <div class="info">الوقت: ${new Date().toLocaleString('ar-SA')}</div>
                    <div class="info">الحجم: ${JSON.stringify(backup).length} حرف</div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إنشاء النسخة الاحتياطية: ${error.message}</div>`;
            }
        }

        // تحميل النسخة الاحتياطية
        function downloadBackup() {
            const resultDiv = document.getElementById('backup-result');
            
            try {
                const backup = localStorage.getItem('cafeteriaDB_backup');
                if (!backup) {
                    resultDiv.innerHTML = '<div class="error">✗ لا توجد نسخة احتياطية</div>';
                    return;
                }

                const blob = new Blob([backup], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `cafeteria_backup_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                resultDiv.innerHTML += '<div class="success">✓ تم تحميل النسخة الاحتياطية</div>';

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تحميل النسخة الاحتياطية: ${error.message}</div>`;
            }
        }

        // استعادة من نسخة احتياطية
        function restoreFromBackup() {
            const resultDiv = document.getElementById('restore-result');
            const fileInput = document.getElementById('restore-file');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء اختيار ملف</div>';
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    resultDiv.innerHTML = '<div class="info">🔄 جاري استعادة النسخة الاحتياطية...</div>';

                    const backupData = JSON.parse(e.target.result);
                    
                    if (backupData.data) {
                        localStorage.setItem('cafeteriaDB', JSON.stringify(backupData.data));
                        resultDiv.innerHTML = `
                            <div class="success">✓ تم استعادة النسخة الاحتياطية بنجاح</div>
                            <div class="info">تاريخ النسخة: ${new Date(backupData.timestamp).toLocaleString('ar-SA')}</div>
                            <div class="info">يرجى تحديث الصفحة لرؤية التغييرات</div>
                        `;
                    } else {
                        resultDiv.innerHTML = '<div class="error">✗ تنسيق النسخة الاحتياطية غير صحيح</div>';
                    }

                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">✗ خطأ في استعادة النسخة الاحتياطية: ${error.message}</div>`;
                }
            };

            reader.readAsText(file);
        }

        // تنظيف البيانات التالفة
        function cleanCorruptedData() {
            const resultDiv = document.getElementById('clean-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري تنظيف البيانات التالفة...</div>';

                const db = getDatabase();
                if (!db) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let cleaned = 0;
                let html = '';

                // تنظيف المستخدمين
                if (db.users) {
                    const originalCount = db.users.length;
                    db.users = db.users.filter(user => 
                        user.id && user.username && user.role
                    );
                    const removedUsers = originalCount - db.users.length;
                    if (removedUsers > 0) {
                        html += `<div class="warning">تم حذف ${removedUsers} مستخدم تالف</div>`;
                        cleaned += removedUsers;
                    }
                }

                // تنظيف المدارس
                if (db.schools) {
                    const originalCount = db.schools.length;
                    db.schools = db.schools.filter(school => 
                        school.id && school.name
                    );
                    const removedSchools = originalCount - db.schools.length;
                    if (removedSchools > 0) {
                        html += `<div class="warning">تم حذف ${removedSchools} مدرسة تالفة</div>`;
                        cleaned += removedSchools;
                    }
                }

                if (cleaned > 0) {
                    saveDatabase(db);
                    html += `<div class="success">✓ تم تنظيف ${cleaned} عنصر تالف</div>`;
                } else {
                    html += '<div class="success">✓ لا توجد بيانات تالفة</div>';
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تنظيف البيانات: ${error.message}</div>`;
            }
        }

        // إعادة تهيئة قاعدة البيانات
        function reinitializeDatabase() {
            const resultDiv = document.getElementById('reinit-result');
            
            if (!confirm('هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري إعادة تهيئة قاعدة البيانات...</div>';

                // إنشاء قاعدة بيانات جديدة
                const newDB = {
                    users: [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            role: 'admin',
                            name: 'مدير النظام'
                        }
                    ],
                    schools: [],
                    products: [],
                    orders: []
                };

                localStorage.setItem('cafeteriaDB', JSON.stringify(newDB));
                
                resultDiv.innerHTML = `
                    <div class="success">✓ تم إعادة تهيئة قاعدة البيانات بنجاح</div>
                    <div class="info">تم إنشاء حساب مدير افتراضي:</div>
                    <div class="info">اسم المستخدم: admin</div>
                    <div class="info">كلمة المرور: admin123</div>
                    <div class="warning">يرجى تحديث الصفحة لرؤية التغييرات</div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة التهيئة: ${error.message}</div>`;
            }
        }

        // عرض إحصائيات قاعدة البيانات
        function showDatabaseStats() {
            const resultDiv = document.getElementById('stats-result');
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري حساب الإحصائيات...</div>';

                const db = getDatabase();
                if (!db) {
                    resultDiv.innerHTML = '<div class="error">✗ قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                const dbString = JSON.stringify(db);
                const sizeInBytes = new Blob([dbString]).size;
                const sizeInKB = (sizeInBytes / 1024).toFixed(2);

                let html = '<div class="info">📊 إحصائيات قاعدة البيانات:</div>';
                html += `<div class="success">الحجم الإجمالي: ${sizeInKB} KB</div>`;
                html += `<div class="success">عدد الأحرف: ${dbString.length}</div>`;

                if (db.users) {
                    const admins = db.users.filter(u => u.role === 'admin').length;
                    const staff = db.users.filter(u => u.role === 'staff').length;
                    const students = db.users.filter(u => u.role === 'student').length;
                    const parents = db.users.filter(u => u.role === 'parent').length;
                    
                    html += `<div class="info">👥 المستخدمين: ${db.users.length}</div>`;
                    html += `<div class="info">- المدراء: ${admins}</div>`;
                    html += `<div class="info">- العاملين: ${staff}</div>`;
                    html += `<div class="info">- الطلاب: ${students}</div>`;
                    html += `<div class="info">- أولياء الأمور: ${parents}</div>`;
                }

                if (db.schools) {
                    html += `<div class="info">🏫 المدارس: ${db.schools.length}</div>`;
                }

                if (db.products) {
                    html += `<div class="info">🛍️ المنتجات: ${db.products.length}</div>`;
                }

                if (db.orders) {
                    html += `<div class="info">📦 الطلبات: ${db.orders.length}</div>`;
                }

                html += `<div class="info">📅 آخر تحديث: ${new Date().toLocaleString('ar-SA')}</div>`;

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في حساب الإحصائيات: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
