<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#2e7d32">
    <title>تخصيص واجهة المستخدم - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <link rel="stylesheet" href="../../assets/css/dark-mode.css">
    <link rel="stylesheet" href="../../assets/css/animations.css">
    <link rel="stylesheet" href="../../assets/css/customization.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;700&family=Almarai:wght@300;400;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
    <!-- Preload Theme Manager -->
    <script>
        // تحقق من وجود إعدادات السمة في التخزين المحلي
        const savedThemeSettings = localStorage.getItem('themeSettings');
        if (savedThemeSettings) {
            try {
                const settings = JSON.parse(savedThemeSettings);
                // تطبيق الوضع الليلي/النهاري مبكراً لتجنب وميض الشاشة
                if (settings.darkMode && settings.darkMode.enabled) {
                    document.documentElement.classList.add('dark-mode');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark-mode');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {
                console.error('خطأ في تحليل إعدادات السمة:', e);
            }
        }
    </script>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>محمد علي</strong></span>
            </div>
        </div>
    </header>

    <!-- Customization Section -->
    <div class="customization-container" data-aos="fade-up">
        <a href="javascript:history.back()" class="back-link"><i class="fas fa-arrow-right"></i> العودة</a>

        <div class="customization-header">
            <h2>تخصيص واجهة المستخدم</h2>
            <p>قم بتخصيص واجهة المستخدم حسب تفضيلاتك الشخصية، بما في ذلك الألوان والخطوط والتأثيرات الحركية</p>
        </div>

        <div class="customization-tabs">
            <button class="tab-button active" data-tab="colors">الألوان</button>
            <button class="tab-button" data-tab="fonts">الخطوط</button>
            <button class="tab-button" data-tab="animations">التأثيرات الحركية</button>
            <button class="tab-button" data-tab="themes">السمات</button>
        </div>

        <!-- Colors Tab -->
        <div class="tab-content active" id="colors-tab">
            <div class="colors-section">
                <h3>تخصيص الألوان</h3>
                <div class="color-pickers">
                    <div class="color-picker">
                        <label for="primary-color">اللون الرئيسي</label>
                        <input type="color" id="primary-color" value="#2e7d32">
                        <span class="color-value" id="primary-color-value">#2e7d32</span>
                    </div>
                    <div class="color-picker">
                        <label for="secondary-color">اللون الثانوي</label>
                        <input type="color" id="secondary-color" value="#f9a825">
                        <span class="color-value" id="secondary-color-value">#f9a825</span>
                    </div>
                    <div class="color-picker">
                        <label for="dark-color">اللون الداكن</label>
                        <input type="color" id="dark-color" value="#1b5e20">
                        <span class="color-value" id="dark-color-value">#1b5e20</span>
                    </div>
                    <div class="color-picker">
                        <label for="light-color">اللون الفاتح</label>
                        <input type="color" id="light-color" value="#e8f5e9">
                        <span class="color-value" id="light-color-value">#e8f5e9</span>
                    </div>
                    <div class="color-picker">
                        <label for="text-color">لون النص</label>
                        <input type="color" id="text-color" value="#333333">
                        <span class="color-value" id="text-color-value">#333333</span>
                    </div>
                    <div class="color-picker">
                        <label for="background-color">لون الخلفية</label>
                        <input type="color" id="background-color" value="#ffffff">
                        <span class="color-value" id="background-color-value">#ffffff</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fonts Tab -->
        <div class="tab-content" id="fonts-tab">
            <div class="fonts-section">
                <h3>تخصيص الخطوط</h3>
                <div class="font-options">
                    <div class="font-option">
                        <label for="font-family">نوع الخط</label>
                        <select id="font-family">
                            <option value="Tajawal">Tajawal</option>
                            <option value="Cairo">Cairo</option>
                            <option value="Almarai">Almarai</option>
                        </select>
                    </div>
                    <div class="font-option">
                        <label for="font-size">حجم الخط</label>
                        <select id="font-size">
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                    <div class="font-option">
                        <label for="font-weight">وزن الخط</label>
                        <select id="font-weight">
                            <option value="light">خفيف</option>
                            <option value="normal" selected>عادي</option>
                            <option value="bold">ثقيل</option>
                        </select>
                    </div>
                </div>

                <div class="font-preview">
                    <h4>معاينة الخط</h4>
                    <p class="font-preview-text" style="font-size: 1.5rem;">هذا مثال على نص بحجم كبير</p>
                    <p class="font-preview-text" style="font-size: 1rem;">هذا مثال على نص بحجم متوسط</p>
                    <p class="font-preview-text" style="font-size: 0.8rem;">هذا مثال على نص بحجم صغير</p>
                </div>
            </div>
        </div>

        <!-- Animations Tab -->
        <div class="tab-content" id="animations-tab">
            <div class="animations-section">
                <h3>تخصيص التأثيرات الحركية</h3>
                <div class="animation-options">
                    <div class="animation-option">
                        <label>تفعيل التأثيرات الحركية</label>
                        <div class="animation-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox" id="animations-enabled" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="toggle-label">مفعل</span>
                        </div>
                    </div>

                    <div class="animation-option">
                        <label>سرعة التأثيرات الحركية</label>
                        <div class="animation-speed">
                            <input type="range" min="0.5" max="2" step="0.1" value="1" class="speed-slider" id="animation-speed">
                            <span class="speed-value" id="speed-value">عادي (1x)</span>
                        </div>
                    </div>

                    <div class="animation-option">
                        <label>معاينة التأثيرات الحركية</label>
                        <div class="animation-preview">
                            <button class="btn btn-primary" id="preview-fade">تأثير الظهور</button>
                            <button class="btn btn-primary" id="preview-bounce">تأثير القفز</button>
                            <button class="btn btn-primary" id="preview-shake">تأثير الاهتزاز</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Themes Tab -->
        <div class="tab-content" id="themes-tab">
            <div class="themes-section">
                <h3>السمات المتاحة</h3>
                <div class="themes-grid" id="themes-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>

                <div class="themes-actions">
                    <button class="btn btn-primary" id="save-current-theme">حفظ الإعدادات الحالية كسمة جديدة</button>
                </div>
            </div>
        </div>

        <div class="actions">
            <button id="apply-settings" class="action-btn action-btn-primary"><i class="fas fa-check"></i> تطبيق الإعدادات</button>
            <button id="reset-settings" class="action-btn action-btn-secondary"><i class="fas fa-undo"></i> إعادة تعيين</button>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/theme-manager.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');

            // تهيئة مكتبة AOS للتأثيرات الحركية
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });

            // التحقق من تسجيل دخول المستخدم
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser) {
                console.warn('لم يتم العثور على بيانات المستخدم، جاري التحويل إلى صفحة تسجيل الدخول');
                window.location.href = '../auth/login.html';
                return;
            }

            // عرض اسم المستخدم
            const userNameEl = document.getElementById('user-name');
            if (userNameEl) {
                userNameEl.innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';
            }

            // التحقق من وجود مدير السمات
            if (!window.ThemeManager) {
                console.error('مدير السمات غير متاح، جاري محاولة تحميله');

                // محاولة تحميل ملف مدير السمات
                const script = document.createElement('script');
                script.src = '../../assets/js/theme-manager.js';
                script.onload = function() {
                    console.log('تم تحميل مدير السمات بنجاح');

                    // تهيئة مدير السمات
                    if (window.ThemeManager) {
                        window.ThemeManager.init();

                        // تحميل الإعدادات والسمات بعد تهيئة مدير السمات
                        setTimeout(function() {
                            loadCurrentSettings();
                            loadAvailableThemes();
                        }, 500);
                    }
                };
                script.onerror = function() {
                    console.error('فشل في تحميل مدير السمات');
                    alert('حدث خطأ في تحميل مدير السمات. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                };
                document.head.appendChild(script);
            } else {
                console.log('مدير السمات متاح، جاري تحميل الإعدادات');

                // تحميل الإعدادات الحالية
                loadCurrentSettings();

                // تحميل السمات المتاحة
                loadAvailableThemes();
            }

            // إعداد مستمعي الأحداث
            setupEventListeners();
        });

        // تحميل الإعدادات الحالية
        function loadCurrentSettings() {
            try {
                // التحقق من وجود مدير السمات
                if (!window.ThemeManager) {
                    console.error('مدير السمات غير متاح');
                    return;
                }

                const settings = window.ThemeManager.currentSettings;
                console.log('تحميل الإعدادات الحالية:', settings);

                // التحقق من وجود الإعدادات
                if (!settings || !settings.colors || !settings.fonts || !settings.animations) {
                    console.error('الإعدادات غير مكتملة');
                    return;
                }

                // تحديث حقول الألوان
                const primaryColorEl = document.getElementById('primary-color');
                const primaryColorValueEl = document.getElementById('primary-color-value');
                if (primaryColorEl && primaryColorValueEl) {
                    primaryColorEl.value = settings.colors.primary;
                    primaryColorValueEl.textContent = settings.colors.primary;
                }

                const secondaryColorEl = document.getElementById('secondary-color');
                const secondaryColorValueEl = document.getElementById('secondary-color-value');
                if (secondaryColorEl && secondaryColorValueEl) {
                    secondaryColorEl.value = settings.colors.secondary;
                    secondaryColorValueEl.textContent = settings.colors.secondary;
                }

                const darkColorEl = document.getElementById('dark-color');
                const darkColorValueEl = document.getElementById('dark-color-value');
                if (darkColorEl && darkColorValueEl) {
                    darkColorEl.value = settings.colors.dark;
                    darkColorValueEl.textContent = settings.colors.dark;
                }

                const lightColorEl = document.getElementById('light-color');
                const lightColorValueEl = document.getElementById('light-color-value');
                if (lightColorEl && lightColorValueEl) {
                    lightColorEl.value = settings.colors.light;
                    lightColorValueEl.textContent = settings.colors.light;
                }

                const textColorEl = document.getElementById('text-color');
                const textColorValueEl = document.getElementById('text-color-value');
                if (textColorEl && textColorValueEl) {
                    textColorEl.value = settings.colors.text;
                    textColorValueEl.textContent = settings.colors.text;
                }

                const backgroundColorEl = document.getElementById('background-color');
                const backgroundColorValueEl = document.getElementById('background-color-value');
                if (backgroundColorEl && backgroundColorValueEl) {
                    backgroundColorEl.value = settings.colors.background;
                    backgroundColorValueEl.textContent = settings.colors.background;
                }

                // تحديث حقول الخطوط
                const fontFamilyEl = document.getElementById('font-family');
                if (fontFamilyEl) {
                    fontFamilyEl.value = settings.fonts.main;
                }

                const fontSizeEl = document.getElementById('font-size');
                if (fontSizeEl) {
                    fontSizeEl.value = settings.fonts.size;
                }

                const fontWeightEl = document.getElementById('font-weight');
                if (fontWeightEl) {
                    fontWeightEl.value = settings.fonts.weight;
                }

                // تحديث معاينة الخط
                updateFontPreview();

                // تحديث حقول التأثيرات الحركية
                const animationsEnabledEl = document.getElementById('animations-enabled');
                if (animationsEnabledEl) {
                    animationsEnabledEl.checked = settings.animations.enabled;
                }

                const speedSlider = document.getElementById('animation-speed');
                if (speedSlider) {
                    speedSlider.value = settings.animations.speed === 'slow' ? 0.5 :
                                      settings.animations.speed === 'fast' ? 1.5 : 1;

                    updateSpeedValue(speedSlider.value);
                }

                console.log('تم تحميل الإعدادات الحالية بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات الحالية:', error);
            }
        }

        // تحميل السمات المتاحة
        function loadAvailableThemes() {
            try {
                const db = getDatabase();
                const themesGrid = document.getElementById('themes-grid');

                if (!themesGrid) {
                    console.error('لم يتم العثور على عنصر themes-grid');
                    return;
                }

                themesGrid.innerHTML = '';

                if (!db || !db.settings || !db.settings.themes || !Array.isArray(db.settings.themes)) {
                    console.error('لم يتم العثور على بيانات السمات في قاعدة البيانات');
                    return;
                }

                // الحصول على السمة الحالية
                const currentThemeId = getCurrentTheme();
                console.log('السمة الحالية:', currentThemeId);

                db.settings.themes.forEach(theme => {
                    const themeCard = document.createElement('div');
                    themeCard.className = 'theme-card';
                    themeCard.setAttribute('data-id', theme.id);

                    // تحديد السمة النشطة
                    if (theme.id === currentThemeId) {
                        themeCard.classList.add('active');
                    }

                    themeCard.innerHTML = `
                        <div class="theme-preview">
                            <div class="theme-header" style="background-color: ${theme.primaryColor};">
                                العنوان
                            </div>
                            <div class="theme-body">
                                <div class="theme-sidebar" style="background-color: ${theme.darkColor};"></div>
                                <div class="theme-content" style="background-color: ${theme.lightColor};">
                                    <div class="card"></div>
                                    <div class="card"></div>
                                </div>
                            </div>
                        </div>
                        <div class="theme-name">${theme.name}</div>
                    `;

                    // إضافة حدث النقر
                    themeCard.addEventListener('click', function() {
                        // إزالة الفئة النشطة من جميع البطاقات
                        document.querySelectorAll('.theme-card').forEach(card => {
                            card.classList.remove('active');
                        });

                        // إضافة الفئة النشطة للبطاقة المحددة
                        this.classList.add('active');

                        // تطبيق السمة
                        applyTheme(theme.id);
                    });

                    themesGrid.appendChild(themeCard);
                });

                console.log('تم تحميل السمات المتاحة بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل السمات المتاحة:', error);
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // مستمعي أحداث علامات التبويب
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // إزالة الفئة النشطة من جميع الأزرار والمحتويات
                    document.querySelectorAll('.tab-button').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });

                    // إضافة الفئة النشطة للزر والمحتوى المحدد
                    this.classList.add('active');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });

            // مستمعي أحداث حقول الألوان
            document.querySelectorAll('.color-picker input[type="color"]').forEach(input => {
                input.addEventListener('input', function() {
                    const valueSpan = document.getElementById(`${this.id}-value`);
                    if (valueSpan) {
                        valueSpan.textContent = this.value;
                    }
                });
            });

            // مستمعي أحداث حقول الخطوط
            document.getElementById('font-family').addEventListener('change', updateFontPreview);
            document.getElementById('font-size').addEventListener('change', updateFontPreview);
            document.getElementById('font-weight').addEventListener('change', updateFontPreview);

            // مستمعي أحداث التأثيرات الحركية
            document.getElementById('animations-enabled').addEventListener('change', function() {
                document.querySelector('.toggle-label').textContent = this.checked ? 'مفعل' : 'معطل';
            });

            document.getElementById('animation-speed').addEventListener('input', function() {
                updateSpeedValue(this.value);
            });

            // أزرار معاينة التأثيرات الحركية
            document.getElementById('preview-fade').addEventListener('click', function() {
                this.classList.add('animate-fade-in');
                setTimeout(() => {
                    this.classList.remove('animate-fade-in');
                }, 1000);
            });

            document.getElementById('preview-bounce').addEventListener('click', function() {
                this.classList.add('animate-bounce');
                setTimeout(() => {
                    this.classList.remove('animate-bounce');
                }, 2000);
            });

            document.getElementById('preview-shake').addEventListener('click', function() {
                this.classList.add('animate-shake');
                setTimeout(() => {
                    this.classList.remove('animate-shake');
                }, 800);
            });

            // مستمعي أحداث السمات
            document.querySelectorAll('.theme-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.theme-card').forEach(c => {
                        c.classList.remove('active');
                    });
                    this.classList.add('active');
                });
            });

            // زر حفظ السمة الحالية
            document.getElementById('save-current-theme').addEventListener('click', saveCurrentTheme);

            // زر تطبيق الإعدادات
            document.getElementById('apply-settings').addEventListener('click', applySettings);

            // زر إعادة تعيين الإعدادات
            document.getElementById('reset-settings').addEventListener('click', resetSettings);
        }

        // تحديث معاينة الخط
        function updateFontPreview() {
            const fontFamily = document.getElementById('font-family').value;
            const fontSize = document.getElementById('font-size').value;
            const fontWeight = document.getElementById('font-weight').value;

            // تحويل قيم الحجم والوزن إلى قيم CSS
            let fontSizeValue = '16px';
            switch (fontSize) {
                case 'small': fontSizeValue = '14px'; break;
                case 'large': fontSizeValue = '18px'; break;
            }

            let fontWeightValue = '400';
            switch (fontWeight) {
                case 'light': fontWeightValue = '300'; break;
                case 'bold': fontWeightValue = '700'; break;
            }

            // تطبيق الخط على معاينة الخط
            const previewTexts = document.querySelectorAll('.font-preview-text');
            previewTexts.forEach(text => {
                text.style.fontFamily = fontFamily;
                text.style.fontWeight = fontWeightValue;
            });
        }

        // تحديث قيمة سرعة التأثيرات الحركية
        function updateSpeedValue(value) {
            const speedValue = document.getElementById('speed-value');
            let speedText = 'عادي (1x)';

            if (value < 0.7) {
                speedText = 'بطيء (0.5x)';
            } else if (value > 1.3) {
                speedText = 'سريع (1.5x)';
            }

            speedValue.textContent = speedText;
        }

        // حفظ السمة الحالية
        function saveCurrentTheme() {
            const themeName = prompt('أدخل اسم السمة الجديدة:');

            if (themeName) {
                const db = getDatabase();

                // إنشاء سمة جديدة
                const newTheme = {
                    id: db.settings.themes.length + 1,
                    name: themeName,
                    primaryColor: document.getElementById('primary-color').value,
                    secondaryColor: document.getElementById('secondary-color').value,
                    darkColor: document.getElementById('dark-color').value,
                    lightColor: document.getElementById('light-color').value
                };

                // إضافة السمة إلى قاعدة البيانات
                db.settings.themes.push(newTheme);
                saveDatabase(db);

                // إعادة تحميل السمات المتاحة
                loadAvailableThemes();

                alert('تم حفظ السمة الجديدة بنجاح!');
            }
        }

        // تطبيق الإعدادات
        function applySettings() {
            try {
                // التحقق من وجود مدير السمات
                if (!window.ThemeManager) {
                    console.error('مدير السمات غير متاح');
                    alert('حدث خطأ: مدير السمات غير متاح');
                    return;
                }

                // التحقق من وجود عناصر النموذج
                const primaryColorEl = document.getElementById('primary-color');
                const secondaryColorEl = document.getElementById('secondary-color');
                const darkColorEl = document.getElementById('dark-color');
                const lightColorEl = document.getElementById('light-color');
                const textColorEl = document.getElementById('text-color');
                const backgroundColorEl = document.getElementById('background-color');
                const fontFamilyEl = document.getElementById('font-family');
                const fontSizeEl = document.getElementById('font-size');
                const fontWeightEl = document.getElementById('font-weight');
                const animationsEnabledEl = document.getElementById('animations-enabled');
                const animationSpeedEl = document.getElementById('animation-speed');

                if (!primaryColorEl || !secondaryColorEl || !darkColorEl || !lightColorEl ||
                    !textColorEl || !backgroundColorEl || !fontFamilyEl || !fontSizeEl ||
                    !fontWeightEl || !animationsEnabledEl || !animationSpeedEl) {
                    console.error('بعض عناصر النموذج غير موجودة');
                    alert('حدث خطأ: بعض عناصر النموذج غير موجودة');
                    return;
                }

                // الحصول على الإعدادات من النموذج
                const settings = {
                    colors: {
                        primary: primaryColorEl.value,
                        secondary: secondaryColorEl.value,
                        dark: darkColorEl.value,
                        light: lightColorEl.value,
                        text: textColorEl.value,
                        background: backgroundColorEl.value,
                        surface: backgroundColorEl.value
                    },
                    fonts: {
                        main: fontFamilyEl.value,
                        size: fontSizeEl.value,
                        weight: fontWeightEl.value
                    },
                    animations: {
                        enabled: animationsEnabledEl.checked,
                        speed: getSpeedFromSlider(animationSpeedEl.value)
                    },
                    darkMode: window.ThemeManager.currentSettings.darkMode
                };

                console.log('الإعدادات الجديدة:', settings);

                // تحديث الإعدادات في مدير السمات
                window.ThemeManager.currentSettings = settings;

                // تطبيق الإعدادات
                window.ThemeManager.applySettings();

                // حفظ الإعدادات
                window.ThemeManager.saveSettings();

                // إظهار رسالة نجاح
                const message = document.createElement('div');
                message.className = 'success-message';
                message.textContent = 'تم تطبيق الإعدادات بنجاح!';
                message.style.position = 'fixed';
                message.style.top = '20px';
                message.style.right = '20px';
                message.style.padding = '15px 25px';
                message.style.backgroundColor = '#4caf50';
                message.style.color = 'white';
                message.style.borderRadius = '5px';
                message.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                message.style.zIndex = '9999';
                message.style.opacity = '0';
                message.style.transition = 'opacity 0.3s ease';

                document.body.appendChild(message);

                // إظهار الرسالة
                setTimeout(() => {
                    message.style.opacity = '1';
                }, 10);

                // إخفاء الرسالة بعد 3 ثوانٍ
                setTimeout(() => {
                    message.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(message);
                    }, 300);
                }, 3000);
            } catch (error) {
                console.error('خطأ في تطبيق الإعدادات:', error);
                alert('حدث خطأ أثناء تطبيق الإعدادات: ' + error.message);
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            try {
                if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                    // التحقق من وجود مدير السمات
                    if (!window.ThemeManager) {
                        console.error('مدير السمات غير متاح');
                        alert('حدث خطأ: مدير السمات غير متاح');
                        return;
                    }

                    // إعادة تعيين الإعدادات إلى القيم الافتراضية
                    window.ThemeManager.currentSettings = JSON.parse(JSON.stringify(window.ThemeManager.defaultSettings));

                    // تطبيق الإعدادات
                    window.ThemeManager.applySettings();

                    // حفظ الإعدادات
                    window.ThemeManager.saveSettings();

                    // إعادة تحميل الإعدادات في النموذج
                    loadCurrentSettings();

                    // إظهار رسالة نجاح
                    const message = document.createElement('div');
                    message.className = 'success-message';
                    message.textContent = 'تم إعادة تعيين الإعدادات بنجاح!';
                    message.style.position = 'fixed';
                    message.style.top = '20px';
                    message.style.right = '20px';
                    message.style.padding = '15px 25px';
                    message.style.backgroundColor = '#2196f3';
                    message.style.color = 'white';
                    message.style.borderRadius = '5px';
                    message.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                    message.style.zIndex = '9999';
                    message.style.opacity = '0';
                    message.style.transition = 'opacity 0.3s ease';

                    document.body.appendChild(message);

                    // إظهار الرسالة
                    setTimeout(() => {
                        message.style.opacity = '1';
                    }, 10);

                    // إخفاء الرسالة بعد 3 ثوانٍ
                    setTimeout(() => {
                        message.style.opacity = '0';
                        setTimeout(() => {
                            document.body.removeChild(message);
                        }, 300);
                    }, 3000);
                }
            } catch (error) {
                console.error('خطأ في إعادة تعيين الإعدادات:', error);
                alert('حدث خطأ أثناء إعادة تعيين الإعدادات: ' + error.message);
            }
        }

        // الحصول على قيمة السرعة من شريط التمرير
        function getSpeedFromSlider(value) {
            if (value < 0.7) {
                return 'slow';
            } else if (value > 1.3) {
                return 'fast';
            } else {
                return 'normal';
            }
        }
    </script>
</body>
</html>
