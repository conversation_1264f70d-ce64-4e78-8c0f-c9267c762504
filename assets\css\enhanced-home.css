/* الشاشة الرئيسية المحسنة والمتقدمة */

/* متغيرات الألوان */
:root {
    --primary-color: #2e7d32;
    --secondary-color: #f9a825;
    --dark-color: #1b5e20;
    --light-color: #e8f5e9;
    --text-color: #333;
    --text-muted: #666;
    --white: #ffffff;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow-x: hidden;
}

/* الشريط العلوي المتقدم */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(46, 125, 50, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 8px 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.12);
    padding: 6px 0;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.header .logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header .logo a {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: var(--text-color);
}

.header .logo-img {
    width: 35px;
    height: 35px;
    transition: var(--transition);
}

.header .logo h1 {
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.header .logo:hover .logo-img {
    transform: scale(1.1) rotate(5deg);
}

.header .main-nav {
    display: flex;
    align-items: center;
}

.header .main-nav ul {
    display: flex;
    align-items: center;
    gap: 25px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.header .main-nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 8px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.header .main-nav ul li a:hover,
.header .main-nav ul li a.active {
    color: var(--primary-color);
    background: rgba(46, 125, 50, 0.1);
}

.header .main-nav ul li a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.header .main-nav ul li a:hover::before,
.header .main-nav ul li a.active::before {
    width: 80%;
}

/* زر تسجيل الدخول المتقدم */
.header .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.header .btn-primary:hover::before {
    left: 100%;
}

.header .btn-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
    border-color: var(--secondary-color);
}

.header .btn-primary:active {
    transform: translateY(0) scale(0.98);
}

/* تأثير النبض للزر */
.header .btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(249, 168, 37, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.header .btn-primary:hover::after {
    width: 300px;
    height: 300px;
}

.header .menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--primary-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.header .menu-toggle:hover {
    background: rgba(46, 125, 50, 0.1);
    transform: scale(1.1);
}

/* الحاوية */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section المحسن */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(46, 125, 50, 0.9), rgba(27, 94, 32, 0.9)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/><circle cx="200" cy="150" r="3" fill="rgba(249,168,37,0.6)"/><circle cx="800" cy="200" r="2" fill="rgba(255,255,255,0.4)"/><circle cx="400" cy="400" r="4" fill="rgba(249,168,37,0.5)"/><circle cx="1000" cy="300" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="600" cy="600" r="3" fill="rgba(249,168,37,0.4)"/><circle cx="300" cy="700" r="2" fill="rgba(255,255,255,0.5)"/></svg>');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: radial-gradient(circle at 30% 70%, rgba(249, 168, 37, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.hero-shapes .shape {
    position: absolute;
    opacity: 0.08;
    animation: float 20s ease-in-out infinite;
}

.shape-1 {
    top: 15%;
    left: 10%;
    width: 60px;
    height: 60px;
    background: var(--secondary-color);
    border-radius: 50%;
    animation-delay: 0s;
}

.shape-2 {
    top: 25%;
    right: 15%;
    width: 40px;
    height: 40px;
    background: var(--white);
    transform: rotate(45deg);
    animation-delay: 5s;
}

.shape-3 {
    bottom: 35%;
    left: 20%;
    width: 80px;
    height: 80px;
    background: var(--secondary-color);
    border-radius: 15px;
    animation-delay: 10s;
}

.shape-4 {
    bottom: 20%;
    right: 25%;
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: 50%;
    animation-delay: 15s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.08;
    }
    50% {
        transform: translateY(-15px) rotate(180deg);
        opacity: 0.12;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 25px;
    line-height: 1.1;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);
    }
    100% {
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 40px rgba(255, 255, 255, 0.2);
    }
}

.hero-title .highlight {
    color: var(--secondary-color);
    position: relative;
    display: inline-block;
    background: linear-gradient(135deg, var(--secondary-color) 0%, #ffb74d 50%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: highlightPulse 2s ease-in-out infinite alternate;
}

@keyframes highlightPulse {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    100% {
        transform: scale(1.02);
        filter: brightness(1.1);
    }
}

.hero-title .highlight::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(249, 168, 37, 0.2) 0%, transparent 70%);
    border-radius: 15px;
    z-index: -1;
    animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(0.95);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, transparent 0%, var(--secondary-color) 20%, var(--secondary-color) 80%, transparent 100%);
    border-radius: 2px;
    animation: underlineGlow 2s ease-in-out infinite alternate;
}

@keyframes underlineGlow {
    0% {
        box-shadow: 0 0 5px rgba(249, 168, 37, 0.5);
    }
    100% {
        box-shadow: 0 0 15px rgba(249, 168, 37, 0.8);
    }
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.hero-feature i {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.hero-btn-primary {
    background: var(--secondary-color);
    color: var(--dark-color);
    box-shadow: 0 4px 15px rgba(249, 168, 37, 0.3);
}

.hero-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(249, 168, 37, 0.4);
}

.hero-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* شارات الأقسام */
.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
}

/* قسم المميزات */
.features-section {
    padding: 60px 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 18px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: var(--white);
    padding: 20px 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.feature-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: var(--white);
    font-size: 1.2rem;
}

.feature-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 10px;
    line-height: 1.3;
}

.feature-description {
    color: var(--text-muted);
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.feature-link {
    color: var(--primary-color);
    font-size: 1rem;
    transition: var(--transition);
}

.feature-card:hover .feature-link {
    transform: translateX(-3px);
}

/* قسم الوجبات */
.meals-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.meals-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.meal-card {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.meal-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.meal-card.featured {
    border: 2px solid var(--secondary-color);
    transform: scale(1.05);
}

.meal-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--secondary-color);
    color: var(--dark-color);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.meal-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
}

.meal-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 15px;
}

.meal-description {
    color: var(--text-muted);
    margin-bottom: 20px;
    line-height: 1.6;
}

.meal-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.meal-price span {
    font-size: 1rem;
    color: var(--text-muted);
}

.meal-rating {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 20px;
}

.meal-rating i {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.meals-cta {
    text-align: center;
}

.cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: var(--white);
    padding: 15px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* قسم عن المقصف */
.about-section {
    padding: 80px 0;
    background: var(--white);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-description {
    font-size: 1.2rem;
    color: var(--text-muted);
    line-height: 1.8;
    margin-bottom: 40px;
}

.about-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    color: var(--text-color);
}

.about-feature i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* قسم الاتصال */
.contact-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    color: var(--white);
}

.contact-section .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
}

.contact-section .section-title {
    color: var(--white);
}

.contact-section .section-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.contact-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.contact-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.contact-card .contact-icon {
    width: 60px;
    height: 60px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--dark-color);
    font-size: 1.5rem;
}

.contact-card h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.contact-card p {
    opacity: 0.9;
}

.contact-cta {
    text-align: center;
}

.contact-cta .cta-btn {
    background: var(--secondary-color);
    color: var(--dark-color);
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    /* الشريط العلوي للأجهزة المحمولة */
    .header .main-nav ul {
        display: none;
    }

    .header .menu-toggle {
        display: block;
    }

    .header .logo h1 {
        font-size: 1rem;
    }

    .header .btn-primary {
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    /* العنوان للأجهزة المحمولة */
    .hero-title {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-features {
        gap: 20px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        gap: 20px;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .meals-showcase,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .about-features {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
}

@media (max-width: 640px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

@media (max-width: 480px) {
    /* الشريط العلوي للأجهزة الصغيرة */
    .header .logo h1 {
        font-size: 0.9rem;
    }

    .header .btn-primary {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    /* العنوان للأجهزة الصغيرة */
    .hero-title {
        font-size: 2.2rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .hero-btn,
    .cta-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 300px;
    }

    .feature-card {
        padding: 15px 10px;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .feature-title {
        font-size: 0.9rem;
    }

    .feature-description {
        font-size: 0.8rem;
    }
}
