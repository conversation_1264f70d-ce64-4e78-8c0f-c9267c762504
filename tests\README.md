# 🧪 مركز اختبارات نظام المقاصف الذكية

مجموعة شاملة من أدوات الاختبار والتشخيص لنظام إدارة المقاصف الذكية.

## 📁 هيكل المجلد

```
tests/
├── index.html                  # الصفحة الرئيسية لمركز الاختبارات
├── registration-test.html      # اختبار نظام التسجيل
├── login-test.html            # اختبار نظام تسجيل الدخول
└── README.md                  # هذا الملف
```

## 🚀 كيفية الاستخدام

### 1. الصفحة الرئيسية
افتح `index.html` للوصول إلى جميع أدوات الاختبار من مكان واحد.

### 2. اختبار نظام التسجيل
- **الملف**: `registration-test.html`
- **الوظيفة**: اختبار إنشاء الحسابات الجديدة
- **الميزات**:
  - اختبار جميع أنواع المستخدمين (طلاب، عاملين، أولياء أمور، مديرين، مدارس)
  - التحقق من تفرد البيانات (رقم نور، رقم الإقامة)
  - إنشاء مستخدمين عشوائيين للاختبار
  - فحص قاعدة البيانات
  - أدوات تنظيف البيانات

### 3. اختبار نظام تسجيل الدخول
- **الملف**: `login-test.html`
- **الوظيفة**: اختبار المصادقة وتسجيل الدخول
- **الميزات**:
  - عرض المستخدمين المتاحين
  - اختبار تسجيل الدخول لجميع الأنواع
  - اختبارات سريعة مع بيانات محددة مسبقاً
  - إنشاء مستخدمين تجريبيين
  - فحص نظام المصادقة
  - اختبار الجلسات

## 🔧 الميزات الرئيسية

### ✅ اختبار التسجيل
- **إنشاء حسابات جديدة**: اختبار إنشاء حسابات لجميع أنواع المستخدمين
- **التحقق من التفرد**: 
  - رقم نور الطالب (فريد لكل طالب)
  - رقم الإقامة (خاص بولي الأمر)
  - اسم المستخدم (فريد لجميع المستخدمين)
  - البريد الإلكتروني (فريد لجميع المستخدمين)
- **حقول مخصصة**:
  - **الطلاب**: رقم نور الطالب، المدرسة، الصف الدراسي
  - **أولياء الأمور**: رقم الإقامة، رقم نور الطالب المرتبط
  - **المدارس**: اسم المدرسة، العنوان، رقم الهاتف
  - **العاملين**: المدرسة، الصلاحيات
  - **المديرين**: صلاحيات كاملة

### ✅ اختبار تسجيل الدخول
- **المصادقة**: التحقق من اسم المستخدم وكلمة المرور ونوع المستخدم
- **الجلسات**: اختبار حفظ واستعادة بيانات الجلسة
- **التوجيه**: التحقق من التوجيه الصحيح حسب نوع المستخدم
- **التبديل**: اختبار التبديل بين أنواع المستخدمين المختلفة

### ✅ إدارة البيانات
- **فحص قاعدة البيانات**: التحقق من سلامة وهيكل البيانات
- **إنشاء بيانات تجريبية**: إضافة مستخدمين نموذجيين للاختبار
- **تصدير البيانات**: حفظ نسخة احتياطية من قاعدة البيانات
- **مسح البيانات**: إعادة تعيين النظام بالكامل

## 📊 أنواع المستخدمين المدعومة

| النوع | الوصف | الحقول الخاصة |
|-------|--------|----------------|
| **طالب** | الطلاب والدارسين | رقم نور الطالب، المدرسة، الصف |
| **ولي أمر** | أولياء الأمور | رقم الإقامة، رقم نور الطالب |
| **عامل** | عاملو المقصف | المدرسة، الصلاحيات |
| **مدير** | مديرو النظام | صلاحيات كاملة |
| **مدرسة** | إدارة المدارس | اسم المدرسة، العنوان |

## 🗄️ قاعدة البيانات

### التخزين
- **النوع**: localStorage
- **المفتاح**: `cafeteriaDB`
- **التنسيق**: JSON

### هيكل البيانات
```json
{
  "users": [
    {
      "id": 1,
      "username": "student1",
      "password": "password123",
      "role": "student",
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "status": "active",
      "noorId": "123456789",
      "schoolId": 1,
      "balance": 50,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "schools": [],
  "products": [],
  "orders": []
}
```

## 🔒 الأمان والخصوصية

- **كلمات المرور**: مخزنة بشكل عادي (حسب تفضيل المستخدم)
- **البيانات المحلية**: جميع البيانات مخزنة محلياً في المتصفح
- **لا توجد اتصالات خارجية**: النظام يعمل بالكامل دون إنترنت
- **بيانات تجريبية**: جميع البيانات المنشأة للاختبار فقط

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **لا يتم حفظ المستخدمين الجدد**
   - تحقق من دعم localStorage في المتصفح
   - امسح البيانات وأعد المحاولة

2. **فشل تسجيل الدخول**
   - تأكد من صحة اسم المستخدم وكلمة المرور
   - تحقق من نوع المستخدم المحدد

3. **رسائل خطأ "رقم نور مكرر"**
   - كل طالب يجب أن يكون له رقم نور فريد
   - استخدم أرقام مختلفة لكل طالب

4. **مشاكل في التوجيه**
   - تحقق من وجود الصفحات المطلوبة
   - تأكد من صحة مسارات الملفات

## 📞 الدعم

إذا واجهت أي مشاكل:
1. افتح أدوات المطور في المتصفح (F12)
2. تحقق من رسائل الخطأ في وحدة التحكم
3. استخدم أدوات التشخيص المدمجة في الاختبارات
4. جرب مسح البيانات وإعادة إنشائها

## 🔄 التحديثات

- **الإصدار الحالي**: 2.0
- **آخر تحديث**: ديسمبر 2024
- **الميزات الجديدة**:
  - دعم رقم الإقامة لأولياء الأمور
  - تحسين التحقق من تفرد البيانات
  - واجهة محسنة للاختبارات
  - أدوات تشخيص متقدمة

---

**ملاحظة**: جميع الاختبارات آمنة ولا تؤثر على البيانات الحقيقية للنظام.
