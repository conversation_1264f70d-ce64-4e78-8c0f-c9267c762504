/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * نظام الإشعارات في الوقت الحقيقي
 */

// نظام الإشعارات
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.listeners = [];
        this.initialized = false;
        this.notificationCount = 0;
        this.notificationBadge = null;
        this.notificationContainer = null;
        this.notificationSound = null;
    }

    // تهيئة نظام الإشعارات
    init() {
        if (this.initialized) return;

        // إنشاء عناصر الإشعارات
        this.createNotificationElements();

        // تحميل الإشعارات من قاعدة البيانات
        this.loadNotifications();

        // بدء التحقق من الإشعارات الجديدة
        this.startNotificationCheck();

        this.initialized = true;
    }

    // إنشاء عناصر الإشعارات
    createNotificationElements() {
        // إنشاء زر الإشعارات
        const header = document.querySelector('.header .container');
        if (!header) return;

        // التحقق من وجود عنصر user-menu
        let userMenu = header.querySelector('.user-menu');
        if (!userMenu) {
            userMenu = document.createElement('div');
            userMenu.className = 'user-menu';
            header.appendChild(userMenu);
        }

        // إنشاء زر الإشعارات إذا لم يكن موجودًا
        if (!userMenu.querySelector('.notification-btn')) {
            const notificationBtn = document.createElement('div');
            notificationBtn.className = 'notification-btn';
            notificationBtn.innerHTML = `
                <i class="fas fa-bell"></i>
                <span class="notification-badge">0</span>
            `;
            userMenu.appendChild(notificationBtn);

            // إضافة حدث النقر على زر الإشعارات
            notificationBtn.addEventListener('click', () => {
                this.toggleNotificationContainer();
            });

            // تخزين عنصر شارة الإشعارات
            this.notificationBadge = notificationBtn.querySelector('.notification-badge');
        }

        // إنشاء حاوية الإشعارات
        if (!document.querySelector('.notification-container')) {
            const notificationContainer = document.createElement('div');
            notificationContainer.className = 'notification-container';
            notificationContainer.innerHTML = `
                <div class="notification-header">
                    <h3>الإشعارات</h3>
                    <button class="mark-all-read-btn"><i class="fas fa-check-double"></i> تعيين الكل كمقروء</button>
                </div>
                <div class="notification-list"></div>
                <div class="notification-footer">
                    <a href="notifications.html">عرض جميع الإشعارات</a>
                </div>
            `;
            document.body.appendChild(notificationContainer);

            // تخزين حاوية الإشعارات
            this.notificationContainer = notificationContainer;

            // إضافة حدث النقر على زر تعيين الكل كمقروء
            const markAllReadBtn = notificationContainer.querySelector('.mark-all-read-btn');
            markAllReadBtn.addEventListener('click', () => {
                this.markAllAsRead();
            });

            // إضافة حدث النقر خارج حاوية الإشعارات لإغلاقها
            document.addEventListener('click', (event) => {
                if (!event.target.closest('.notification-container') &&
                    !event.target.closest('.notification-btn') &&
                    this.notificationContainer.classList.contains('active')) {
                    this.notificationContainer.classList.remove('active');
                }
            });
        }

        // إنشاء صوت الإشعارات
        if (!document.querySelector('#notification-sound')) {
            const notificationSound = document.createElement('audio');
            notificationSound.id = 'notification-sound';
            // استخدام مسار مطلق للصوت
            const basePath = window.location.pathname.includes('/pages/') ? '../../' : './';
            notificationSound.src = basePath + 'assets/sounds/notification.mp3';
            document.body.appendChild(notificationSound);

            this.notificationSound = notificationSound;
        }

        // إضافة الأنماط CSS
        this.addNotificationStyles();
    }

    // إضافة الأنماط CSS للإشعارات
    addNotificationStyles() {
        if (document.querySelector('#notification-styles')) return;

        const styleElement = document.createElement('style');
        styleElement.id = 'notification-styles';
        styleElement.textContent = `
            .notification-btn {
                position: relative;
                cursor: pointer;
                margin-right: 20px;
                font-size: 1.2rem;
                color: var(--primary-color);
            }

            .notification-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background-color: var(--secondary-color);
                color: white;
                font-size: 0.7rem;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }

            .notification-badge.hidden {
                display: none;
            }

            .notification-container {
                position: absolute;
                top: 70px;
                left: 20px;
                width: 350px;
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                display: none;
                overflow: hidden;
                transition: all 0.3s ease;
                max-height: 80vh;
                flex-direction: column;
            }

            .notification-container.active {
                display: flex;
            }

            .notification-header {
                padding: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #eee;
            }

            .notification-header h3 {
                margin: 0;
                font-size: 1.1rem;
                color: var(--primary-color);
            }

            .mark-all-read-btn {
                background: none;
                border: none;
                color: var(--primary-color);
                cursor: pointer;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
            }

            .mark-all-read-btn i {
                margin-left: 5px;
            }

            .notification-list {
                overflow-y: auto;
                max-height: 350px;
                flex: 1;
            }

            .notification-item {
                padding: 15px;
                border-bottom: 1px solid #eee;
                display: flex;
                align-items: flex-start;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .notification-item:hover {
                background-color: #f9f9f9;
            }

            .notification-item.unread {
                background-color: rgba(46, 125, 50, 0.05);
            }

            .notification-icon {
                width: 40px;
                height: 40px;
                background-color: rgba(46, 125, 50, 0.1);
                color: var(--primary-color);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 15px;
                font-size: 1.2rem;
            }

            .notification-content {
                flex: 1;
            }

            .notification-title {
                font-weight: 500;
                margin-bottom: 5px;
                color: var(--primary-color);
            }

            .notification-message {
                font-size: 0.9rem;
                color: #666;
                margin-bottom: 5px;
            }

            .notification-date {
                font-size: 0.8rem;
                color: #999;
            }

            .notification-footer {
                padding: 15px;
                text-align: center;
                border-top: 1px solid #eee;
            }

            .notification-footer a {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
            }

            .notification-empty {
                padding: 30px;
                text-align: center;
                color: #999;
            }

            .notification-empty i {
                font-size: 3rem;
                margin-bottom: 10px;
                color: #ddd;
            }

            @media (max-width: 768px) {
                .notification-container {
                    width: calc(100% - 40px);
                    left: 20px;
                    right: 20px;
                }
            }

            /* Animation */
            @keyframes notification-pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }

            .notification-badge.pulse {
                animation: notification-pulse 1s infinite;
            }
        `;
        document.head.appendChild(styleElement);
    }

    // تحميل الإشعارات من قاعدة البيانات
    loadNotifications() {
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
        if (!currentUser) return;

        const db = getDatabase();
        this.notifications = db.notifications.filter(notification => notification.userId === currentUser.id);

        // تحديث عدد الإشعارات غير المقروءة
        this.updateNotificationCount();

        // عرض الإشعارات
        this.renderNotifications();
    }

    // تحديث عدد الإشعارات غير المقروءة
    updateNotificationCount() {
        const unreadCount = this.notifications.filter(notification => !notification.isRead).length;
        this.notificationCount = unreadCount;

        if (this.notificationBadge) {
            this.notificationBadge.textContent = unreadCount;

            if (unreadCount === 0) {
                this.notificationBadge.classList.add('hidden');
            } else {
                this.notificationBadge.classList.remove('hidden');
            }
        }
    }

    // عرض الإشعارات
    renderNotifications() {
        if (!this.notificationContainer) return;

        const notificationList = this.notificationContainer.querySelector('.notification-list');
        if (!notificationList) return;

        // ترتيب الإشعارات بحسب التاريخ (الأحدث أولاً)
        const sortedNotifications = [...this.notifications].sort((a, b) => {
            return new Date(b.date) - new Date(a.date);
        });

        // عرض أحدث 5 إشعارات فقط
        const recentNotifications = sortedNotifications.slice(0, 5);

        // تفريغ قائمة الإشعارات
        notificationList.innerHTML = '';

        // إذا لم توجد إشعارات
        if (recentNotifications.length === 0) {
            notificationList.innerHTML = `
                <div class="notification-empty">
                    <i class="fas fa-bell-slash"></i>
                    <p>لا توجد إشعارات</p>
                </div>
            `;
            return;
        }

        // إضافة الإشعارات إلى القائمة
        recentNotifications.forEach(notification => {
            const notificationItem = document.createElement('div');
            notificationItem.className = 'notification-item';
            if (!notification.isRead) {
                notificationItem.classList.add('unread');
            }

            notificationItem.innerHTML = `
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-date">${notification.date}</div>
                </div>
            `;

            // إضافة حدث النقر على الإشعار
            notificationItem.addEventListener('click', () => {
                this.markAsRead(notification.id);

                // توجيه المستخدم إلى صفحة الإشعارات
                window.location.href = 'notifications.html';
            });

            notificationList.appendChild(notificationItem);
        });
    }

    // تبديل عرض حاوية الإشعارات
    toggleNotificationContainer() {
        if (!this.notificationContainer) return;

        this.notificationContainer.classList.toggle('active');
    }

    // تعيين إشعار كمقروء
    markAsRead(notificationId) {
        const db = getDatabase();
        const notificationIndex = db.notifications.findIndex(n => n.id === notificationId);

        if (notificationIndex !== -1) {
            db.notifications[notificationIndex].isRead = true;
            saveDatabase(db);

            // تحديث الإشعارات المحلية
            const localIndex = this.notifications.findIndex(n => n.id === notificationId);
            if (localIndex !== -1) {
                this.notifications[localIndex].isRead = true;
            }

            // تحديث عدد الإشعارات وإعادة عرضها
            this.updateNotificationCount();
            this.renderNotifications();

            // إطلاق حدث تحديث الإشعارات
            this.notifyListeners('update');
        }
    }

    // تعيين جميع الإشعارات كمقروءة
    markAllAsRead() {
        const db = getDatabase();
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));

        if (!currentUser) return;

        // تحديث الإشعارات في قاعدة البيانات
        db.notifications.forEach(notification => {
            if (notification.userId === currentUser.id) {
                notification.isRead = true;
            }
        });

        saveDatabase(db);

        // تحديث الإشعارات المحلية
        this.notifications.forEach(notification => {
            notification.isRead = true;
        });

        // تحديث عدد الإشعارات وإعادة عرضها
        this.updateNotificationCount();
        this.renderNotifications();

        // إطلاق حدث تحديث الإشعارات
        this.notifyListeners('update');
    }

    // إضافة إشعار جديد
    addNotification(userId, title, message, metadata = null) {
        const db = getDatabase();

        // إنشاء إشعار جديد
        const newNotification = {
            id: db.notifications.length + 1,
            userId: userId,
            title: title,
            message: message,
            isRead: false,
            date: new Date().toISOString().split('T')[0],
            metadata: metadata // بيانات إضافية مثل معلومات الطلب أو المشتريات
        };

        // إضافة الإشعار إلى قاعدة البيانات
        db.notifications.push(newNotification);
        saveDatabase(db);

        // إذا كان الإشعار للمستخدم الحالي
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
        if (currentUser && currentUser.id === userId) {
            // إضافة الإشعار إلى الإشعارات المحلية
            this.notifications.push(newNotification);

            // تحديث عدد الإشعارات وإعادة عرضها
            this.updateNotificationCount();
            this.renderNotifications();

            // تشغيل صوت الإشعار
            this.playNotificationSound();

            // تنشيط شارة الإشعارات
            this.pulseNotificationBadge();

            // إطلاق حدث إضافة إشعار
            this.notifyListeners('add');
        }
    }

    // إضافة إشعار للطالب وولي الأمر معًا
    addStudentAndParentNotification(studentId, studentTitle, studentMessage, parentTitle, parentMessage, metadata = null) {
        const db = getDatabase();

        // البحث عن الطالب
        const student = db.users.find(user => user.id === studentId && user.role === 'student');
        if (!student) {
            console.error('لم يتم العثور على الطالب برقم المعرف:', studentId);
            return;
        }

        // إضافة إشعار للطالب
        this.addNotification(studentId, studentTitle, studentMessage, metadata);

        // التحقق من وجود ولي أمر مرتبط بالطالب
        if (student.parentId) {
            // التحقق من إعدادات إشعارات الطالب لولي الأمر
            let shouldNotifyParent = true;

            // التحقق من إعدادات الإشعارات إذا كانت موجودة
            if (student.notificationSettings &&
                student.notificationSettings.parent &&
                metadata && metadata.type) {

                const parentSettings = student.notificationSettings.parent;

                // التحقق من نوع الإشعار وإعداداته
                switch (metadata.type) {
                    case 'purchase':
                        shouldNotifyParent = parentSettings.purchaseNotifications !== false;
                        break;
                    case 'balance':
                        shouldNotifyParent = parentSettings.balanceNotifications !== false;
                        break;
                    case 'login':
                        shouldNotifyParent = parentSettings.loginNotifications === true;
                        break;
                    // يمكن إضافة أنواع أخرى من الإشعارات هنا
                }
            }

            // إرسال الإشعار لولي الأمر إذا كانت الإعدادات تسمح بذلك
            if (shouldNotifyParent) {
                this.addNotification(student.parentId, parentTitle, parentMessage, metadata);
            }
        }
    }

    // تشغيل صوت الإشعار
    playNotificationSound() {
        if (this.notificationSound) {
            this.notificationSound.currentTime = 0;
            this.notificationSound.play().catch(error => {
                console.log('Error playing notification sound:', error);
            });
        }
    }

    // تنشيط شارة الإشعارات
    pulseNotificationBadge() {
        if (this.notificationBadge) {
            this.notificationBadge.classList.add('pulse');

            setTimeout(() => {
                this.notificationBadge.classList.remove('pulse');
            }, 3000);
        }
    }

    // بدء التحقق من الإشعارات الجديدة
    startNotificationCheck() {
        // في تطبيق حقيقي، هنا يمكن استخدام WebSockets أو Server-Sent Events
        // للتحقق من الإشعارات الجديدة في الوقت الحقيقي

        // للتجربة، سنقوم بالتحقق كل 30 ثانية
        setInterval(() => {
            this.loadNotifications();
        }, 30000);
    }

    // إضافة مستمع لأحداث الإشعارات
    addListener(callback) {
        this.listeners.push(callback);
    }

    // إزالة مستمع لأحداث الإشعارات
    removeListener(callback) {
        this.listeners = this.listeners.filter(listener => listener !== callback);
    }

    // إخطار المستمعين بحدث
    notifyListeners(event) {
        this.listeners.forEach(listener => {
            listener(event, this.notifications);
        });
    }
}

// إنشاء كائن نظام الإشعارات
const notificationSystem = new NotificationSystem();

// تهيئة نظام الإشعارات بطريقة آمنة
function safeInitNotifications() {
    try {
        // التحقق من وجود مستخدم مسجل الدخول
        let currentUser = null;
        try {
            const userJson = sessionStorage.getItem('currentUser');
            if (userJson) {
                currentUser = JSON.parse(userJson);
            }
        } catch (e) {
            console.warn('Error parsing user data:', e);
            return; // لا تقم بتهيئة النظام في حالة وجود خطأ
        }

        // تحقق من المسار الحالي بطريقة أكثر دقة
        const path = window.location.pathname.toLowerCase();
        const isAuthPage = path.includes('/auth/') ||
                          path.includes('/login.html') ||
                          path.includes('/register.html') ||
                          path.endsWith('/login') ||
                          path.endsWith('/register');

        // تهيئة نظام الإشعارات فقط إذا كان المستخدم مسجل الدخول وليس في صفحة تسجيل الدخول
        if (currentUser && !isAuthPage) {
            console.log('Initializing notification system');
            notificationSystem.init();
        } else {
            console.log('Skipping notification system initialization for auth page or no user');
        }
    } catch (error) {
        console.error('Error in notification system initialization:', error);
    }
}

// تأجيل تهيئة نظام الإشعارات حتى يتم تحميل الصفحة بالكامل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        // تأخير التهيئة لضمان تحميل جميع الموارد
        setTimeout(safeInitNotifications, 2000);
    });
} else {
    // الصفحة محملة بالفعل
    setTimeout(safeInitNotifications, 2000);
}

// تصدير كائن نظام الإشعارات
window.notificationSystem = notificationSystem;
