<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدرسة - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.5rem;
        }

        .stat-icon.students {
            background-color: rgba(249, 168, 37, 0.1);
            color: var(--secondary-color);
        }

        .stat-icon.products {
            background-color: rgba(33, 150, 243, 0.1);
            color: #2196f3;
        }

        .stat-icon.orders {
            background-color: rgba(233, 30, 99, 0.1);
            color: #e91e63;
        }

        .stat-icon.revenue {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .stat-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .edit-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 150px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image i {
            font-size: 3rem;
            color: #ccc;
        }

        .product-info {
            padding: 15px;
        }

        .product-info h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .product-info .price {
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        .product-info .category {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 10px;
        }

        .product-actions {
            display: flex;
            gap: 5px;
        }

        .add-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .add-btn i {
            margin-left: 5px;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="../../index.html">
                    <img src="../../assets/images/logo-enhanced.svg" alt="شعار فواصل النجاح" class="logo-img">
                    <h1>فواصل النجاح للخدمات الإعاشة</h1>
                </a>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير المدرسة</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير المدرسة</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="school-dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="school-students.html"><i class="fas fa-users"></i> إدارة الطلاب</a></li>
                <li><a href="school-products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="school-orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="school-reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="school-settings.html"><i class="fas fa-cog"></i> إعدادات المدرسة</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>لوحة تحكم المدرسة</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon students">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="students-count">0</h3>
                        <p>الطلاب</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="products-count">0</h3>
                        <p>المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="orders-count">0</h3>
                        <p>الطلبات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon revenue">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="revenue">0 ريال</h3>
                        <p>الإيرادات</p>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <h2>الطلاب المسجلين</h2>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الطالب</th>
                                <th>رقم نور</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الصف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="students-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="content-card">
                <h2>منتجات المقصف</h2>
                <button class="add-btn" id="add-product-btn"><i class="fas fa-plus"></i> إضافة منتج جديد</button>
                <div class="products-grid" id="products-grid">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>

            <div class="content-card">
                <h2>آخر الطلبات</h2>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الطالب</th>
                                <th>المنتجات</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة للتحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم من sessionStorage
                let currentUser = null;
                try {
                    const userDataString = sessionStorage.getItem('currentUser');
                    if (userDataString) {
                        currentUser = JSON.parse(userDataString);
                    }
                } catch (sessionError) {
                    console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
                }

                // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
                if (!currentUser || !currentUser.role) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'school' && userId) {
                        // إنشاء كائن مستخدم بسيط
                        currentUser = {
                            id: parseInt(userId),
                            role: userRole,
                            name: 'مدير المدرسة',
                            schoolId: 1
                        };

                        // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                        try {
                            const db = getDatabase();
                            const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                            if (fullUserData) {
                                currentUser = fullUserData;
                                // تخزين البيانات الكاملة في sessionStorage
                                sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                            }
                        } catch (dbError) {
                            console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                        }
                    }
                }

                // التحقق من صلاحيات المستخدم
                if (!currentUser || currentUser.role !== 'school') {
                    alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                    window.location.href = '../auth/login.html';
                    return null;
                }

                return currentUser;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
                window.location.href = '../auth/login.html';
                return null;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صلاحيات المستخدم
            const currentUser = checkUserAuthorization();
            if (!currentUser) {
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Get school ID
            const schoolId = currentUser.schoolId;

            // Get students for this school
            const students = db.users.filter(user => user.role === 'student' && user.schoolId === schoolId);

            // Get orders for this school
            const orders = db.orders.filter(order => order.schoolId === schoolId);

            // Calculate revenue
            const revenue = orders.reduce((total, order) => total + order.totalPrice, 0);

            // Update stats
            document.getElementById('students-count').textContent = students.length;
            document.getElementById('products-count').textContent = db.products.length;
            document.getElementById('orders-count').textContent = orders.length;
            document.getElementById('revenue').textContent = revenue + ' ريال';

            // Populate students table
            const studentsTable = document.getElementById('students-table');
            students.forEach(student => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${student.id}</td>
                    <td>${student.name}</td>
                    <td>${student.noorId || 'غير متوفر'}</td>
                    <td>${student.email || 'غير متوفر'}</td>
                    <td>${student.phone || 'غير متوفر'}</td>
                    <td>${getGradeText(student.grade)}</td>
                    <td>
                        <button class="action-btn view-btn">عرض</button>
                        <button class="action-btn edit-btn">تعديل</button>
                        <button class="action-btn delete-btn">حذف</button>
                    </td>
                `;
                studentsTable.appendChild(row);
            });

            // Populate products grid
            const productsGrid = document.getElementById('products-grid');
            db.products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <div class="product-image">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <div class="price">${product.price} ريال</div>
                        <div class="category">${product.category}</div>
                        <div class="product-actions">
                            <button class="action-btn edit-btn">تعديل</button>
                            <button class="action-btn delete-btn">حذف</button>
                        </div>
                    </div>
                `;
                productsGrid.appendChild(productCard);
            });

            // Populate orders table
            const ordersTable = document.getElementById('orders-table');
            orders.forEach(order => {
                const user = db.users.find(u => u.id === order.userId);

                // Get product names
                const productNames = order.products.map(p => {
                    const product = db.products.find(prod => prod.id === p.productId);
                    return `${product ? product.name : 'منتج غير معروف'} (${p.quantity})`;
                }).join(', ');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.id}</td>
                    <td>${user ? user.name : 'غير معروف'}</td>
                    <td>${productNames}</td>
                    <td>${order.totalPrice} ريال</td>
                    <td>${order.date}</td>
                    <td>${getOrderStatusText(order.status)}</td>
                    <td>
                        <button class="action-btn view-btn">عرض</button>
                        <button class="action-btn edit-btn">تعديل</button>
                    </td>
                `;
                ordersTable.appendChild(row);
            });

            // Add Product Button
            document.getElementById('add-product-btn').addEventListener('click', function() {
                window.location.href = 'school-add-product.html';
            });

            // Logout button
            document.getElementById('logout-btn').addEventListener('click', function() {
                try {
                    // حذف بيانات المستخدم من جميع وسائل التخزين
                    sessionStorage.clear();
                    localStorage.removeItem('currentUserRole');
                    localStorage.removeItem('currentUserId');

                    // حذف الكوكيز المتعلقة بالمستخدم
                    document.cookie = 'currentUserRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'currentUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                    console.log('تم تسجيل الخروج بنجاح');

                    // التوجيه إلى صفحة تسجيل الدخول
                    window.location.href = '../auth/login.html';
                } catch (error) {
                    console.error('خطأ في تسجيل الخروج:', error);
                    alert('حدث خطأ في تسجيل الخروج. سيتم إعادة تحميل الصفحة.');
                    window.location.reload();
                }
            });
        });

        // Helper function to get grade text
        function getGradeText(grade) {
            switch(grade) {
                case '1':
                    return 'الصف الأول';
                case '2':
                    return 'الصف الثاني';
                case '3':
                    return 'الصف الثالث';
                case '4':
                    return 'الصف الرابع';
                case '5':
                    return 'الصف الخامس';
                case '6':
                    return 'الصف السادس';
                default:
                    return 'غير معروف';
            }
        }

        // Helper function to get order status text
        function getOrderStatusText(status) {
            switch(status) {
                case 'pending':
                    return 'قيد الانتظار';
                case 'processing':
                    return 'قيد التنفيذ';
                case 'completed':
                    return 'مكتمل';
                case 'cancelled':
                    return 'ملغي';
                default:
                    return status;
            }
        }
    </script>
</body>
</html>
