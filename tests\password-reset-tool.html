<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة إعادة تعيين كلمات المرور - نظام المقصف المدرسي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #f44336;
            margin-bottom: 10px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .alert.danger {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .alert.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-btn {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }
        .test-btn.success {
            background: #4caf50;
        }
        .test-btn.success:hover {
            background: #388e3c;
        }
        .test-btn.info {
            background: #2196f3;
        }
        .test-btn.info:hover {
            background: #1976d2;
        }
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #f44336;
        }
        .user-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .user-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .user-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .user-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .password-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .password-status.encrypted {
            background: #ffebee;
            color: #c62828;
        }
        .password-status.plain {
            background: #e8f5e9;
            color: #2e7d32;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            background: #1b5e20;
            transform: translateY(-2px);
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 أداة إعادة تعيين كلمات المرور</h1>
            <p>إصلاح مشاكل المصادقة وإعادة كلمات المرور إلى حالتها الأصلية</p>
        </div>

        <div class="nav-links">
            <a href="index.html">🏠 مركز الاختبارات</a>
            <a href="../pages/auth/login.html">🔑 دخول المدير</a>
            <a href="../pages/staff/login.html">👨‍💼 دخول العاملين</a>
            <a href="staff-login-flow-test.html">🧪 اختبار تدفق الدخول</a>
        </div>

        <div class="alert danger">
            <strong>تحذير:</strong> هذه الأداة ستقوم بإعادة تعيين جميع كلمات المرور إلى حالتها الأصلية البسيطة (غير مشفرة).
        </div>

        <div class="section">
            <h2>تشخيص المشكلة</h2>
            <button class="test-btn info" onclick="diagnoseProblem()">فحص حالة كلمات المرور</button>
            <button class="test-btn info" onclick="showAllUsers()">عرض جميع المستخدمين</button>
            <div id="diagnosis-results" class="results" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>إصلاح المشكلة</h2>
            <button class="test-btn" onclick="resetAllPasswords()">إعادة تعيين جميع كلمات المرور</button>
            <button class="test-btn" onclick="disablePasswordHashing()">تعطيل تشفير كلمات المرور</button>
            <button class="test-btn success" onclick="restoreOriginalDatabase()">استعادة قاعدة البيانات الأصلية</button>
            <div id="fix-results" class="results" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>اختبار تسجيل الدخول</h2>
            <button class="test-btn info" onclick="testAdminLogin()">اختبار دخول المدير</button>
            <button class="test-btn info" onclick="testStaffLogin()">اختبار دخول العاملين</button>
            <button class="test-btn info" onclick="testAllLogins()">اختبار جميع الحسابات</button>
            <div id="test-results" class="results" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>كلمات المرور الأصلية</h2>
            <div class="user-list" id="original-passwords">
                <div class="user-card">
                    <h4>👑 المدير</h4>
                    <div class="user-info">اسم المستخدم: admin</div>
                    <div class="user-info">كلمة المرور: admin123</div>
                </div>
                <div class="user-card">
                    <h4>💰 الكاشير</h4>
                    <div class="user-info">اسم المستخدم: cashier1</div>
                    <div class="user-info">كلمة المرور: cashier123</div>
                </div>
                <div class="user-card">
                    <h4>👨‍🍳 الطباخ</h4>
                    <div class="user-info">اسم المستخدم: cook1</div>
                    <div class="user-info">كلمة المرور: cook123</div>
                </div>
                <div class="user-card">
                    <h4>👨‍💼 المدير</h4>
                    <div class="user-info">اسم المستخدم: manager1</div>
                    <div class="user-info">كلمة المرور: manager123</div>
                </div>
            </div>
        </div>

        <div id="user-list-section" class="section" style="display: none;">
            <h2>قائمة المستخدمين الحالية</h2>
            <div id="current-users" class="user-list"></div>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        // كلمات المرور الأصلية
        const originalPasswords = {
            'admin': 'admin123',
            'school1': 'school123',
            'student1': 'student123',
            'parent1': 'parent123',
            'cashier1': 'cashier123',
            'cook1': 'cook123',
            'manager1': 'manager123'
        };

        function diagnoseProblem() {
            const resultsDiv = document.getElementById('diagnosis-results');
            resultsDiv.style.display = 'block';
            
            let results = '<h3>نتائج التشخيص:</h3>';
            
            try {
                const db = getDatabase();
                if (!db || !db.users) {
                    results += '<div class="alert danger">❌ قاعدة البيانات غير متوفرة</div>';
                    resultsDiv.innerHTML = results;
                    return;
                }

                results += `<div class="alert">📊 عدد المستخدمين: ${db.users.length}</div>`;
                
                let encryptedCount = 0;
                let plainCount = 0;
                
                db.users.forEach(user => {
                    if (user.password && user.password.length > 20) {
                        encryptedCount++;
                    } else {
                        plainCount++;
                    }
                });
                
                results += `<div class="alert">🔐 كلمات مرور مشفرة: ${encryptedCount}</div>`;
                results += `<div class="alert">📝 كلمات مرور بسيطة: ${plainCount}</div>`;
                
                if (encryptedCount > 0) {
                    results += '<div class="alert danger">⚠️ المشكلة: توجد كلمات مرور مشفرة تحتاج إلى إعادة تعيين</div>';
                } else {
                    results += '<div class="alert success">✅ جميع كلمات المرور بسيطة</div>';
                }
                
            } catch (error) {
                results += `<div class="alert danger">❌ خطأ في التشخيص: ${error.message}</div>`;
            }
            
            resultsDiv.innerHTML = results;
        }

        function showAllUsers() {
            const userListSection = document.getElementById('user-list-section');
            const currentUsersDiv = document.getElementById('current-users');
            
            try {
                const db = getDatabase();
                if (!db || !db.users) {
                    currentUsersDiv.innerHTML = '<div class="alert danger">قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let usersHtml = '';
                db.users.forEach(user => {
                    const isEncrypted = user.password && user.password.length > 20;
                    const statusClass = isEncrypted ? 'encrypted' : 'plain';
                    const statusText = isEncrypted ? 'مشفرة' : 'بسيطة';
                    
                    usersHtml += `
                        <div class="user-card">
                            <h4>${user.name || user.username}</h4>
                            <div class="user-info">اسم المستخدم: ${user.username}</div>
                            <div class="user-info">الدور: ${user.role}</div>
                            <div class="user-info">كلمة المرور: ${user.password.substring(0, 20)}${user.password.length > 20 ? '...' : ''}</div>
                            <div class="password-status ${statusClass}">حالة كلمة المرور: ${statusText}</div>
                        </div>
                    `;
                });
                
                currentUsersDiv.innerHTML = usersHtml;
                userListSection.style.display = 'block';
                
            } catch (error) {
                currentUsersDiv.innerHTML = `<div class="alert danger">خطأ في عرض المستخدمين: ${error.message}</div>`;
            }
        }

        function resetAllPasswords() {
            const resultsDiv = document.getElementById('fix-results');
            resultsDiv.style.display = 'block';
            
            try {
                const db = getDatabase();
                if (!db || !db.users) {
                    resultsDiv.innerHTML = '<div class="alert danger">قاعدة البيانات غير متوفرة</div>';
                    return;
                }

                let resetCount = 0;
                let results = '<h3>نتائج إعادة التعيين:</h3>';
                
                db.users.forEach(user => {
                    if (originalPasswords[user.username]) {
                        const oldPassword = user.password;
                        user.password = originalPasswords[user.username];
                        resetCount++;
                        results += `<div class="alert success">✅ تم إعادة تعيين كلمة مرور ${user.username}</div>`;
                    }
                });
                
                // حفظ قاعدة البيانات
                saveDatabase(db);
                
                results += `<div class="alert success">🎉 تم إعادة تعيين ${resetCount} كلمة مرور بنجاح!</div>`;
                results += '<div class="alert">💡 يمكنك الآن تسجيل الدخول باستخدام كلمات المرور الأصلية</div>';
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert danger">خطأ في إعادة التعيين: ${error.message}</div>`;
            }
        }

        function disablePasswordHashing() {
            const resultsDiv = document.getElementById('fix-results');
            resultsDiv.style.display = 'block';
            
            try {
                // تعطيل تشفير كلمات المرور
                localStorage.setItem('disablePasswordHashing', 'true');
                
                // إيقاف النظام التلقائي لتشفير كلمات المرور
                if (window.PasswordUtils) {
                    window.PasswordUtils.disabled = true;
                }
                
                resultsDiv.innerHTML = `
                    <h3>تم تعطيل تشفير كلمات المرور:</h3>
                    <div class="alert success">✅ تم تعطيل نظام تشفير كلمات المرور</div>
                    <div class="alert">💡 سيتم الآن استخدام كلمات المرور البسيطة فقط</div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert danger">خطأ في تعطيل التشفير: ${error.message}</div>`;
            }
        }

        function restoreOriginalDatabase() {
            const resultsDiv = document.getElementById('fix-results');
            resultsDiv.style.display = 'block';
            
            if (!confirm('هل أنت متأكد من استعادة قاعدة البيانات الأصلية؟ سيتم حذف جميع البيانات الحالية!')) {
                return;
            }
            
            try {
                // حذف قاعدة البيانات الحالية
                localStorage.removeItem('cafeteriaDB');
                
                // تعطيل تشفير كلمات المرور
                localStorage.setItem('disablePasswordHashing', 'true');
                
                // إعادة تحميل الصفحة لاستعادة قاعدة البيانات الأصلية
                resultsDiv.innerHTML = `
                    <h3>استعادة قاعدة البيانات الأصلية:</h3>
                    <div class="alert success">✅ تم حذف قاعدة البيانات الحالية</div>
                    <div class="alert success">✅ تم تعطيل تشفير كلمات المرور</div>
                    <div class="alert">🔄 سيتم إعادة تحميل الصفحة لاستعادة البيانات الأصلية...</div>
                `;
                
                setTimeout(() => {
                    location.reload();
                }, 2000);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert danger">خطأ في الاستعادة: ${error.message}</div>`;
            }
        }

        function testLogin(username, password, loginType = 'admin') {
            return new Promise((resolve) => {
                try {
                    const db = getDatabase();
                    if (!db || !db.users) {
                        resolve({ success: false, error: 'قاعدة البيانات غير متوفرة' });
                        return;
                    }

                    const user = db.users.find(u => u.username === username);
                    if (!user) {
                        resolve({ success: false, error: 'المستخدم غير موجود' });
                        return;
                    }

                    if (user.password === password) {
                        resolve({ success: true, user: user });
                    } else {
                        resolve({ success: false, error: 'كلمة المرور غير صحيحة' });
                    }
                    
                } catch (error) {
                    resolve({ success: false, error: error.message });
                }
            });
        }

        async function testAdminLogin() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            
            const result = await testLogin('admin', 'admin123', 'admin');
            
            if (result.success) {
                resultsDiv.innerHTML = `
                    <h3>اختبار دخول المدير:</h3>
                    <div class="alert success">✅ تم تسجيل دخول المدير بنجاح!</div>
                    <div class="alert">👤 المستخدم: ${result.user.name}</div>
                    <div class="alert">🔑 الدور: ${result.user.role}</div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <h3>اختبار دخول المدير:</h3>
                    <div class="alert danger">❌ فشل تسجيل دخول المدير: ${result.error}</div>
                `;
            }
        }

        async function testStaffLogin() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            
            const staffAccounts = [
                { username: 'cashier1', password: 'cashier123', name: 'الكاشير' },
                { username: 'cook1', password: 'cook123', name: 'الطباخ' },
                { username: 'manager1', password: 'manager123', name: 'المدير' }
            ];
            
            let results = '<h3>اختبار دخول العاملين:</h3>';
            
            for (const account of staffAccounts) {
                const result = await testLogin(account.username, account.password, 'staff');
                
                if (result.success) {
                    results += `<div class="alert success">✅ ${account.name} (${account.username}): نجح</div>`;
                } else {
                    results += `<div class="alert danger">❌ ${account.name} (${account.username}): ${result.error}</div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }

        async function testAllLogins() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            
            let results = '<h3>اختبار جميع الحسابات:</h3>';
            
            // اختبار المدير
            const adminResult = await testLogin('admin', 'admin123');
            results += adminResult.success ? 
                '<div class="alert success">✅ المدير: نجح</div>' : 
                `<div class="alert danger">❌ المدير: ${adminResult.error}</div>`;
            
            // اختبار العاملين
            const staffAccounts = [
                { username: 'cashier1', password: 'cashier123', name: 'الكاشير' },
                { username: 'cook1', password: 'cook123', name: 'الطباخ' },
                { username: 'manager1', password: 'manager123', name: 'مدير المقصف' }
            ];
            
            for (const account of staffAccounts) {
                const result = await testLogin(account.username, account.password);
                results += result.success ? 
                    `<div class="alert success">✅ ${account.name}: نجح</div>` : 
                    `<div class="alert danger">❌ ${account.name}: ${result.error}</div>`;
            }
            
            // اختبار حسابات أخرى
            const otherAccounts = [
                { username: 'school1', password: 'school123', name: 'المدرسة' },
                { username: 'student1', password: 'student123', name: 'الطالب' },
                { username: 'parent1', password: 'parent123', name: 'ولي الأمر' }
            ];
            
            for (const account of otherAccounts) {
                const result = await testLogin(account.username, account.password);
                results += result.success ? 
                    `<div class="alert success">✅ ${account.name}: نجح</div>` : 
                    `<div class="alert danger">❌ ${account.name}: ${result.error}</div>`;
            }
            
            resultsDiv.innerHTML = results;
        }

        // تشغيل التشخيص التلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                diagnoseProblem();
            }, 1000);
        });
    </script>
</body>
</html>
