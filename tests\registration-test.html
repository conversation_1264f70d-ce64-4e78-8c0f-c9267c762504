<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التسجيل</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2e7d32;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .btn {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin: 10px 0;
        }
        .btn:hover {
            background: #1b5e20;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn.secondary {
            background: #1976d2;
        }
        .btn.secondary:hover {
            background: #1565c0;
        }
        .btn.danger {
            background: #d32f2f;
        }
        .btn.danger:hover {
            background: #b71c1c;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .user-list {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام التسجيل</h1>
            <p>اختبار شامل لنظام إنشاء الحسابات الجديدة</p>
        </div>

        <div class="test-section">
            <div class="test-title">📝 إنشاء حساب تجريبي</div>
            <form id="testForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" value="test_user_123" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" value="test123" required>
                </div>

                <div class="form-group">
                    <label for="fullName">الاسم الكامل:</label>
                    <input type="text" id="fullName" name="fullName" value="مستخدم تجريبي" required>
                </div>

                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" name="email" value="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="userType">نوع المستخدم:</label>
                    <select id="userType" name="userType">
                        <option value="student">طالب</option>
                        <option value="staff">عامل</option>
                        <option value="parent">ولي أمر</option>
                        <option value="admin">مدير</option>
                        <option value="school">مدرسة</option>
                    </select>
                </div>

                <button type="submit" class="btn" id="submitBtn">إنشاء الحساب التجريبي</button>
            </form>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 أدوات الاختبار</div>
            <button class="btn secondary" onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <button class="btn secondary" onclick="generateRandomUser()">إنشاء مستخدم عشوائي</button>
            <button class="btn danger" onclick="clearDatabase()">مسح قاعدة البيانات</button>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 روابط سريعة</div>
            <button class="btn secondary" onclick="window.open('../pages/auth/register.html', '_blank')">فتح صفحة التسجيل</button>
            <button class="btn secondary" onclick="window.open('../pages/auth/login.html', '_blank')">فتح صفحة تسجيل الدخول</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('testForm');
            const resultDiv = document.getElementById('result');

            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = document.getElementById('submitBtn');
                const originalText = submitBtn.textContent;
                
                submitBtn.textContent = 'جاري الإنشاء...';
                submitBtn.disabled = true;

                // جمع البيانات
                const userData = {
                    username: document.getElementById('username').value.trim(),
                    password: document.getElementById('password').value.trim(),
                    fullName: document.getElementById('fullName').value.trim(),
                    email: document.getElementById('email').value.trim(),
                    userType: document.getElementById('userType').value,
                    // بيانات إضافية للاختبار
                    noorId: '123456789', // رقم نور تجريبي للطلاب
                    residenceId: '987654321' // رقم إقامة تجريبي لأولياء الأمور
                };

                console.log('بيانات المستخدم:', userData);

                try {
                    // التحقق من البيانات
                    if (!userData.username || !userData.password || !userData.fullName) {
                        throw new Error('يرجى ملء جميع الحقول المطلوبة');
                    }

                    // الحصول على قاعدة البيانات
                    let db;
                    try {
                        db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                    } catch {
                        db = {};
                    }

                    // التأكد من وجود مصفوفة المستخدمين
                    if (!db.users || !Array.isArray(db.users)) {
                        db.users = [];
                    }

                    // التحقق من عدم وجود اسم المستخدم
                    const existingUser = db.users.find(u => u.username === userData.username);
                    if (existingUser) {
                        throw new Error('اسم المستخدم موجود بالفعل');
                    }

                    // إنشاء المستخدم الجديد
                    const newId = db.users.length > 0 ? Math.max(...db.users.map(u => u.id || 0)) + 1 : 1;
                    const newUser = {
                        id: newId,
                        username: userData.username,
                        password: userData.password,
                        role: userData.userType,
                        name: userData.fullName,
                        email: userData.email,
                        status: 'active',
                        createdAt: new Date().toISOString()
                    };

                    // إضافة البيانات الخاصة حسب نوع المستخدم
                    if (userData.userType === 'student') {
                        newUser.noorId = userData.noorId;
                        newUser.grade = 'الصف الأول';
                        newUser.schoolId = 1;
                        newUser.balance = 0;
                        newUser.allergies = [];
                    } else if (userData.userType === 'parent') {
                        newUser.studentNoorId = userData.noorId; // استخدام نفس الرقم للاختبار
                        newUser.residenceId = userData.residenceId;
                    } else if (userData.userType === 'school') {
                        newUser.schoolName = userData.fullName;
                        newUser.address = 'عنوان تجريبي';
                    }

                    // حفظ المستخدم
                    db.users.push(newUser);
                    localStorage.setItem('cafeteriaDB', JSON.stringify(db));

                    // عرض النتيجة
                    let successMessage = `
                        ✅ تم إنشاء الحساب بنجاح!<br>
                        المعرف: ${newUser.id}<br>
                        اسم المستخدم: ${newUser.username}<br>
                        النوع: ${newUser.role}<br>
                        التاريخ: ${new Date(newUser.createdAt).toLocaleString('ar-SA')}
                    `;

                    // إضافة البيانات الخاصة حسب النوع
                    if (newUser.noorId) {
                        successMessage += `<br>رقم نور الطالب: ${newUser.noorId}`;
                    }
                    if (newUser.residenceId) {
                        successMessage += `<br>رقم الإقامة: ${newUser.residenceId}`;
                    }
                    if (newUser.studentNoorId) {
                        successMessage += `<br>رقم نور الطالب المرتبط: ${newUser.studentNoorId}`;
                    }
                    if (newUser.schoolName) {
                        successMessage += `<br>اسم المدرسة: ${newUser.schoolName}`;
                    }

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = successMessage;
                    resultDiv.style.display = 'block';

                    // إعادة تعيين النموذج
                    form.reset();
                    
                    // تحديث اسم المستخدم للاختبار التالي
                    document.getElementById('username').value = 'test_user_' + Date.now();

                } catch (error) {
                    console.error('خطأ:', error);
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ خطأ: ${error.message}`;
                    resultDiv.style.display = 'block';
                }

                // إعادة تفعيل الزر
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // فحص قاعدة البيانات
        function checkDatabase() {
            const resultDiv = document.getElementById('result');
            
            try {
                const db = JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
                
                if (!db.users || db.users.length === 0) {
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = 'ℹ️ قاعدة البيانات فارغة أو غير موجودة';
                } else {
                    const usersByRole = {};
                    db.users.forEach(user => {
                        if (!usersByRole[user.role]) {
                            usersByRole[user.role] = 0;
                        }
                        usersByRole[user.role]++;
                    });

                    const usersList = db.users.map((user, index) => 
                        `${index + 1}. ${user.username} (${user.role}) - ${user.name}${user.noorId ? ' - نور: ' + user.noorId : ''}${user.residenceId ? ' - إقامة: ' + user.residenceId : ''}`
                    ).join('<br>');
                    
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = `
                        📊 قاعدة البيانات تحتوي على ${db.users.length} مستخدم:<br>
                        📈 التوزيع: ${JSON.stringify(usersByRole, null, 2)}<br><br>
                        <div class="user-list">${usersList}</div>
                    `;
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ خطأ في قراءة قاعدة البيانات: ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        // إنشاء مستخدم عشوائي
        function generateRandomUser() {
            const userTypes = ['student', 'staff', 'parent', 'admin', 'school'];
            const randomType = userTypes[Math.floor(Math.random() * userTypes.length)];
            const randomId = Math.floor(Math.random() * 10000);
            
            document.getElementById('userType').value = randomType;
            document.getElementById('username').value = `test_${randomType}_${randomId}`;
            document.getElementById('password').value = 'test123';
            document.getElementById('fullName').value = `مستخدم ${randomType} ${randomId}`;
            document.getElementById('email').value = `test${randomId}@example.com`;
            
            // إرسال النموذج تلقائياً
            document.getElementById('testForm').dispatchEvent(new Event('submit'));
        }

        // مسح قاعدة البيانات
        function clearDatabase() {
            if (confirm('هل أنت متأكد من مسح قاعدة البيانات؟')) {
                localStorage.removeItem('cafeteriaDB');
                
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ تم مسح قاعدة البيانات';
                resultDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>
