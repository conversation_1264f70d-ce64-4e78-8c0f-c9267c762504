<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة العاملين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1b5e20;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .step {
            background: #f0f0f0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .staff-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        .status-active {
            color: #2e7d32;
            font-weight: bold;
        }
        .status-inactive {
            color: #c62828;
            font-weight: bold;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام إدارة العاملين</h1>
        <p>اختبار شامل لنظام إنشاء وإدارة حسابات العاملين</p>

        <div class="grid">
            <div class="test-section">
                <h3>1. إنشاء حساب عامل جديد</h3>
                <input type="text" id="staff-username" placeholder="اسم المستخدم" value="">
                <input type="password" id="staff-password" placeholder="كلمة المرور" value="staff123">
                <input type="text" id="staff-name" placeholder="اسم العامل" value="عامل تجريبي">
                <br>
                <select id="staff-type">
                    <option value="cashier">أمين الصندوق</option>
                    <option value="cook">طباخ</option>
                    <option value="manager">مدير</option>
                </select>
                <select id="staff-status">
                    <option value="active">مفعل</option>
                    <option value="inactive">غير مفعل</option>
                </select>
                <br>
                <button onclick="createStaffAccount()">إنشاء حساب عامل</button>
                <div id="create-result" class="result"></div>
            </div>

            <div class="test-section">
                <h3>2. اختبار تسجيل دخول العامل</h3>
                <input type="text" id="login-username" placeholder="اسم المستخدم" value="">
                <input type="password" id="login-password" placeholder="كلمة المرور" value="staff123">
                <br>
                <button onclick="testStaffLogin()">اختبار تسجيل الدخول</button>
                <div id="login-result" class="result"></div>
            </div>

            <div class="test-section">
                <h3>3. عرض جميع العاملين</h3>
                <button onclick="showAllStaff()">عرض العاملين</button>
                <div id="staff-list-result" class="result"></div>
            </div>

            <div class="test-section">
                <h3>4. تفعيل/إلغاء تفعيل حساب</h3>
                <input type="text" id="toggle-username" placeholder="اسم المستخدم" value="">
                <select id="new-status">
                    <option value="active">تفعيل</option>
                    <option value="inactive">إلغاء تفعيل</option>
                </select>
                <br>
                <button onclick="toggleStaffStatus()">تغيير الحالة</button>
                <div id="toggle-result" class="result"></div>
            </div>

            <div class="test-section">
                <h3>5. اختبار التدفق الكامل</h3>
                <button onclick="testCompleteFlow()">اختبار التدفق الكامل</button>
                <div id="complete-flow-result" class="result"></div>
            </div>

            <div class="test-section">
                <h3>6. إعادة تعيين البيانات</h3>
                <button onclick="resetDatabase()" style="background: #d32f2f;">إعادة تعيين قاعدة البيانات</button>
                <div id="reset-result" class="result"></div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // انتظار تحميل النظام
        setTimeout(() => {
            console.log('نظام اختبار إدارة العاملين جاهز');
        }, 1000);

        // إنشاء حساب عامل جديد
        async function createStaffAccount() {
            const resultDiv = document.getElementById('create-result');
            const username = document.getElementById('staff-username').value || `staff_${Date.now()}`;
            const password = document.getElementById('staff-password').value;
            const name = document.getElementById('staff-name').value;
            const staffType = document.getElementById('staff-type').value;
            const status = document.getElementById('staff-status').value;

            if (!password || !name) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء ملء جميع الحقول</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري إنشاء حساب العامل...</div>';

                const db = getDatabase();

                // التحقق من عدم وجود اسم المستخدم
                if (db.users.some(user => user.username === username)) {
                    resultDiv.innerHTML = '<div class="error">✗ اسم المستخدم موجود بالفعل</div>';
                    return;
                }

                // إنشاء حساب العامل الجديد
                const newStaff = {
                    id: Math.max(...db.users.map(u => u.id), 0) + 1,
                    username: username,
                    password: password, // كلمة مرور عادية
                    name: name,
                    role: 'staff',
                    staffType: staffType,
                    schoolId: 1, // مدرسة افتراضية
                    status: status,
                    permissions: getDefaultPermissions(staffType)
                };

                db.users.push(newStaff);
                saveDatabase(db);

                resultDiv.innerHTML = `
                    <div class="success">✓ تم إنشاء حساب العامل بنجاح</div>
                    <div class="info">اسم المستخدم: ${username}</div>
                    <div class="info">كلمة المرور: ${password}</div>
                    <div class="info">النوع: ${getStaffTypeText(staffType)}</div>
                    <div class="info">الحالة: ${status === 'active' ? 'مفعل' : 'غير مفعل'}</div>
                `;

                // ملء حقول تسجيل الدخول تلقائياً
                document.getElementById('login-username').value = username;
                document.getElementById('login-password').value = password;
                document.getElementById('toggle-username').value = username;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إنشاء الحساب: ${error.message}</div>`;
                console.error('Create staff error:', error);
            }
        }

        // اختبار تسجيل دخول العامل
        async function testStaffLogin() {
            const resultDiv = document.getElementById('login-result');
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">🔄 جاري اختبار تسجيل الدخول...</div>';

                const db = getDatabase();
                const user = db.users.find(u => u.username === username && u.role === 'staff');

                if (!user) {
                    resultDiv.innerHTML = '<div class="error">✗ العامل غير موجود</div>';
                    return;
                }

                // التحقق من كلمة المرور
                const passwordMatch = (user.password === password);

                if (!passwordMatch) {
                    resultDiv.innerHTML = `
                        <div class="error">✗ كلمة المرور غير صحيحة</div>
                        <div class="info">كلمة المرور المحفوظة: "${user.password}"</div>
                        <div class="info">كلمة المرور المدخلة: "${password}"</div>
                    `;
                    return;
                }

                // التحقق من حالة الحساب
                if (user.status !== 'active') {
                    resultDiv.innerHTML = '<div class="error">✗ الحساب غير مفعل</div>';
                    return;
                }

                resultDiv.innerHTML = `
                    <div class="success">✓ تم تسجيل الدخول بنجاح!</div>
                    <div class="info">العامل: ${user.name}</div>
                    <div class="info">النوع: ${getStaffTypeText(user.staffType)}</div>
                    <div class="info">الصلاحيات: ${user.permissions ? user.permissions.join(', ') : 'لا توجد'}</div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تسجيل الدخول: ${error.message}</div>`;
                console.error('Login test error:', error);
            }
        }

        // عرض جميع العاملين
        function showAllStaff() {
            const resultDiv = document.getElementById('staff-list-result');
            try {
                const db = getDatabase();
                const staff = db.users.filter(user => user.role === 'staff');

                if (staff.length === 0) {
                    resultDiv.innerHTML = '<div class="info">لا يوجد عاملين في النظام</div>';
                    return;
                }

                let html = '<div class="info">العاملين في النظام:</div>';
                staff.forEach(worker => {
                    const statusClass = worker.status === 'active' ? 'status-active' : 'status-inactive';
                    const statusText = worker.status === 'active' ? 'مفعل' : 'غير مفعل';
                    
                    html += `
                        <div class="staff-card">
                            <strong>${worker.name}</strong> (${worker.username})<br>
                            النوع: ${getStaffTypeText(worker.staffType)}<br>
                            الحالة: <span class="${statusClass}">${statusText}</span><br>
                            كلمة المرور: "${worker.password}"<br>
                            الصلاحيات: ${worker.permissions ? worker.permissions.join(', ') : 'لا توجد'}
                        </div>
                    `;
                });
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في عرض العاملين: ${error.message}</div>`;
            }
        }

        // تفعيل/إلغاء تفعيل حساب عامل
        function toggleStaffStatus() {
            const resultDiv = document.getElementById('toggle-result');
            const username = document.getElementById('toggle-username').value;
            const newStatus = document.getElementById('new-status').value;

            if (!username) {
                resultDiv.innerHTML = '<div class="error">✗ الرجاء إدخال اسم المستخدم</div>';
                return;
            }

            try {
                const db = getDatabase();
                const staffIndex = db.users.findIndex(u => u.username === username && u.role === 'staff');

                if (staffIndex === -1) {
                    resultDiv.innerHTML = '<div class="error">✗ العامل غير موجود</div>';
                    return;
                }

                const oldStatus = db.users[staffIndex].status;
                db.users[staffIndex].status = newStatus;
                saveDatabase(db);

                resultDiv.innerHTML = `
                    <div class="success">✓ تم تغيير حالة الحساب بنجاح</div>
                    <div class="info">العامل: ${db.users[staffIndex].name}</div>
                    <div class="info">الحالة السابقة: ${oldStatus === 'active' ? 'مفعل' : 'غير مفعل'}</div>
                    <div class="info">الحالة الجديدة: ${newStatus === 'active' ? 'مفعل' : 'غير مفعل'}</div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في تغيير الحالة: ${error.message}</div>`;
            }
        }

        // اختبار التدفق الكامل
        async function testCompleteFlow() {
            const resultDiv = document.getElementById('complete-flow-result');
            const testUsername = `teststaff_${Date.now()}`;
            const testPassword = 'test123';

            try {
                resultDiv.innerHTML = '<div class="info">🔄 بدء اختبار التدفق الكامل...</div>';

                // الخطوة 1: إنشاء حساب عامل
                resultDiv.innerHTML += '<div class="step">الخطوة 1: إنشاء حساب عامل جديد...</div>';
                
                const db = getDatabase();
                const newStaff = {
                    id: Math.max(...db.users.map(u => u.id), 0) + 1,
                    username: testUsername,
                    password: testPassword,
                    name: 'عامل اختبار التدفق',
                    role: 'staff',
                    staffType: 'cashier',
                    schoolId: 1,
                    status: 'active',
                    permissions: ['sell', 'view_products']
                };

                db.users.push(newStaff);
                saveDatabase(db);
                resultDiv.innerHTML += '<div class="success">✓ تم إنشاء الحساب</div>';

                // الخطوة 2: اختبار تسجيل الدخول
                resultDiv.innerHTML += '<div class="step">الخطوة 2: اختبار تسجيل الدخول...</div>';
                
                const savedDb = getDatabase();
                const savedUser = savedDb.users.find(u => u.username === testUsername);
                
                if (!savedUser) {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم العثور على العامل</div>';
                    return;
                }

                const passwordMatch = (savedUser.password === testPassword);
                if (!passwordMatch) {
                    resultDiv.innerHTML += '<div class="error">✗ كلمة المرور غير صحيحة</div>';
                    return;
                }

                if (savedUser.status !== 'active') {
                    resultDiv.innerHTML += '<div class="error">✗ الحساب غير مفعل</div>';
                    return;
                }

                resultDiv.innerHTML += '<div class="success">✓ تم تسجيل الدخول بنجاح</div>';

                // الخطوة 3: اختبار إلغاء التفعيل
                resultDiv.innerHTML += '<div class="step">الخطوة 3: اختبار إلغاء التفعيل...</div>';
                
                savedUser.status = 'inactive';
                saveDatabase(savedDb);
                resultDiv.innerHTML += '<div class="success">✓ تم إلغاء تفعيل الحساب</div>';

                // الخطوة 4: اختبار منع الدخول للحساب المُلغى
                resultDiv.innerHTML += '<div class="step">الخطوة 4: اختبار منع الدخول للحساب المُلغى...</div>';
                
                const finalDb = getDatabase();
                const finalUser = finalDb.users.find(u => u.username === testUsername);
                
                if (finalUser.status === 'inactive') {
                    resultDiv.innerHTML += '<div class="success">✓ تم منع الدخول للحساب المُلغى بنجاح</div>';
                } else {
                    resultDiv.innerHTML += '<div class="error">✗ لم يتم منع الدخول للحساب المُلغى</div>';
                }

                resultDiv.innerHTML += '<div class="success"><strong>🎉 اختبار التدفق الكامل نجح!</strong></div>';

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">✗ خطأ في اختبار التدفق: ${error.message}</div>`;
                console.error('Complete flow test error:', error);
            }
        }

        // إعادة تعيين قاعدة البيانات
        function resetDatabase() {
            const resultDiv = document.getElementById('reset-result');
            try {
                localStorage.removeItem('cafeteriaDB');
                resultDiv.innerHTML = '<div class="success">✓ تم إعادة تعيين قاعدة البيانات. يرجى تحديث الصفحة.</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ خطأ في إعادة تعيين قاعدة البيانات: ${error.message}</div>`;
            }
        }

        // دوال مساعدة
        function getStaffTypeText(type) {
            const types = {
                'cashier': 'أمين الصندوق',
                'cook': 'طباخ',
                'manager': 'مدير'
            };
            return types[type] || type;
        }

        function getDefaultPermissions(staffType) {
            const permissions = {
                'cashier': ['sell', 'view_products', 'view_students'],
                'cook': ['view_products', 'view_orders'],
                'manager': ['sell', 'view_products', 'view_students', 'view_reports', 'manage_products', 'manage_orders']
            };
            return permissions[staffType] || [];
        }
    </script>
</body>
</html>
