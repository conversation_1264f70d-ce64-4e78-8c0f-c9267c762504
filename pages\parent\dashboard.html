<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة ولي الأمر - نظام المقاصف الذكية</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4caf50;
            --secondary-color: #2196f3;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --card-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #e8f5e9, #f1f8e9);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* الشريط العلوي المتقدم */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            padding: 12px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-bottom: 1px solid rgba(76, 175, 80, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header:hover {
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
        }

        .header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* لوجو متقدم مع الشوكة والسكينة */
        .header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }

        .header .logo:hover {
            transform: translateY(-1px);
        }

        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 12px;
            color: white;
            font-size: 1.6rem;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .logo-text {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .logo-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-subtitle {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
            line-height: 1;
        }

        /* معلومات المستخدم */
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-welcome {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            line-height: 1;
        }

        .user-role {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
            line-height: 1;
        }

        /* زر تسجيل الخروج المتقدم */
        .logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            position: relative;
            overflow: hidden;
        }

        .logout-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logout-btn:hover::before {
            left: 100%;
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .logout-btn:active {
            transform: translateY(0);
        }

        .logout-btn i {
            font-size: 1rem;
            transition: transform 0.3s ease;
        }

        .logout-btn:hover i {
            transform: rotate(10deg);
        }

        /* المحتوى الرئيسي */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-title {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-size: 2rem;
            font-weight: 700;
        }

        /* الشبكة الرئيسية */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }

        /* قسم الأبناء */
        .children-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--card-shadow);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 700;
        }

        .add-child-btn {
            background: linear-gradient(135deg, var(--primary-color), #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            justify-content: center;
        }

        .add-child-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
        }

        .children-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .child-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .child-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        .child-card.selected {
            border-color: var(--primary-color);
            background: rgba(76, 175, 80, 0.05);
        }

        .child-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .child-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-color);
        }

        .child-balance {
            background: var(--primary-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .child-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .child-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* الشريط الجانبي */
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--secondary-color), #1976d2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
        }

        .quick-action-btn.success {
            background: linear-gradient(135deg, var(--success-color), #45a049);
        }

        .quick-action-btn.warning {
            background: linear-gradient(135deg, var(--warning-color), #f57c00);
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            padding: 5px;
        }

        .close-btn:hover {
            color: var(--danger-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            background: #e0e0e0;
            color: var(--text-color);
        }

        .btn-submit {
            background: var(--primary-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .status-pending {
            background-color: #ffeaa7;
            color: #d35400;
        }

        .status-processing {
            background-color: #81ecec;
            color: #00b894;
        }

        .status-completed {
            background-color: #55efc4;
            color: #00b894;
        }

        .status-cancelled {
            background-color: #fab1a0;
            color: #d63031;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* الجداول */
        .orders-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            margin-top: 30px;
        }

        /* الحساسية */
        .allergies-section {
            margin-top: 20px;
            padding: 20px;
            background: #fff3e0;
            border-radius: 10px;
            border-left: 4px solid var(--warning-color);
        }

        .allergies-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .allergy-tag {
            background: var(--warning-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .remove-allergy {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 0.9rem;
        }

        /* شبكة اختيار المنتجات */
        .products-selection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }

        .product-item {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .product-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .product-item.selected {
            border-color: var(--primary-color);
            background: rgba(76, 175, 80, 0.05);
        }

        .product-item.has-allergy {
            border-color: var(--warning-color);
            background: rgba(255, 152, 0, 0.05);
        }

        .product-item.has-allergy::before {
            content: '⚠️';
            position: absolute;
            top: 5px;
            right: 5px;
            font-size: 1.2rem;
        }

        .product-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-color);
        }

        .product-price {
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        .product-category {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 10px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: var(--dark-color);
            transform: scale(1.1);
        }

        .quantity-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .quantity-display {
            font-weight: bold;
            font-size: 1.1rem;
            color: var(--primary-color);
            min-width: 30px;
            text-align: center;
        }

        .product-stock {
            font-size: 0.8rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .product-stock.in-stock {
            color: var(--success-color);
        }

        .product-stock.out-of-stock {
            color: var(--danger-color);
        }

        .product-item.out-of-stock {
            opacity: 0.6;
            background: #f5f5f5;
        }

        .product-item.out-of-stock .quantity-btn {
            background: #ccc;
            cursor: not-allowed;
        }

        /* الاستجابة */
        @media (max-width: 992px) {
            .header .container {
                padding: 0 15px;
            }

            .logo-title {
                font-size: 1.1rem;
            }

            .logo-subtitle {
                font-size: 0.75rem;
            }

            .user-welcome {
                display: none;
            }

            .logout-btn {
                padding: 10px 16px;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 10px 0;
            }

            .header .container {
                padding: 0 10px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                font-size: 1.4rem;
            }

            .logo-title {
                font-size: 1rem;
            }

            .logo-subtitle {
                font-size: 0.7rem;
            }

            .logout-btn {
                padding: 8px 12px;
                font-size: 0.8rem;
                gap: 6px;
            }

            .logout-btn span {
                display: none;
            }

            .main-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .sidebar {
                order: -1;
                position: static;
            }

            .child-info {
                grid-template-columns: 1fr;
            }

            .child-actions {
                flex-direction: column;
            }

            .form-actions {
                flex-direction: column;
            }

            table th,
            table td {
                padding: 10px 8px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .header .logo {
                gap: 8px;
            }

            .logo-icon {
                width: 35px;
                height: 35px;
                font-size: 1.2rem;
            }

            .logo-title {
                font-size: 0.9rem;
            }

            .logo-subtitle {
                display: none;
            }

            .logout-btn {
                padding: 6px 10px;
                border-radius: 20px;
            }

            .logout-btn i {
                font-size: 0.9rem;
            }

            .main-container {
                padding: 10px;
            }

            .children-section,
            .sidebar {
                padding: 15px;
            }

            .modal-content {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي المتقدم -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="logo-text">
                    <div class="logo-title">سمارت مقصف</div>
                    <div class="logo-subtitle">ولي الأمر</div>
                </div>
            </div>
            <div class="user-info">
                <div class="user-welcome">
                    <div class="user-name" id="parent-name">مرحباً، ولي الأمر</div>
                    <div class="user-role">لوحة تحكم ولي الأمر</div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <h1 class="page-title">لوحة تحكم ولي الأمر</h1>

        <div class="main-grid">
            <!-- قسم الأبناء -->
            <div class="children-section">
                <h2 class="section-title">
                    <i class="fas fa-child"></i>
                    أبنائي
                </h2>

                <button class="add-child-btn" onclick="openAddChildModal()">
                    <i class="fas fa-plus"></i>
                    إضافة ابن جديد
                </button>

                <div class="children-list" id="children-list">
                    <!-- سيتم ملء قائمة الأبناء هنا ديناميكياً -->
                </div>
            </div>

            <!-- الشريط الجانبي للإجراءات السريعة -->
            <div class="sidebar">
                <h3 class="section-title">
                    <i class="fas fa-bolt"></i>
                    إجراءات سريعة
                </h3>

                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="openPreOrderModal()" style="background: linear-gradient(135deg, #2196f3, #1976d2);">
                        <i class="fas fa-shopping-cart"></i>
                        طلب مسبق للابن
                    </button>

                    <button class="quick-action-btn success" onclick="openAddBalanceModal()">
                        <i class="fas fa-plus-circle"></i>
                        إضافة رصيد
                    </button>

                    <button class="quick-action-btn warning" onclick="openManageAllergiesModal()">
                        <i class="fas fa-exclamation-triangle"></i>
                        إدارة الحساسية
                    </button>

                    <button class="quick-action-btn" onclick="viewAllOrders()">
                        <i class="fas fa-list"></i>
                        عرض جميع الطلبات
                    </button>
                </div>
            </div>
        </div>

        <!-- قسم الطلبات الأخيرة -->
        <div class="orders-section">
            <h2 class="section-title">
                <i class="fas fa-shopping-cart"></i>
                آخر الطلبات
            </h2>

            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>اسم الطالب</th>
                            <th>المنتجات</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="orders-table">
                        <!-- سيتم ملء الطلبات هنا ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->

    <!-- نافذة إضافة ابن جديد -->
    <div class="modal" id="addChildModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إضافة ابن جديد</h3>
                <button class="close-btn" onclick="closeModal('addChildModal')">&times;</button>
            </div>

            <form id="addChildForm">
                <div class="form-group">
                    <label for="childName">اسم الطالب:</label>
                    <input type="text" id="childName" name="childName" required>
                </div>

                <div class="form-group">
                    <label for="childNoorId">رقم نور الطالب:</label>
                    <input type="text" id="childNoorId" name="childNoorId" required>
                </div>

                <div class="form-group">
                    <label for="childSchool">المدرسة:</label>
                    <select id="childSchool" name="childSchool" required>
                        <option value="">اختر المدرسة</option>
                        <option value="1">مدرسة الأمل الابتدائية</option>
                        <option value="2">مدرسة النور المتوسطة</option>
                        <option value="3">مدرسة المستقبل الثانوية</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="childGrade">الصف الدراسي:</label>
                    <select id="childGrade" name="childGrade" required>
                        <option value="">اختر الصف</option>
                        <option value="1">الصف الأول</option>
                        <option value="2">الصف الثاني</option>
                        <option value="3">الصف الثالث</option>
                        <option value="4">الصف الرابع</option>
                        <option value="5">الصف الخامس</option>
                        <option value="6">الصف السادس</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeModal('addChildModal')">إلغاء</button>
                    <button type="submit" class="btn btn-submit">إضافة الطالب</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة رصيد -->
    <div class="modal" id="addBalanceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إضافة رصيد</h3>
                <button class="close-btn" onclick="closeModal('addBalanceModal')">&times;</button>
            </div>

            <form id="addBalanceForm">
                <div class="form-group">
                    <label for="balanceChild">اختر الطالب:</label>
                    <select id="balanceChild" name="balanceChild" required>
                        <option value="">اختر الطالب</option>
                        <!-- سيتم ملء قائمة الأطفال هنا ديناميكياً -->
                    </select>
                </div>

                <div class="form-group">
                    <label for="balanceAmount">المبلغ (ريال):</label>
                    <input type="number" id="balanceAmount" name="balanceAmount" min="1" step="0.01" required>
                </div>

                <div class="form-group">
                    <label for="paymentMethod">طريقة الدفع:</label>
                    <select id="paymentMethod" name="paymentMethod" required>
                        <option value="">اختر طريقة الدفع</option>
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة ائتمان</option>
                        <option value="bank">تحويل بنكي</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeModal('addBalanceModal')">إلغاء</button>
                    <button type="submit" class="btn btn-submit">إضافة الرصيد</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إدارة الحساسية -->
    <div class="modal" id="manageAllergiesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إدارة الحساسية</h3>
                <button class="close-btn" onclick="closeModal('manageAllergiesModal')">&times;</button>
            </div>

            <form id="manageAllergiesForm">
                <div class="form-group">
                    <label for="allergyChild">اختر الطالب:</label>
                    <select id="allergyChild" name="allergyChild" required onchange="loadChildAllergies()">
                        <option value="">اختر الطالب</option>
                        <!-- سيتم ملء قائمة الأطفال هنا ديناميكياً -->
                    </select>
                </div>

                <div class="form-group">
                    <label for="newAllergy">إضافة حساسية جديدة:</label>
                    <input type="text" id="newAllergy" name="newAllergy" placeholder="مثال: الفول السوداني">
                </div>

                <button type="button" class="btn btn-submit" onclick="addAllergy()" style="margin-bottom: 20px;">
                    <i class="fas fa-plus"></i>
                    إضافة حساسية
                </button>

                <div class="allergies-section" id="currentAllergies" style="display: none;">
                    <h4>الحساسيات الحالية:</h4>
                    <div class="allergies-list" id="allergiesList">
                        <!-- سيتم عرض الحساسيات هنا -->
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeModal('manageAllergiesModal')">إغلاق</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة الطلب المسبق -->
    <div class="modal" id="preOrderModal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">طلب مسبق للابن</h3>
                <button class="close-btn" onclick="closeModal('preOrderModal')">&times;</button>
            </div>

            <form id="preOrderForm">
                <div class="form-group">
                    <label for="orderChild">اختر الطالب:</label>
                    <select id="orderChild" name="orderChild" required onchange="updateChildInfo()">
                        <option value="">اختر الطالب</option>
                        <!-- سيتم ملء قائمة الأطفال هنا ديناميكياً -->
                    </select>
                </div>

                <!-- معلومات الطالب -->
                <div id="childInfoSection" style="display: none; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                    <h4 style="margin-bottom: 10px; color: var(--primary-color);">معلومات الطالب:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 0.9rem;">
                        <div><strong>الرصيد:</strong> <span id="childBalance">0 ريال</span></div>
                        <div><strong>الحساسيات:</strong> <span id="childAllergies">لا توجد</span></div>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="form-group">
                    <label>اختر المنتجات:</label>
                    <div id="productsGrid" class="products-selection-grid">
                        <!-- سيتم ملء المنتجات هنا ديناميكياً -->
                    </div>
                </div>

                <!-- ملخص الطلب -->
                <div id="orderSummary" style="display: none; margin-top: 20px; padding: 15px; background: #e8f5e9; border-radius: 10px; border-left: 4px solid var(--primary-color);">
                    <h4 style="margin-bottom: 10px; color: var(--primary-color);">ملخص الطلب:</h4>
                    <div id="orderItems"></div>
                    <div style="margin-top: 10px; font-size: 1.1rem; font-weight: bold; color: var(--primary-color);">
                        الإجمالي: <span id="orderTotal">0</span> ريال
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeModal('preOrderModal')">إلغاء</button>
                    <button type="submit" class="btn btn-submit">تأكيد الطلب</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentUser = null;
        let selectedChild = null;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            checkAuth();

            // تحميل بيانات ولي الأمر
            loadParentData();

            // تحميل قائمة الأبناء
            loadChildren();

            // تحميل الطلبات الأخيرة
            loadRecentOrders();

            // إعداد النماذج
            setupForms();
        });

        // التحقق من المصادقة
        function checkAuth() {
            const userSession = sessionStorage.getItem('currentUser');
            if (!userSession) {
                window.location.href = '../auth/login.html';
                return;
            }

            currentUser = JSON.parse(userSession);
            if (currentUser.role !== 'parent') {
                alert('غير مصرح لك بالوصول لهذه الصفحة');
                window.location.href = '../auth/login.html';
                return;
            }

            // تحديث اسم المستخدم في الشريط العلوي
            updateHeaderUserInfo(currentUser);
        }

        // تحديث معلومات المستخدم في الشريط العلوي
        function updateHeaderUserInfo(user) {
            const parentNameElement = document.getElementById('parent-name');
            if (parentNameElement) {
                parentNameElement.textContent = `مرحباً، ${user.name || 'ولي الأمر'}`;
            }
        }

        // تحميل بيانات ولي الأمر
        function loadParentData() {
            const db = getDatabase();
            const parent = db.users.find(u => u.id === currentUser.id);

            if (parent) {
                currentUser = parent;
                updateHeaderUserInfo(parent);
            }
        }

        // الحصول على قاعدة البيانات
        function getDatabase() {
            try {
                return JSON.parse(localStorage.getItem('cafeteriaDB') || '{}');
            } catch {
                return { users: [], orders: [], products: [] };
            }
        }

        // حفظ قاعدة البيانات
        function saveDatabase(db) {
            localStorage.setItem('cafeteriaDB', JSON.stringify(db));
        }

        // تحميل قائمة الأبناء
        function loadChildren() {
            const db = getDatabase();
            const children = db.users.filter(u =>
                u.role === 'student' &&
                (u.parentId === currentUser.id ||
                 u.parentNoorId === currentUser.residenceId ||
                 u.noorId === currentUser.studentNoorId)
            );

            const childrenList = document.getElementById('children-list');
            childrenList.innerHTML = '';

            if (children.length === 0) {
                childrenList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-child" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                        <p>لم يتم إضافة أي أبناء بعد</p>
                        <p style="font-size: 0.9rem;">اضغط على "إضافة ابن جديد" لبدء الإضافة</p>
                    </div>
                `;
                return;
            }

            children.forEach(child => {
                const childCard = createChildCard(child);
                childrenList.appendChild(childCard);
            });

            // تحديث قوائم الأطفال في النماذج
            updateChildrenSelects(children);
        }

        // إنشاء بطاقة طفل
        function createChildCard(child) {
            const div = document.createElement('div');
            div.className = 'child-card';
            div.onclick = () => selectChild(child);

            const allergiesText = child.allergies && child.allergies.length > 0
                ? child.allergies.join(', ')
                : 'لا توجد حساسيات';

            div.innerHTML = `
                <div class="child-header">
                    <div class="child-name">${child.name}</div>
                    <div class="child-balance">${child.balance || 0} ريال</div>
                </div>

                <div class="child-info">
                    <div><strong>رقم نور:</strong> ${child.noorId || 'غير محدد'}</div>
                    <div><strong>المدرسة:</strong> ${getSchoolName(child.schoolId)}</div>
                    <div><strong>الصف:</strong> ${getGradeName(child.grade)}</div>
                    <div><strong>الحساسيات:</strong> ${allergiesText}</div>
                </div>

                <div class="child-actions">
                    <button class="action-btn" onclick="event.stopPropagation(); createPreOrderForChild(${child.id})" style="background: var(--secondary-color);">
                        <i class="fas fa-shopping-cart"></i> طلب مسبق
                    </button>
                    <button class="action-btn btn-primary" onclick="event.stopPropagation(); addBalanceToChild(${child.id})">
                        <i class="fas fa-plus"></i> إضافة رصيد
                    </button>
                    <button class="action-btn btn-warning" onclick="event.stopPropagation(); manageChildAllergies(${child.id})">
                        <i class="fas fa-exclamation-triangle"></i> الحساسية
                    </button>
                    <button class="action-btn btn-secondary" onclick="event.stopPropagation(); viewChildOrders(${child.id})">
                        <i class="fas fa-list"></i> الطلبات
                    </button>
                </div>
            `;

            return div;
        }

        // تحديد طفل
        function selectChild(child) {
            selectedChild = child;

            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.child-card').forEach(card => {
                card.classList.remove('selected');
            });

            // إضافة التحديد للبطاقة الحالية
            event.currentTarget.classList.add('selected');
        }



        // الحصول على اسم المدرسة
        function getSchoolName(schoolId) {
            const schools = {
                1: 'مدرسة الأمل الابتدائية',
                2: 'مدرسة النور المتوسطة',
                3: 'مدرسة المستقبل الثانوية'
            };
            return schools[schoolId] || 'غير محدد';
        }

        // الحصول على اسم الصف
        function getGradeName(grade) {
            const grades = {
                1: 'الصف الأول',
                2: 'الصف الثاني',
                3: 'الصف الثالث',
                4: 'الصف الرابع',
                5: 'الصف الخامس',
                6: 'الصف السادس'
            };
            return grades[grade] || 'غير محدد';
        }

        // متغيرات الطلب المسبق
        let selectedProducts = {};
        let orderTotal = 0;

        // فتح النوافذ المنبثقة
        function openAddChildModal() {
            document.getElementById('addChildModal').classList.add('show');
        }

        function openAddBalanceModal() {
            document.getElementById('addBalanceModal').classList.add('show');
        }

        function openManageAllergiesModal() {
            document.getElementById('manageAllergiesModal').classList.add('show');
        }

        function openPreOrderModal() {
            document.getElementById('preOrderModal').classList.add('show');
            loadProducts();
        }

        // إغلاق النوافذ المنبثقة
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // إعداد النماذج
        function setupForms() {
            // نموذج إضافة طفل
            document.getElementById('addChildForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addNewChild();
            });

            // نموذج إضافة رصيد
            document.getElementById('addBalanceForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addBalance();
            });

            // نموذج الطلب المسبق
            document.getElementById('preOrderForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitPreOrder();
            });

            // إغلاق النوافذ عند النقر خارجها
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.classList.remove('show');
                    }
                });
            });
        }

        // إضافة طفل جديد
        function addNewChild() {
            const form = document.getElementById('addChildForm');
            const formData = new FormData(form);

            const childData = {
                id: Date.now(),
                name: formData.get('childName'),
                noorId: formData.get('childNoorId'),
                schoolId: parseInt(formData.get('childSchool')),
                grade: parseInt(formData.get('childGrade')),
                role: 'student',
                parentId: currentUser.id,
                parentNoorId: currentUser.residenceId,
                balance: 0,
                allergies: [],
                registrationDate: new Date().toLocaleDateString('ar-SA')
            };

            const db = getDatabase();

            // التحقق من عدم وجود رقم نور مكرر
            const existingStudent = db.users.find(u => u.noorId === childData.noorId);
            if (existingStudent) {
                alert('رقم نور الطالب موجود مسبقاً');
                return;
            }

            // إضافة الطالب
            if (!db.users) db.users = [];
            db.users.push(childData);

            saveDatabase(db);

            // إعادة تحميل القائمة
            loadChildren();

            // إغلاق النافذة وإعادة تعيين النموذج
            closeModal('addChildModal');
            form.reset();

            alert('تم إضافة الطالب بنجاح!');
        }

        // إضافة رصيد
        function addBalance() {
            const form = document.getElementById('addBalanceForm');
            const formData = new FormData(form);

            const childId = parseInt(formData.get('balanceChild'));
            const amount = parseFloat(formData.get('balanceAmount'));
            const paymentMethod = formData.get('paymentMethod');

            if (!childId || !amount || !paymentMethod) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child) {
                alert('الطالب غير موجود');
                return;
            }

            // إضافة الرصيد
            child.balance = (child.balance || 0) + amount;

            // إضافة سجل المعاملة
            if (!db.transactions) db.transactions = [];
            db.transactions.push({
                id: Date.now(),
                userId: childId,
                type: 'deposit',
                amount: amount,
                paymentMethod: paymentMethod,
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA'),
                parentId: currentUser.id
            });

            saveDatabase(db);

            // إعادة تحميل القائمة
            loadChildren();

            // إغلاق النافذة وإعادة تعيين النموذج
            closeModal('addBalanceModal');
            form.reset();

            alert(`تم إضافة ${amount} ريال لحساب ${child.name} بنجاح!`);
        }

        // إدارة الحساسية
        function loadChildAllergies() {
            const childId = parseInt(document.getElementById('allergyChild').value);
            if (!childId) {
                document.getElementById('currentAllergies').style.display = 'none';
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child) return;

            const allergiesSection = document.getElementById('currentAllergies');
            const allergiesList = document.getElementById('allergiesList');

            if (child.allergies && child.allergies.length > 0) {
                allergiesSection.style.display = 'block';
                allergiesList.innerHTML = '';

                child.allergies.forEach(allergy => {
                    const allergyTag = document.createElement('div');
                    allergyTag.className = 'allergy-tag';
                    allergyTag.innerHTML = `
                        ${allergy}
                        <button class="remove-allergy" onclick="removeAllergy(${childId}, '${allergy}')">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    allergiesList.appendChild(allergyTag);
                });
            } else {
                allergiesSection.style.display = 'none';
            }
        }

        function addAllergy() {
            const childId = parseInt(document.getElementById('allergyChild').value);
            const newAllergy = document.getElementById('newAllergy').value.trim();

            if (!childId) {
                alert('يرجى اختيار الطالب أولاً');
                return;
            }

            if (!newAllergy) {
                alert('يرجى إدخال نوع الحساسية');
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child) {
                alert('الطالب غير موجود');
                return;
            }

            // التأكد من وجود مصفوفة الحساسيات
            if (!child.allergies) {
                child.allergies = [];
            }

            // التحقق من عدم وجود الحساسية مسبقاً
            if (child.allergies.includes(newAllergy)) {
                alert('هذه الحساسية موجودة مسبقاً');
                return;
            }

            // إضافة الحساسية
            child.allergies.push(newAllergy);
            saveDatabase(db);

            // تحديث العرض
            loadChildAllergies();
            loadChildren();

            // مسح الحقل
            document.getElementById('newAllergy').value = '';

            alert(`تم إضافة حساسية "${newAllergy}" للطالب ${child.name}`);
        }

        function removeAllergy(childId, allergy) {
            if (!confirm(`هل أنت متأكد من حذف حساسية "${allergy}"؟`)) {
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child || !child.allergies) return;

            // إزالة الحساسية
            child.allergies = child.allergies.filter(a => a !== allergy);
            saveDatabase(db);

            // تحديث العرض
            loadChildAllergies();
            loadChildren();

            alert(`تم حذف حساسية "${allergy}"`);
        }

        // وظائف سريعة للأطفال
        function addBalanceToChild(childId) {
            document.getElementById('balanceChild').value = childId;
            openAddBalanceModal();
        }

        function manageChildAllergies(childId) {
            document.getElementById('allergyChild').value = childId;
            openManageAllergiesModal();
            loadChildAllergies();
        }

        function viewChildOrders(childId) {
            // يمكن تطوير هذه الوظيفة لاحقاً لعرض طلبات الطفل
            alert('سيتم تطوير هذه الميزة قريباً');
        }

        // تحميل الطلبات الأخيرة
        function loadRecentOrders() {
            const db = getDatabase();
            const children = db.users.filter(u =>
                u.role === 'student' &&
                (u.parentId === currentUser.id ||
                 u.parentNoorId === currentUser.residenceId ||
                 u.noorId === currentUser.studentNoorId)
            );

            const childrenIds = children.map(c => c.id);
            const orders = (db.orders || []).filter(order =>
                childrenIds.includes(order.userId)
            ).sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 10);

            const ordersTable = document.getElementById('orders-table');
            ordersTable.innerHTML = '';

            if (orders.length === 0) {
                ordersTable.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.3;"></i>
                            <br>لا توجد طلبات حتى الآن
                        </td>
                    </tr>
                `;
                return;
            }

            orders.forEach(order => {
                const child = children.find(c => c.id === order.userId);
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${child ? child.name : 'غير معروف'}</td>
                    <td>${order.products ? order.products.length : 0} منتج</td>
                    <td>${order.totalPrice || 0} ريال</td>
                    <td>${order.date || 'غير محدد'}</td>
                    <td><span class="status-badge status-${order.status || 'pending'}">${getOrderStatusText(order.status)}</span></td>
                `;

                ordersTable.appendChild(row);
            });
        }

        function getOrderStatusText(status) {
            const statusTexts = {
                'pending': 'في الانتظار',
                'processing': 'قيد التحضير',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };
            return statusTexts[status] || 'غير محدد';
        }

        // عرض جميع الطلبات
        function viewAllOrders() {
            // يمكن تطوير هذه الوظيفة لاحقاً
            alert('سيتم تطوير هذه الميزة قريباً');
        }

        // وظائف الطلب المسبق - تم توحيد المنتجات مع منتجات الطالب
        function loadProducts() {
            const db = getDatabase();

            // استخدام نفس منتجات الطالب (منتجات المقصف الموحدة)
            const canteenProducts = [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, icon: '🥙', isPopular: true },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, icon: '🧀', isPopular: false },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, icon: '🐟', isPopular: false },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, icon: '🍗', isPopular: false },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, icon: '🥗', isPopular: false },
                { id: 6, name: 'برجر لحم', price: 12.00, category: 'meals', stock: 6, icon: '🍔', isPopular: true },
                { id: 7, name: 'برجر دجاج', price: 11.00, category: 'meals', stock: 7, icon: '🍗', isPopular: false },
                { id: 8, name: 'بيتزا صغيرة', price: 15.00, category: 'meals', stock: 5, icon: '🍕', isPopular: true },
                { id: 9, name: 'مرامي قشار بطل افخاذ', price: 13.50, category: 'meals', stock: 4, icon: '🌯', isPopular: false },
                { id: 10, name: 'شاورما دجاج', price: 9.50, category: 'meals', stock: 9, icon: '🌯', isPopular: false },

                // المشروبات
                { id: 11, name: 'عصير برتقال طازج', price: 4.00, category: 'drinks', stock: 20, icon: '🍊', isPopular: true },
                { id: 12, name: 'عصير تفاح', price: 3.50, category: 'drinks', stock: 18, icon: '🍎', isPopular: false },
                { id: 13, name: 'عصير مانجو', price: 4.50, category: 'drinks', stock: 15, icon: '🥭', isPopular: false },
                { id: 14, name: 'عصير فراولة', price: 4.00, category: 'drinks', stock: 16, icon: '🍓', isPopular: false },
                { id: 15, name: 'ماء معدني', price: 1.50, category: 'drinks', stock: 30, icon: '💧', isPopular: false },
                { id: 16, name: 'مشروب غازي', price: 3.00, category: 'drinks', stock: 25, icon: '🥤', isPopular: false },
                { id: 17, name: 'شاي مثلج', price: 3.50, category: 'drinks', stock: 12, icon: '🧊', isPopular: false },
                { id: 18, name: 'قهوة باردة', price: 5.00, category: 'drinks', stock: 10, icon: '☕', isPopular: false },
                { id: 19, name: 'لبن رائب', price: 2.50, category: 'drinks', stock: 14, icon: '🥛', isPopular: false },
                { id: 20, name: 'عصير ليمون', price: 3.50, category: 'drinks', stock: 13, icon: '🍋', isPopular: false },

                // الحلويات والوجبات الخفيفة
                { id: 21, name: 'كيك شوكولاتة', price: 6.00, category: 'desserts', stock: 8, icon: '🍰', isPopular: true },
                { id: 22, name: 'دونات محشي', price: 4.50, category: 'desserts', stock: 12, icon: '🍩', isPopular: false },
                { id: 23, name: 'كوكيز', price: 3.00, category: 'desserts', stock: 20, icon: '🍪', isPopular: false },
                { id: 24, name: 'آيس كريم', price: 5.50, category: 'desserts', stock: 15, icon: '🍦', isPopular: true },
                { id: 25, name: 'شيبس', price: 2.50, category: 'snacks', stock: 25, icon: '🥔', isPopular: false },
                { id: 26, name: 'مكسرات', price: 4.00, category: 'snacks', stock: 18, icon: '🥜', isPopular: false },
                { id: 27, name: 'فشار', price: 3.50, category: 'snacks', stock: 22, icon: '🍿', isPopular: false },
                { id: 28, name: 'شوكولاتة', price: 3.00, category: 'desserts', stock: 30, icon: '🍫', isPopular: false },
                { id: 29, name: 'علكة', price: 1.00, category: 'snacks', stock: 50, icon: '🍬', isPopular: false },
                { id: 30, name: 'حلوى جيلي', price: 2.00, category: 'desserts', stock: 35, icon: '🍭', isPopular: false }
            ];

            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = '';
            selectedProducts = {};
            orderTotal = 0;
            updateOrderSummary();

            // عرض رسالة تحميل
            productsGrid.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">جاري تحميل المنتجات...</div>';

            // محاكاة تحميل
            setTimeout(() => {
                productsGrid.innerHTML = '';

                canteenProducts.forEach(product => {
                    const productDiv = document.createElement('div');
                    productDiv.className = 'product-item';
                    productDiv.setAttribute('data-product-id', product.id);

                    // تحديد حالة المخزون
                    const stockStatus = product.stock > 0 ? 'متوفر' : 'غير متوفر';
                    const stockClass = product.stock > 0 ? 'in-stock' : 'out-of-stock';

                    productDiv.innerHTML = `
                        <div class="product-icon">
                            ${product.icon}
                        </div>
                        <div class="product-name">${product.name}</div>
                        <div class="product-price">${product.price.toFixed(2)} ريال</div>
                        <div class="product-category">${getCategoryName(product.category)}</div>
                        <div class="product-stock ${stockClass}">
                            <i class="fas fa-box"></i> ${stockStatus}
                            ${product.stock > 0 ? `(${product.stock})` : ''}
                        </div>
                        <div class="quantity-controls">
                            <button type="button" class="quantity-btn" onclick="changeQuantity(${product.id}, -1)" ${product.stock === 0 ? 'disabled' : ''}>-</button>
                            <span class="quantity-display" id="qty-${product.id}">0</span>
                            <button type="button" class="quantity-btn" onclick="changeQuantity(${product.id}, 1)" ${product.stock === 0 ? 'disabled' : ''}>+</button>
                        </div>
                    `;

                    // إضافة فئة للمنتجات غير المتوفرة
                    if (product.stock === 0) {
                        productDiv.classList.add('out-of-stock');
                    }

                    productsGrid.appendChild(productDiv);
                });

                console.log(`تم تحميل ${canteenProducts.length} منتج موحد للطلب المسبق`);
            }, 500);
        }

        function getDefaultProducts() {
            return [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, status: 'active', emoji: '🥙' },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, status: 'active', emoji: '🧀' },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, status: 'active', emoji: '🐟' },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, status: 'active', emoji: '🐔' },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, status: 'active', emoji: '🥬' },
                { id: 6, name: 'برجر لحم', price: 12.00, category: 'meals', stock: 6, status: 'active', emoji: '🍔' },
                { id: 7, name: 'بيتزا صغيرة', price: 15.00, category: 'meals', stock: 8, status: 'active', emoji: '🍕' },
                { id: 8, name: 'شاورما', price: 11.00, category: 'meals', stock: 10, status: 'active', emoji: '🌯' },

                // المشروبات
                { id: 9, name: 'عصير برتقال', price: 4.00, category: 'drinks', stock: 20, status: 'active', emoji: '🍊' },
                { id: 10, name: 'عصير تفاح', price: 4.00, category: 'drinks', stock: 18, status: 'active', emoji: '🍎' },
                { id: 11, name: 'ماء', price: 1.50, category: 'drinks', stock: 25, status: 'active', emoji: '💧' },
                { id: 12, name: 'مشروب غازي', price: 3.50, category: 'drinks', stock: 15, status: 'active', emoji: '🥤' },
                { id: 13, name: 'شاي', price: 2.00, category: 'drinks', stock: 30, status: 'active', emoji: '🍵' },
                { id: 14, name: 'قهوة', price: 3.00, category: 'drinks', stock: 22, status: 'active', emoji: '☕' },
                { id: 15, name: 'عصير مانجو', price: 4.50, category: 'drinks', stock: 16, status: 'active', emoji: '🥭' },
                { id: 16, name: 'لبن', price: 2.50, category: 'drinks', stock: 20, status: 'active', emoji: '🥛' },

                // الوجبات الخفيفة
                { id: 17, name: 'بسكويت', price: 2.50, category: 'snacks', stock: 40, status: 'active', emoji: '🍪' },
                { id: 18, name: 'شيبس', price: 3.00, category: 'snacks', stock: 35, status: 'active', emoji: '🍟' },
                { id: 19, name: 'مكسرات', price: 5.00, category: 'snacks', stock: 20, status: 'active', emoji: '🥜' },
                { id: 20, name: 'كراكرز', price: 2.00, category: 'snacks', stock: 25, status: 'active', emoji: '🍘' },
                { id: 21, name: 'بوشار', price: 3.50, category: 'snacks', stock: 30, status: 'active', emoji: '🍿' },
                { id: 22, name: 'شوكولاتة', price: 4.00, category: 'snacks', stock: 25, status: 'active', emoji: '🍫' },

                // الحلويات
                { id: 23, name: 'كيك شوكولاتة', price: 6.00, category: 'desserts', stock: 12, status: 'active', emoji: '🍰' },
                { id: 24, name: 'دونات', price: 4.50, category: 'desserts', stock: 15, status: 'active', emoji: '🍩' },
                { id: 25, name: 'كوكيز', price: 3.50, category: 'desserts', stock: 20, status: 'active', emoji: '🍪' },
                { id: 26, name: 'آيس كريم', price: 5.50, category: 'desserts', stock: 10, status: 'active', emoji: '🍦' },
                { id: 27, name: 'كنافة', price: 7.00, category: 'desserts', stock: 8, status: 'active', emoji: '🧁' },
                { id: 28, name: 'بقلاوة', price: 6.50, category: 'desserts', stock: 12, status: 'active', emoji: '🥮' },

                // الفواكه
                { id: 29, name: 'تفاحة', price: 2.00, category: 'fruits', stock: 30, status: 'active', emoji: '🍎' },
                { id: 30, name: 'موزة', price: 1.50, category: 'fruits', stock: 25, status: 'active', emoji: '🍌' },
                { id: 31, name: 'برتقالة', price: 2.50, category: 'fruits', stock: 20, status: 'active', emoji: '🍊' },
                { id: 32, name: 'عنب', price: 4.00, category: 'fruits', stock: 15, status: 'active', emoji: '🍇' },
                { id: 33, name: 'فراولة', price: 5.00, category: 'fruits', stock: 18, status: 'active', emoji: '🍓' },
                { id: 34, name: 'كيوي', price: 3.00, category: 'fruits', stock: 12, status: 'active', emoji: '🥝' }
            ];
        }

        function getProductIcon(category) {
            const icons = {
                'meals': 'fas fa-hamburger',
                'sandwiches': 'fas fa-hamburger',
                'drinks': 'fas fa-glass-whiskey',
                'desserts': 'fas fa-birthday-cake',
                'snacks': 'fas fa-cookie-bite',
                'fruits': 'fas fa-apple-alt'
            };
            return icons[category] || 'fas fa-utensils';
        }

        function getCategoryName(category) {
            const names = {
                'meals': 'وجبات رئيسية',
                'sandwiches': 'ساندويشات',
                'drinks': 'مشروبات',
                'desserts': 'حلويات',
                'snacks': 'وجبات خفيفة',
                'fruits': 'فواكه'
            };
            return names[category] || 'أخرى';
        }

        function changeQuantity(productId, change) {
            // استخدام نفس منتجات الطالب (منتجات المقصف الموحدة)
            const canteenProducts = [
                // الوجبات الرئيسية
                { id: 1, name: 'ساندويش فلافل', price: 8.50, category: 'meals', stock: 15, icon: '🥙', isPopular: true },
                { id: 2, name: 'ساندويش جبن', price: 7.00, category: 'meals', stock: 12, icon: '🧀', isPopular: false },
                { id: 3, name: 'ساندويش تونة', price: 9.00, category: 'meals', stock: 10, icon: '🐟', isPopular: false },
                { id: 4, name: 'ساندويش دجاج', price: 10.00, category: 'meals', stock: 8, icon: '🍗', isPopular: false },
                { id: 5, name: 'ساندويش خضار', price: 6.50, category: 'meals', stock: 11, icon: '🥗', isPopular: false },
                { id: 6, name: 'برجر لحم', price: 12.00, category: 'meals', stock: 6, icon: '🍔', isPopular: true },
                { id: 7, name: 'برجر دجاج', price: 11.00, category: 'meals', stock: 7, icon: '🍗', isPopular: false },
                { id: 8, name: 'بيتزا صغيرة', price: 15.00, category: 'meals', stock: 5, icon: '🍕', isPopular: true },
                { id: 9, name: 'مرامي قشار بطل افخاذ', price: 13.50, category: 'meals', stock: 4, icon: '🌯', isPopular: false },
                { id: 10, name: 'شاورما دجاج', price: 9.50, category: 'meals', stock: 9, icon: '🌯', isPopular: false },

                // المشروبات
                { id: 11, name: 'عصير برتقال طازج', price: 4.00, category: 'drinks', stock: 20, icon: '🍊', isPopular: true },
                { id: 12, name: 'عصير تفاح', price: 3.50, category: 'drinks', stock: 18, icon: '🍎', isPopular: false },
                { id: 13, name: 'عصير مانجو', price: 4.50, category: 'drinks', stock: 15, icon: '🥭', isPopular: false },
                { id: 14, name: 'عصير فراولة', price: 4.00, category: 'drinks', stock: 16, icon: '🍓', isPopular: false },
                { id: 15, name: 'ماء معدني', price: 1.50, category: 'drinks', stock: 30, icon: '💧', isPopular: false },
                { id: 16, name: 'مشروب غازي', price: 3.00, category: 'drinks', stock: 25, icon: '🥤', isPopular: false },
                { id: 17, name: 'شاي مثلج', price: 3.50, category: 'drinks', stock: 12, icon: '🧊', isPopular: false },
                { id: 18, name: 'قهوة باردة', price: 5.00, category: 'drinks', stock: 10, icon: '☕', isPopular: false },
                { id: 19, name: 'لبن رائب', price: 2.50, category: 'drinks', stock: 14, icon: '🥛', isPopular: false },
                { id: 20, name: 'عصير ليمون', price: 3.50, category: 'drinks', stock: 13, icon: '🍋', isPopular: false },

                // الحلويات والوجبات الخفيفة
                { id: 21, name: 'كيك شوكولاتة', price: 6.00, category: 'desserts', stock: 8, icon: '🍰', isPopular: true },
                { id: 22, name: 'دونات محشي', price: 4.50, category: 'desserts', stock: 12, icon: '🍩', isPopular: false },
                { id: 23, name: 'كوكيز', price: 3.00, category: 'desserts', stock: 20, icon: '🍪', isPopular: false },
                { id: 24, name: 'آيس كريم', price: 5.50, category: 'desserts', stock: 15, icon: '🍦', isPopular: true },
                { id: 25, name: 'شيبس', price: 2.50, category: 'snacks', stock: 25, icon: '🥔', isPopular: false },
                { id: 26, name: 'مكسرات', price: 4.00, category: 'snacks', stock: 18, icon: '🥜', isPopular: false },
                { id: 27, name: 'فشار', price: 3.50, category: 'snacks', stock: 22, icon: '🍿', isPopular: false },
                { id: 28, name: 'شوكولاتة', price: 3.00, category: 'desserts', stock: 30, icon: '🍫', isPopular: false },
                { id: 29, name: 'علكة', price: 1.00, category: 'snacks', stock: 50, icon: '🍬', isPopular: false },
                { id: 30, name: 'حلوى جيلي', price: 2.00, category: 'desserts', stock: 35, icon: '🍭', isPopular: false }
            ];

            const product = canteenProducts.find(p => p.id === productId);
            if (!product) return;

            const currentQty = selectedProducts[productId] ? selectedProducts[productId].quantity : 0;
            const newQty = Math.max(0, Math.min(product.stock, currentQty + change));

            if (newQty === 0) {
                delete selectedProducts[productId];
                document.querySelector(`[data-product-id="${productId}"]`).classList.remove('selected');
            } else {
                selectedProducts[productId] = {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: newQty
                };
                document.querySelector(`[data-product-id="${productId}"]`).classList.add('selected');
            }

            document.getElementById(`qty-${productId}`).textContent = newQty;
            updateOrderSummary();
        }

        function updateOrderSummary() {
            const orderItems = document.getElementById('orderItems');
            const orderTotalElement = document.getElementById('orderTotal');
            const orderSummary = document.getElementById('orderSummary');

            orderItems.innerHTML = '';
            orderTotal = 0;

            if (Object.keys(selectedProducts).length === 0) {
                orderSummary.style.display = 'none';
                return;
            }

            orderSummary.style.display = 'block';

            Object.values(selectedProducts).forEach(product => {
                const itemTotal = product.price * product.quantity;
                orderTotal += itemTotal;

                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = 'display: flex; justify-content: space-between; margin-bottom: 5px; padding: 5px 0; border-bottom: 1px solid #eee;';
                itemDiv.innerHTML = `
                    <span>${product.name} × ${product.quantity}</span>
                    <span>${itemTotal} ريال</span>
                `;
                orderItems.appendChild(itemDiv);
            });

            orderTotalElement.textContent = orderTotal;
        }

        function updateChildInfo() {
            const childId = parseInt(document.getElementById('orderChild').value);
            const childInfoSection = document.getElementById('childInfoSection');

            if (!childId) {
                childInfoSection.style.display = 'none';
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (child) {
                childInfoSection.style.display = 'block';
                document.getElementById('childBalance').textContent = `${child.balance || 0} ريال`;

                const allergiesText = child.allergies && child.allergies.length > 0
                    ? child.allergies.join(', ')
                    : 'لا توجد حساسيات';
                document.getElementById('childAllergies').textContent = allergiesText;

                // تحديث المنتجات لإظهار التحذيرات من الحساسية
                updateProductAllergies(child.allergies || []);
            }
        }

        function updateProductAllergies(allergies) {
            document.querySelectorAll('.product-item').forEach(item => {
                item.classList.remove('has-allergy');
                const productName = item.querySelector('.product-name').textContent.toLowerCase();

                allergies.forEach(allergy => {
                    if (productName.includes(allergy.toLowerCase())) {
                        item.classList.add('has-allergy');
                    }
                });
            });
        }

        function submitPreOrder() {
            const childId = parseInt(document.getElementById('orderChild').value);

            if (!childId) {
                alert('يرجى اختيار الطالب');
                return;
            }

            if (Object.keys(selectedProducts).length === 0) {
                alert('يرجى اختيار منتج واحد على الأقل');
                return;
            }

            const db = getDatabase();
            const child = db.users.find(u => u.id === childId);

            if (!child) {
                alert('الطالب غير موجود');
                return;
            }

            if (child.balance < orderTotal) {
                alert(`رصيد الطالب غير كافي. الرصيد الحالي: ${child.balance} ريال، المطلوب: ${orderTotal} ريال`);
                return;
            }

            // التحقق من المخزون
            const outOfStockItems = [];
            Object.values(selectedProducts).forEach(product => {
                const dbProduct = db.products.find(p => p.id === product.id);
                if (dbProduct && dbProduct.stock < product.quantity) {
                    outOfStockItems.push(`${product.name} (متوفر: ${dbProduct.stock}, مطلوب: ${product.quantity})`);
                }
            });

            if (outOfStockItems.length > 0) {
                alert(`بعض المنتجات غير متوفرة بالكمية المطلوبة:\n${outOfStockItems.join('\n')}`);
                return;
            }

            // إنشاء الطلب
            const order = {
                id: Date.now(),
                userId: childId,
                products: Object.values(selectedProducts).map(p => ({
                    productId: p.id,
                    name: p.name,
                    quantity: p.quantity,
                    price: p.price
                })),
                totalPrice: orderTotal,
                status: 'pending',
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA'),
                type: 'preorder',
                parentId: currentUser.id,
                parentName: currentUser.name
            };

            // خصم المبلغ من رصيد الطالب
            child.balance -= orderTotal;

            // تحديث المخزون
            Object.values(selectedProducts).forEach(product => {
                const dbProduct = db.products.find(p => p.id === product.id);
                if (dbProduct) {
                    dbProduct.stock -= product.quantity;
                }
            });

            // إضافة الطلب
            if (!db.orders) db.orders = [];
            db.orders.push(order);

            // إرسال الإشعارات
            sendPreOrderNotifications(order, child);

            saveDatabase(db);

            // إرسال الطلب تلقائياً لصفحة العامل
            sendOrderToStaffPageFromParent(order);

            // إعادة تحميل البيانات
            loadChildren();
            loadRecentOrders();

            // إغلاق النافذة وإعادة تعيين النموذج
            closeModal('preOrderModal');
            document.getElementById('preOrderForm').reset();
            selectedProducts = {};
            orderTotal = 0;

            alert(`تم إنشاء الطلب المسبق بنجاح!\nرقم الطلب: ${order.id}\nالمبلغ: ${orderTotal} ريال\n\nتم إرسال إشعار للطالب والعاملين`);
        }

        function createPreOrderForChild(childId) {
            document.getElementById('orderChild').value = childId;
            openPreOrderModal();
            updateChildInfo();
        }

        // تحديث قوائم الأطفال في نماذج الطلب المسبق
        function updateChildrenSelects(children) {
            const selects = ['balanceChild', 'allergyChild', 'orderChild'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    // الاحتفاظ بالخيار الأول
                    const firstOption = select.querySelector('option[value=""]');
                    select.innerHTML = '';
                    if (firstOption) {
                        select.appendChild(firstOption);
                    }

                    // إضافة الأطفال
                    children.forEach(child => {
                        const option = document.createElement('option');
                        option.value = child.id;
                        option.textContent = child.name;
                        select.appendChild(option);
                    });
                }
            });
        }

        // إرسال إشعارات الطلب المسبق
        function sendPreOrderNotifications(order, child) {
            const db = getDatabase();

            // تأكد من وجود مصفوفة الإشعارات
            if (!db.notifications) {
                db.notifications = [];
            }

            const now = new Date();
            const productsList = order.products.map(p => `${p.name} (${p.quantity})`).join(', ');

            // إشعار للطالب
            const studentNotification = {
                id: Date.now() + 1,
                userId: child.id,
                title: '🛒 طلب مسبق من ولي الأمر',
                message: `قام ولي أمرك ${currentUser.name} بطلب: ${productsList}. المبلغ: ${order.totalPrice} ريال. يمكنك استلام الطلب من المقصف.`,
                type: 'preorder',
                status: 'unread',
                createdAt: now.toISOString(),
                metadata: {
                    orderId: order.id,
                    parentName: currentUser.name,
                    totalPrice: order.totalPrice
                }
            };

            // إشعار للعاملين (جميع المستخدمين من نوع staff)
            const staffUsers = db.users.filter(u => u.role === 'staff');
            staffUsers.forEach(staff => {
                const staffNotification = {
                    id: Date.now() + Math.random() * 1000,
                    userId: staff.id,
                    title: '📋 طلب مسبق جديد',
                    message: `طلب مسبق جديد من ولي الأمر ${currentUser.name} للطالب ${child.name}. المنتجات: ${productsList}. المبلغ: ${order.totalPrice} ريال.`,
                    type: 'staff_preorder',
                    status: 'unread',
                    createdAt: now.toISOString(),
                    metadata: {
                        orderId: order.id,
                        studentName: child.name,
                        parentName: currentUser.name,
                        totalPrice: order.totalPrice,
                        products: order.products
                    }
                };

                db.notifications.push(staffNotification);
            });

            // إضافة إشعار الطالب
            db.notifications.push(studentNotification);

            console.log(`تم إرسال ${staffUsers.length + 1} إشعار للطلب المسبق رقم ${order.id}`);
        }

        // دالة إرسال الطلب تلقائياً لصفحة العامل من ولي الأمر
        function sendOrderToStaffPageFromParent(order) {
            try {
                // إنشاء حدث مخصص لإشعار صفحة العامل
                const orderEvent = new CustomEvent('newPreOrder', {
                    detail: {
                        order: order,
                        timestamp: new Date().toISOString(),
                        source: 'parent'
                    }
                });

                // إرسال الحدث
                window.dispatchEvent(orderEvent);

                // حفظ في localStorage للتأكد من وصول الطلب
                const staffOrders = JSON.parse(localStorage.getItem('staffPendingOrders')) || [];
                staffOrders.push({
                    ...order,
                    receivedAt: new Date().toISOString(),
                    source: 'parent'
                });
                localStorage.setItem('staffPendingOrders', JSON.stringify(staffOrders));

                // إشعار فوري للعاملين المتصلين
                broadcastToStaffPagesFromParent(order);

                console.log('✅ تم إرسال الطلب المسبق تلقائياً لصفحة العامل من ولي الأمر:', order.id);
            } catch (error) {
                console.error('❌ خطأ في إرسال الطلب لصفحة العامل من ولي الأمر:', error);
            }
        }

        // دالة بث الطلب لجميع صفحات العاملين المفتوحة من ولي الأمر
        function broadcastToStaffPagesFromParent(order) {
            try {
                // استخدام BroadcastChannel للتواصل بين الصفحات
                if (typeof BroadcastChannel !== 'undefined') {
                    const channel = new BroadcastChannel('canteen-orders');
                    channel.postMessage({
                        type: 'NEW_PREORDER',
                        order: order,
                        timestamp: new Date().toISOString(),
                        source: 'parent'
                    });

                    console.log('📡 تم بث الطلب المسبق لجميع صفحات العاملين من ولي الأمر');
                }

                // استخدام localStorage كبديل
                const broadcastData = {
                    type: 'NEW_PREORDER',
                    order: order,
                    timestamp: new Date().toISOString(),
                    source: 'parent',
                    id: Date.now()
                };

                localStorage.setItem('orderBroadcast', JSON.stringify(broadcastData));

                // إزالة البيانات بعد ثانية واحدة
                setTimeout(() => {
                    localStorage.removeItem('orderBroadcast');
                }, 1000);

            } catch (error) {
                console.error('خطأ في بث الطلب من ولي الأمر:', error);
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                sessionStorage.removeItem('currentUser');
                window.location.href = '../../index.html';
            }
        }
    </script>
</body>
</html>
