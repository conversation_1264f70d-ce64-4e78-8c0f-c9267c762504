<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .add-btn i {
            margin-left: 5px;
        }

        .add-btn:hover {
            background-color: var(--dark-color);
        }

        .search-filter {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-box i {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .search-box input {
            width: 100%;
            padding: 10px 35px 10px 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .filter-group {
            display: flex;
            align-items: center;
        }

        .filter-group label {
            margin-left: 10px;
        }

        .filter-group select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: right;
        }

        table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }

        table tr {
            border-bottom: 1px solid #eee;
        }

        table tr:last-child {
            border-bottom: none;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-left: 5px;
            border: none;
        }

        .view-btn {
            background-color: #2196f3;
            color: white;
        }

        .edit-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            margin: 0 5px;
            border: 1px solid #ddd;
            background-color: white;
            border-radius: 5px;
            cursor: pointer;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 500px;
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h3 {
            font-size: 1.3rem;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .form-actions button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }

        .cancel-btn {
            background-color: #f5f5f5;
            color: #333;
        }

        .save-btn {
            background-color: var(--primary-color);
            color: white;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .search-filter {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-box {
                width: 100%;
                margin-bottom: 10px;
            }

            .filter-group {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>مدير النظام</strong></span>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>مدير النظام</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="schools.html"><i class="fas fa-school"></i> إدارة المدارس</a></li>
                <li><a href="users.html" class="active"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="staff.html"><i class="fas fa-user-tie"></i> إدارة العاملين</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> إدارة المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> إدارة الطلبات</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>إدارة المستخدمين</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>
                    قائمة المستخدمين
                    <button id="add-user-btn" class="add-btn"><i class="fas fa-plus"></i> إضافة مستخدم</button>
                </h2>

                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-input" placeholder="بحث عن مستخدم...">
                    </div>
                    <div class="filter-group">
                        <label for="role-filter">تصفية حسب:</label>
                        <select id="role-filter">
                            <option value="all">جميع المستخدمين</option>
                            <option value="admin">مدير النظام</option>
                            <option value="school">مدير المدرسة</option>
                            <option value="parent">ولي الأمر</option>
                            <option value="student">الطالب</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المستخدم</th>
                                <th>الاسم</th>
                                <th>نوع المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="users-table">
                            <!-- سيتم ملء هذا الجدول ديناميكيًا -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="pagination">
                    <!-- سيتم ملء هذا القسم ديناميكيًا -->
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for adding/editing user -->
    <div class="modal" id="user-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة مستخدم جديد</h3>
                <button class="close-btn">&times;</button>
            </div>
            <form id="user-form">
                <div class="form-group">
                    <label for="user-role">نوع المستخدم</label>
                    <select id="user-role" required>
                        <option value="">اختر نوع المستخدم</option>
                        <option value="admin">مدير النظام</option>
                        <option value="school">مدير المدرسة</option>
                        <option value="parent">ولي الأمر</option>
                        <option value="student">الطالب</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="user-username">اسم المستخدم</label>
                    <input type="text" id="user-username" required>
                </div>
                <div class="form-group">
                    <label for="user-password">كلمة المرور</label>
                    <input type="password" id="user-password" required>
                </div>
                <div class="form-group">
                    <label for="user-name">الاسم</label>
                    <input type="text" id="user-name" required>
                </div>
                <div class="form-group">
                    <label for="user-email">البريد الإلكتروني</label>
                    <input type="email" id="user-email">
                </div>
                <div class="form-group">
                    <label for="user-phone">رقم الهاتف</label>
                    <input type="tel" id="user-phone">
                </div>

                <!-- حقول إضافية حسب نوع المستخدم -->
                <div id="school-fields" style="display: none;">
                    <div class="form-group">
                        <label for="user-school">المدرسة</label>
                        <select id="user-school"></select>
                    </div>
                </div>

                <div id="student-fields" style="display: none;">
                    <div class="form-group">
                        <label for="user-noor-id">رقم نور</label>
                        <input type="text" id="user-noor-id">
                    </div>
                    <div class="form-group">
                        <label for="user-school-id">المدرسة</label>
                        <select id="user-school-id"></select>
                    </div>
                    <div class="form-group">
                        <label for="user-grade">الصف</label>
                        <select id="user-grade">
                            <option value="1">الصف الأول</option>
                            <option value="2">الصف الثاني</option>
                            <option value="3">الصف الثالث</option>
                            <option value="4">الصف الرابع</option>
                            <option value="5">الصف الخامس</option>
                            <option value="6">الصف السادس</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="user-parent">ولي الأمر</label>
                        <select id="user-parent"></select>
                    </div>
                </div>

                <div id="parent-fields" style="display: none;">
                    <!-- سيتم ملء هذا القسم ديناميكيًا بالأبناء -->
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn">إلغاء</button>
                    <button type="submit" class="save-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script>
        // وظيفة للتحقق من صلاحيات المستخدم
        function checkUserAuthorization() {
            try {
                // محاولة الحصول على بيانات المستخدم من sessionStorage
                let currentUser = null;
                try {
                    const userDataString = sessionStorage.getItem('currentUser');
                    if (userDataString) {
                        currentUser = JSON.parse(userDataString);
                    }
                } catch (sessionError) {
                    console.error('خطأ في قراءة بيانات المستخدم من sessionStorage:', sessionError);
                }

                // إذا لم يتم العثور على بيانات المستخدم في sessionStorage، نحاول الحصول عليها من localStorage
                if (!currentUser || !currentUser.role) {
                    const userRole = localStorage.getItem('currentUserRole');
                    const userId = localStorage.getItem('currentUserId');

                    if (userRole === 'admin' && userId) {
                        // إنشاء كائن مستخدم بسيط
                        currentUser = {
                            id: parseInt(userId),
                            role: userRole,
                            name: 'مدير النظام'
                        };

                        // محاولة استعادة بيانات المستخدم الكاملة من قاعدة البيانات
                        try {
                            const db = getDatabase();
                            const fullUserData = db.users.find(u => u.id === parseInt(userId) && u.role === userRole);
                            if (fullUserData) {
                                currentUser = fullUserData;
                                // تخزين البيانات الكاملة في sessionStorage
                                sessionStorage.setItem('currentUser', JSON.stringify(fullUserData));
                            }
                        } catch (dbError) {
                            console.error('خطأ في استعادة بيانات المستخدم من قاعدة البيانات:', dbError);
                        }
                    }
                }

                // التحقق من صلاحيات المستخدم
                if (!currentUser || currentUser.role !== 'admin') {
                    alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                    window.location.href = '../auth/login.html';
                    return null;
                }

                return currentUser;
            } catch (error) {
                console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
                alert('حدث خطأ في التحقق من صلاحيات المستخدم. سيتم توجيهك إلى صفحة تسجيل الدخول.');
                window.location.href = '../auth/login.html';
                return null;
            }
        }
    </script>
</body>
</html>
