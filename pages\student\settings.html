<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - فواصل النجاح للخدمات الإعاشة</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/modern.css">
    <link rel="stylesheet" href="../../assets/css/dark-mode.css">
    <link rel="stylesheet" href="../../assets/css/animations.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@400;700&family=Almarai:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        /* تصغير الشريط العلوي */
        .compact-header {
            padding: 8px 0;
            min-height: 50px;
        }

        .compact-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .compact-header .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* لوجو الشوكة والسكينة */
        .logo-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            border-radius: 10px;
            color: white;
            font-size: 1.4rem;
            box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .logo-icon:hover::before {
            left: 100%;
        }

        .logo-icon:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .compact-header .logo h1 {
            font-size: 1.1rem;
            margin: 0;
        }

        .compact-header .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .compact-header .user-menu span {
            font-size: 0.9rem;
        }

        /* زر تسجيل الخروج الأحمر في الشريط العلوي */
        .header-logout-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .header-logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .header-logout-btn:active {
            transform: translateY(0);
        }

        .header-logout-btn i {
            font-size: 0.8rem;
        }

        .dashboard {
            display: flex;
            min-height: calc(100vh - 50px);
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            transition: all 0.3s ease;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid var(--secondary-color);
        }

        .sidebar-menu li a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .content-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .content-card h2 {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #eee;
        }

        .theme-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .theme-option {
            border: 2px solid transparent;
            border-radius: 5px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .theme-option.active {
            border-color: var(--primary-color);
        }

        .theme-preview {
            height: 150px;
            display: flex;
            flex-direction: column;
        }

        .theme-header {
            height: 30%;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: 500;
        }

        .theme-body {
            height: 70%;
            display: flex;
        }

        .theme-sidebar {
            width: 30%;
            height: 100%;
        }

        .theme-content {
            width: 70%;
            height: 100%;
            background-color: #f5f5f5;
            padding: 10px;
        }

        .theme-name {
            text-align: center;
            padding: 10px;
            font-weight: 500;
            border-top: 1px solid #eee;
        }

        .notification-settings {
            margin-bottom: 20px;
        }

        .notification-option {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .notification-option input[type="checkbox"] {
            margin-left: 10px;
            width: auto;
        }

        .logout-btn {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            width: 90%;
            margin-right: 5%;
        }

        .logout-btn i {
            margin-left: 8px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .theme-options {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header compact-header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h1><a href="../../index.html">فواصل النجاح للخدمات الإعاشة</a></h1>
            </div>
            <div class="user-menu">
                <span id="user-name">مرحبًا، <strong>أحمد محمد</strong></span>
                <button id="header-logout-btn" class="header-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>لوحة التحكم</h2>
                <p>الطالب</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> الرئيسية</a></li>
                <li><a href="products.html"><i class="fas fa-store"></i> المنتجات</a></li>
                <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> طلباتي</a></li>
                <li><a href="notifications.html"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> الإعدادات</a></li>
            </ul>
            <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
        </div>

        <div class="main-content">
            <div class="page-header">
                <h1>الإعدادات</h1>
                <div class="date">
                    <i class="far fa-calendar-alt"></i>
                    <span id="current-date"></span>
                </div>
            </div>

            <div class="content-card">
                <h2>المعلومات الشخصية</h2>
                <div class="settings-section">
                    <form id="profile-form">
                        <div class="form-group">
                            <label for="name">الاسم</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email">
                        </div>
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="content-card">
                <h2>تغيير كلمة المرور</h2>
                <div class="settings-section">
                    <form id="password-form">
                        <div class="form-group">
                            <label for="current-password">كلمة المرور الحالية</label>
                            <input type="password" id="current-password" name="current-password" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">كلمة المرور الجديدة</label>
                            <input type="password" id="new-password" name="new-password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" id="confirm-password" name="confirm-password" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="content-card">
                <h2>إعدادات الإشعارات</h2>
                <div class="settings-section">
                    <h3 style="margin-bottom: 15px; color: var(--primary-color);">إشعاراتي</h3>
                    <div class="notification-settings">
                        <div class="notification-option">
                            <input type="checkbox" id="order-notifications" checked>
                            <label for="order-notifications">إشعارات الطلبات الجديدة</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="balance-notifications" checked>
                            <label for="balance-notifications">إشعارات تغيير الرصيد</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="status-notifications" checked>
                            <label for="status-notifications">إشعارات تغيير حالة الطلب</label>
                        </div>
                    </div>

                    <h3 style="margin: 25px 0 15px; color: var(--primary-color); border-top: 1px solid #eee; padding-top: 20px;">إشعارات ولي الأمر</h3>
                    <p style="margin-bottom: 15px; color: #666;">يمكنك تحديد أنواع الإشعارات التي سيتلقاها ولي الأمر عند قيامك بعمليات الشراء أو تغيير الإعدادات.</p>
                    <div class="notification-settings">
                        <div class="notification-option">
                            <input type="checkbox" id="parent-purchase-notifications" checked>
                            <label for="parent-purchase-notifications">إشعار ولي الأمر عند إجراء عملية شراء</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="parent-balance-notifications" checked>
                            <label for="parent-balance-notifications">إشعار ولي الأمر عند تغيير الرصيد</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="parent-login-notifications">
                            <label for="parent-login-notifications">إشعار ولي الأمر عند تسجيل الدخول</label>
                        </div>
                        <div class="notification-option">
                            <input type="checkbox" id="parent-daily-summary" checked>
                            <label for="parent-daily-summary">إرسال ملخص يومي لولي الأمر عن المشتريات</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button id="save-notifications" class="btn btn-primary">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <h2>تخصيص المظهر</h2>
                <div class="settings-section">
                    <div class="theme-options" id="theme-options">
                        <!-- سيتم ملء هذا القسم ديناميكيًا -->
                    </div>
                    <div class="form-actions">
                        <button id="apply-theme" class="btn btn-primary">تطبيق المظهر</button>
                        <a href="../settings/customization.html" class="btn btn-secondary">خيارات تخصيص متقدمة</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/theme-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is student
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            if (!currentUser || currentUser.role !== 'student') {
                alert('غير مصرح لك بالوصول إلى هذه الصفحة!');
                window.location.href = '../auth/login.html';
                return;
            }

            // Set user name
            document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + currentUser.name + '</strong>';

            // Set current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);

            // Get database
            const db = getDatabase();

            // Fill profile form
            document.getElementById('name').value = currentUser.name || '';
            document.getElementById('email').value = currentUser.email || '';
            document.getElementById('phone').value = currentUser.phone || '';

            // تحميل إعدادات الإشعارات إذا كانت موجودة
            if (currentUser.notificationSettings) {
                // إعدادات إشعارات الطالب
                if (currentUser.notificationSettings.student) {
                    document.getElementById('order-notifications').checked =
                        currentUser.notificationSettings.student.orderNotifications !== false;
                    document.getElementById('balance-notifications').checked =
                        currentUser.notificationSettings.student.balanceNotifications !== false;
                    document.getElementById('status-notifications').checked =
                        currentUser.notificationSettings.student.statusNotifications !== false;
                }

                // إعدادات إشعارات ولي الأمر
                if (currentUser.notificationSettings.parent) {
                    document.getElementById('parent-purchase-notifications').checked =
                        currentUser.notificationSettings.parent.purchaseNotifications !== false;
                    document.getElementById('parent-balance-notifications').checked =
                        currentUser.notificationSettings.parent.balanceNotifications !== false;
                    document.getElementById('parent-login-notifications').checked =
                        currentUser.notificationSettings.parent.loginNotifications === true;
                    document.getElementById('parent-daily-summary').checked =
                        currentUser.notificationSettings.parent.dailySummary !== false;
                }
            }

            // Profile form submission
            document.getElementById('profile-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                const phone = document.getElementById('phone').value;

                // Update user in database
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].name = name;
                    db.users[userIndex].email = email;
                    db.users[userIndex].phone = phone;

                    // Update current user in session
                    currentUser.name = name;
                    currentUser.email = email;
                    currentUser.phone = phone;
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                    // Update user name display
                    document.getElementById('user-name').innerHTML = 'مرحبًا، <strong>' + name + '</strong>';

                    saveDatabase(db);
                    alert('تم حفظ المعلومات الشخصية بنجاح!');
                }
            });

            // Password form submission
            document.getElementById('password-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                // Check if current password is correct
                if (currentPassword !== currentUser.password) {
                    alert('كلمة المرور الحالية غير صحيحة!');
                    return;
                }

                // Check if new passwords match
                if (newPassword !== confirmPassword) {
                    alert('كلمة المرور الجديدة وتأكيدها غير متطابقين!');
                    return;
                }

                // Update password in database
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    db.users[userIndex].password = newPassword;

                    // Update current user in session
                    currentUser.password = newPassword;
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                    saveDatabase(db);
                    alert('تم تغيير كلمة المرور بنجاح!');

                    // Reset form
                    document.getElementById('password-form').reset();
                }
            });

            // Save notification settings
            document.getElementById('save-notifications').addEventListener('click', function() {
                // إعدادات إشعارات الطالب
                const orderNotifications = document.getElementById('order-notifications').checked;
                const balanceNotifications = document.getElementById('balance-notifications').checked;
                const statusNotifications = document.getElementById('status-notifications').checked;

                // إعدادات إشعارات ولي الأمر
                const parentPurchaseNotifications = document.getElementById('parent-purchase-notifications').checked;
                const parentBalanceNotifications = document.getElementById('parent-balance-notifications').checked;
                const parentLoginNotifications = document.getElementById('parent-login-notifications').checked;
                const parentDailySummary = document.getElementById('parent-daily-summary').checked;

                // تحديث إعدادات الإشعارات في قاعدة البيانات
                const userIndex = db.users.findIndex(u => u.id === currentUser.id);
                if (userIndex !== -1) {
                    // إنشاء أو تحديث كائن إعدادات الإشعارات
                    if (!db.users[userIndex].notificationSettings) {
                        db.users[userIndex].notificationSettings = {};
                    }

                    // حفظ إعدادات إشعارات الطالب
                    db.users[userIndex].notificationSettings.student = {
                        orderNotifications,
                        balanceNotifications,
                        statusNotifications
                    };

                    // حفظ إعدادات إشعارات ولي الأمر
                    db.users[userIndex].notificationSettings.parent = {
                        purchaseNotifications: parentPurchaseNotifications,
                        balanceNotifications: parentBalanceNotifications,
                        loginNotifications: parentLoginNotifications,
                        dailySummary: parentDailySummary
                    };

                    // تحديث المستخدم الحالي في الجلسة
                    currentUser.notificationSettings = db.users[userIndex].notificationSettings;
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

                    // حفظ التغييرات في قاعدة البيانات
                    saveDatabase(db);

                    alert('تم حفظ إعدادات الإشعارات بنجاح!');
                }
            });

            // Populate theme options
            const themeOptions = document.getElementById('theme-options');
            const currentThemeId = getCurrentTheme();

            db.settings.themes.forEach(theme => {
                const themeOption = document.createElement('div');
                themeOption.className = 'theme-option';
                themeOption.setAttribute('data-id', theme.id);

                // Set active theme
                if (theme.id === currentThemeId) {
                    themeOption.classList.add('active');
                }

                themeOption.innerHTML = `
                    <div class="theme-preview">
                        <div class="theme-header" style="background-color: ${theme.primaryColor};">
                            العنوان
                        </div>
                        <div class="theme-body">
                            <div class="theme-sidebar" style="background-color: ${theme.darkColor};"></div>
                            <div class="theme-content">
                                <div style="margin-bottom: 10px; height: 20px; width: 70%; background-color: #ddd; border-radius: 3px;"></div>
                                <div style="height: 10px; width: 90%; background-color: #ddd; border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="theme-name">${theme.name}</div>
                `;

                themeOptions.appendChild(themeOption);
            });

            // Theme selection
            document.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.theme-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                });
            });

            // Apply theme
            document.getElementById('apply-theme').addEventListener('click', function() {
                const selectedTheme = document.querySelector('.theme-option.active');
                if (selectedTheme) {
                    const themeId = parseInt(selectedTheme.getAttribute('data-id'));
                    if (applyTheme(themeId)) {
                        alert('تم تطبيق المظهر بنجاح!');
                    }
                }
            });

            // Logout button - Sidebar
            document.getElementById('logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });

            // Logout button - Header
            document.getElementById('header-logout-btn').addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('currentUser');
                    window.location.href = '../auth/login.html';
                }
            });
        });
    </script>
</body>
</html>
