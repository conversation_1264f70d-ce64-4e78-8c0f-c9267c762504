/**
 * فواصل النجاح للخدمات الإعاشة - نظام إدارة المقصف المدرسي
 * أنماط صفحة التخصيص
 */

/* الحاوية الرئيسية */
.customization-container {
    max-width: 1200px;
    margin: 50px auto;
    padding: 30px;
    background-color: var(--background-color, #fff);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* رأس الصفحة */
.customization-header {
    text-align: center;
    margin-bottom: 40px;
}

.customization-header h2 {
    font-size: 2.2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.customization-header h2::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    border-radius: 3px;
}

.customization-header p {
    color: var(--text-color, #666);
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

/* علامات التبويب */
.customization-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.tab-button {
    padding: 12px 25px;
    margin: 0 10px;
    background-color: transparent;
    border: none;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color, #666);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: var(--primary-color);
    background-color: rgba(46, 125, 50, 0.05);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

/* محتوى التبويب */
.tab-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-content.active {
    display: block;
}

/* قسم الألوان */
.colors-section {
    margin-bottom: 40px;
}

.colors-section h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.color-pickers {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.color-picker {
    margin-bottom: 20px;
}

.color-picker label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.color-picker input[type="color"] {
    width: 100%;
    height: 50px;
    border: 2px solid #eee;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-picker input[type="color"]:hover {
    border-color: var(--primary-color);
}

.color-picker .color-value {
    display: block;
    margin-top: 5px;
    font-size: 0.9rem;
    color: #666;
}

/* قسم الخطوط */
.fonts-section {
    margin-bottom: 40px;
}

.fonts-section h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.font-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.font-option {
    margin-bottom: 20px;
}

.font-option label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.font-option select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #eee;
    border-radius: 10px;
    font-size: 1rem;
    color: var(--text-color, #333);
    background-color: var(--background-color, #fff);
    transition: all 0.3s ease;
}

.font-option select:hover {
    border-color: var(--primary-color);
}

.font-option select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
    outline: none;
}

.font-preview {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 10px;
    background-color: var(--background-color, #fff);
}

.font-preview h4 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.font-preview-text {
    margin-bottom: 10px;
}

/* قسم التأثيرات الحركية */
.animations-section {
    margin-bottom: 40px;
}

.animations-section h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.animation-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.animation-option {
    margin-bottom: 20px;
}

.animation-option label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.animation-toggle {
    display: flex;
    align-items: center;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-right: 10px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 1rem;
    color: var(--text-color, #333);
}

.animation-speed {
    margin-top: 20px;
}

.animation-speed label {
    display: block;
    margin-bottom: 10px;
}

.speed-slider {
    width: 100%;
    height: 10px;
    border-radius: 5px;
    background: #eee;
    outline: none;
    -webkit-appearance: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.speed-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.speed-value {
    display: block;
    text-align: center;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #666;
}

/* قسم السمات */
.themes-section {
    margin-bottom: 40px;
}

.themes-section h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.themes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.theme-card {
    border: 2px solid transparent;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.theme-card.active {
    border-color: var(--primary-color);
}

.theme-preview {
    height: 150px;
    display: flex;
    flex-direction: column;
}

.theme-header {
    height: 30%;
    display: flex;
    align-items: center;
    padding: 0 15px;
    color: white;
    font-weight: 500;
}

.theme-body {
    height: 70%;
    display: flex;
}

.theme-sidebar {
    width: 30%;
    height: 100%;
}

.theme-content {
    width: 70%;
    height: 100%;
    background-color: #f5f5f5;
    padding: 10px;
}

.theme-content .card {
    background-color: white;
    height: 40px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.theme-name {
    text-align: center;
    padding: 15px;
    font-weight: 500;
    font-size: 1.1rem;
    border-top: 1px solid #eee;
    color: var(--text-color, #333);
}

/* أزرار الإجراءات */
.actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
}

.action-btn {
    padding: 12px 30px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn i {
    margin-left: 10px;
}

.action-btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

.action-btn-primary:hover {
    background-color: var(--dark-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
}

.action-btn-secondary {
    background-color: #f5f5f5;
    color: var(--text-color, #333);
    border: 1px solid #ddd;
}

.action-btn-secondary:hover {
    background-color: #eee;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تجاوب الموقع */
@media (max-width: 768px) {
    .customization-container {
        padding: 20px;
        margin: 20px;
    }
    
    .customization-tabs {
        flex-wrap: wrap;
    }
    
    .tab-button {
        margin: 5px;
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .color-pickers,
    .font-options,
    .animation-options,
    .themes-grid {
        grid-template-columns: 1fr;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
